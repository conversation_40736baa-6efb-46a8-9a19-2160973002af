//
//  ApiManager.swift
//  MyApp
//
//  Created by hans<PERSON><PERSON> on 2020/3/9.
//

import Foundation
import SVProgressHUD
import Alamofire

//1.
/// リクエスト成功：データを返す
typealias successBlock = (_ success:Data) -> Void
/// リクエスト失敗：エラーを返す
typealias faildBlock = (_ faild:Error) -> Void
/// トークン認証期限が切れた：トークンレフレッシュ
typealias tokenRefreshBlock = (_ tokenTimeOut:String) -> Void
/// トークン認証失敗：ログイン画面に戻せる
//typealias backToLoginBlock = (_ toLogin:String) -> Void
/// リフレッシュトークンのメソッド専用　成功：取れたデータを返す
typealias refreshTokenSuccessBlock = (_ refreshTokenSuccess: [String: Any]) -> Void
/// リフレッシュトークンのメソッド専用　失敗：エラを返す
typealias refreshTokenFaildBlock = (_ refreshTokenFaild:Error) -> Void

/// トークン
typealias tokenInvalidBlock = (_ tokenInvalid:String) -> Void
typealias toInsertAssetBlock = (_ toInsertAsset:String) -> Void
let MOBILE_VALUE = "31d94e42d6614027848c3f2626e58228"
let MOBILE_KEY = "MOBILE_SPECIAL_HEADER"

class ApiManager:NSObject, NetworkStatusProtocol {
    //create single instance
    static let shared = ApiManager()
    private override init() {}
    //2.
    /// リクエストが成功
    var success:successBlock?
    /// リクエストが失敗
    var faild:faildBlock?
    /// トークンが失効
    var tokenTimeOut:tokenRefreshBlock?
    /// トークンが認証失敗のため、強制的にログイン画面に戻す
    //    var toLogin:backToLoginBlock?
    /// トークンリフレッシュ成功
    var refreshTokenSuccess:refreshTokenSuccessBlock?
    /// トークンリフレッシュ失敗
    var refreshTokenFaild:refreshTokenFaildBlock?
    /// トークンが認証失敗、強制的にログイン画面に戻せる
    var tokenInvalid:tokenInvalidBlock?
    /// バーコード利用可能で資産登録へ進む
    var toInsertAsset:toInsertAssetBlock?
    // Loading请求计数器
    var _loadingCount = 0
    var loadingCount: Int{
        set {
            if 0 >= newValue {
                _loadingCount = 0
                DispatchQueue.main.async {
                    SVProgressHUD.dismiss()
                }
            } else {
                _loadingCount = newValue
                if !SVProgressHUD.isVisible() {
                    DispatchQueue.main.async {
                        SVProgressHUD.setDefaultMaskType(SVProgressHUDMaskType.black)
                        SVProgressHUD.show(withStatus: "スキャン中・・・")
                    }
                }
            }
        }
        
        get {
            _loadingCount
        }
    }
    
    
    func post(url: String, json: Any, success: @escaping successBlock, faild: @escaping faildBlock,name:String,password:String) {
        self.success = success
        self.faild = faild
        let session = URLSession(configuration: .default)
        let url = "https://api.dev.asset-force.com/secure/cognito/signIn"
        var request = URLRequest(url: URL(string: url)!)
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.httpMethod = "POST"
        let postData = ["userName":"\(name)","password":"\(password)"]
        let postString = postData.compactMap({ (key, value) -> String in
            return "\(key)=\(value)"
        }).joined(separator: "&")
        request.httpBody = postString.data(using: .utf8)
        let task = session.dataTask(with: request) {(data, response, error) in
            do{
                if let jsonObj: [String: Any] = try JSONSerialization.jsonObject(with: data!, options: JSONSerialization.ReadingOptions()) as? [String : Any]{
                    DispatchQueue.main.async{
                        DEBUGLOG.NSLog(message: jsonObj)
                        let mapTokeyDic = jsonObj["mapToken"] as! [String : Any]
                        SecureUserDefaults.shared.secureDefaults.set(mapTokeyDic["accessToken"], forKey: "accessToken")
                        SecureUserDefaults.shared.secureDefaults.set(mapTokeyDic["refreshToken"], forKey: "refreshToken")
                        SecureUserDefaults.shared.secureDefaults.set(name, forKey: USER_NAME)
                        if let userList = jsonObj["userList"], userList is Array<[String : Any]> {
                            let userDic: [String : Any] = (userList as! Array<[String : Any]>).first ?? [:]
                            SecureUserDefaults.shared.secureDefaults.set(userDic["userId"], forKey: "userId")
                            SecureUserDefaults.shared.secureDefaults.set("\(userDic["lastName"] ?? "")\(userDic["firstName"] ?? "")", forKey: "name_user")
                        }
                        if let mapToken = (jsonObj)[MAP_TOKEN] {
                            if (mapToken as! [String : Any])[ACCESS_TOKEN] != nil {
                                SecureUserDefaults.shared.secureDefaults.set((mapToken as! [String : Any])[ACCESS_TOKEN], forKey: TOKEN)
                                SecureUserDefaults.shared.secureDefaults.synchronize()
                            }
                            
                            if (mapToken as! [String : Any])[REFRESH_TOKEN] != nil {
                                SecureUserDefaults.shared.secureDefaults.set((mapToken as! [String : Any])[REFRESH_TOKEN], forKey: REFRESH_TOKEN)
                            }
                        }
                        
                        
                        if jsonObj[USER_LIST] != nil {
                            
                            let userList: Array<[String: Any]> = jsonObj[USER_LIST] as? Array<[String : Any]> ?? []
                            let userDic = userList.first
                            let userId = String(format: "%@", userDic?[USER_ID] as! CVarArg)
                            SecureUserDefaults.shared.secureDefaults.set(userId, forKey: USER_ID)
                            DEBUGLOG.NSLog(message: jsonObj[USER_LIST])
                            if userList.count != 0 && userDic?.keys.count != 0 {
                                if let firstName = userDic?["firstName"], let lastName = userDic?["lastName"] {
                                    SecureUserDefaults.shared.secureDefaults.set((lastName as! String) + " " + (firstName as! String), forKey: "registerName")
                                }
                            }
                            
                            let tenantId: String = userDic?[TENANT_ID] as? String ?? ""
                            SingleInstance.share.tenantID = tenantId
                            SecureUserDefaults.shared.secureDefaults.set(tenantId, forKey: TENANT_ID)
                            SecureUserDefaults.shared.secureDefaults.synchronize()
                            
                            SingleInstance.shareList.userGroup = Set<String>()
                            SingleInstance.shareList.userGroup = []
                        }
                        SVProgressHUD.dismiss()
                        if error == nil {
                            if data != nil && data?.isEmpty != true {
                                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                                
                                if let statusCode = resultDic["code"] {
                                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                                    }
                                }
                            }
                            
                            if self.success != nil {
                                self.success!(data!)
                            }
                        }
                    }
                }
            } catch{
                DEBUGLOG.NSLog(message: "Error.")
                self.faild!(error)
            }
        }
        task.resume()
    }
    
    /// GETリクエス共通
    ///
    /// - Parameters:
    ///   - urlStr: urlStr description
    ///   - param: GETだけ対応
    ///   - method: method description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func request(urlStr: String, param: Parameters, method: String, controller: UIViewController, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        let url:NSURL = NSURL.init(string: encodeUrlString)!
        let request:NSMutableURLRequest = NSMutableURLRequest.init(url: url as URL)
        request.httpMethod = method
        request.timeoutInterval = 10.0
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue(MOBILE_VALUE, forHTTPHeaderField: MOBILE_KEY)
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        }
        
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with:request as URLRequest) {
            (data, response, err) in
            semaphore.signal()
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
            }
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    DispatchQueue.main.async {
                        self.netErrAlert(controller)
                    }
                    return
                }
            }
            
            if(err == nil && data?.isEmpty == false){
                let resultDic: [String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && ((resultDic["code"] as! Int) == 99 || (resultDic["code"] as! Int) == 1) {
                    DispatchQueue.main.async {
                        Utils.systemError(resultDic: resultDic, controller: controller)
                    }
                    
                    return
                }
                if self.success != nil {
                    self.success!(data!)
                }
            } else {
                semaphore.signal()
                self.netErrAlert(controller)
                if self.faild != nil {
                    self.faild!(err!)
                }
            }
            
        }
        dataTask.resume()
    }

    func doPrint(param: Parameters, controller: UIViewController, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + "/secure/Asset/mobile/getPrintTemplateMappingAndAssetInfo"
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        let url:NSURL = NSURL.init(string: encodeUrlString)!
        let request:NSMutableURLRequest = NSMutableURLRequest.init(url: url as URL)
        request.httpMethod = "POST"
        request.timeoutInterval = 10.0
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue(MOBILE_VALUE, forHTTPHeaderField: MOBILE_KEY)
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        }
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: param, options: .prettyPrinted)// pass dictionary to data object and set it as
        } catch let error {
            DEBUGLOG.NSLog(message: error.localizedDescription)
        }
        
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with:request as URLRequest) {
            (data, response, err) in
            semaphore.signal()
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
            }
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    DispatchQueue.main.async {
                        self.netErrAlert(controller)
                    }
                    return
                }
            }
            
            if(err == nil && data?.isEmpty == false){
                let resultDic: [String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && ((resultDic["code"] as! Int) == 99 || (resultDic["code"] as! Int) == 1) {
                    DispatchQueue.main.async {
                        Utils.systemError(resultDic: resultDic, controller: controller)
                    }
                    
                    return
                }
                if self.success != nil {
                    self.success!(data!)
                }
            } else {
                semaphore.signal()
                self.netErrAlert(controller)
                if self.faild != nil {
                    self.faild!(err!)
                }
            }
            
        }
        dataTask.resume()
    }
   
    func getToken(url: String, json: Any, success: @escaping successBlock, faild: @escaping faildBlock) {
        
        let api = SingleInstance.api.apiHost + "/secure/cognito/signIn"
        self.success = success
        self.faild = faild
        let urlString:String = api
        let url = URL(string:urlString)
        if url == nil {
            DEBUGLOG.NSLog(message:"url is nil -------")
            return
        }
        var request = URLRequest(url: url!)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        let param = ["userName":SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME), "password":"Asdqwe123#"]
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: param, options: .prettyPrinted)// pass dictionary to data object and set it as
        } catch let error {
            DEBUGLOG.NSLog(message: error.localizedDescription)
        }
        let session = URLSession.shared
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with: request, completionHandler: {(data, response, error) -> Void in
            
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
            }
            if error == nil {
                if data != nil && data?.isEmpty != true {
                    let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                    
                    if let statusCode = resultDic["code"] {
                        if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                            NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                        }
                    }
                }
                
                if self.success != nil {
                    self.success!(data!)
                }
            }else {
                if self.faild != nil {
                    self.faild!(error!)
                }
                
                DEBUGLOG.NSLog(message:error!)
            }
            semaphore.signal()
        }) as URLSessionTask
        dataTask.resume()
        
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
    }
    
    /// リフレッシュトークン
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func refreshToken(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) ?? ""
        var token: String = SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) ?? ""
        var userId: String = SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) ?? ""
        var tenantId: String = SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) ?? ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let headers: HTTPHeaders = [
            "device": "iphone",
            "pageId": "index",
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + "/secure/cognito/refreshToken", method: .post, parameters: ["userName":userName, "refreshToken":SingleInstance.api.refreshToken], headers: headers).responseJSON { response in
            if response.result.isSuccess {
                let resultDic = response.result.value as? [String: Any] ?? [:]
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) != 0 {
                    return
                }
                if let mapToken: [String: Any] = resultDic[MAP_TOKEN] as? [String : Any] {
                    if mapToken[ACCESS_TOKEN] != nil {
                        SecureUserDefaults.shared.secureDefaults.set((mapToken)[ACCESS_TOKEN], forKey: TOKEN)
                        SecureUserDefaults.shared.secureDefaults.synchronize()
                    }
                    if mapToken[REFRESH_TOKEN] != nil {
                        SecureUserDefaults.shared.secureDefaults.set((mapToken)[REFRESH_TOKEN], forKey: REFRESH_TOKEN)
                    }
                }
                success(resultDic)
            }else{
                DEBUGLOG.NSLog(message: "-------------🚀refreshToken  failed--------------")
                fail(response.result.error)
            }
        }
    }
    
    
    /// 获取barcode资产情报
    /// - Parameters:
    ///   - url: url
    ///   - barcode: 識別コード
    ///   - location: 场所信息
    ///   - scanFrom: 扫描场景 workflow、action、asset
    ///   - searchId: 过滤id
    ///   - workflowId: workflowid
    ///   - engineId: wf engineId
    ///   - assetActionId: 处理设定用
    ///   - controller: 视图控制器
    ///   - success: 成功回调函数
    ///   - faild: 失败回调函数
    func getAssetInfoByBarcode(url: String, barcode: String, location: String?, scanFrom: String, searchId: String, workflowId: Int, engineId: String, assetActionId: String, controller: UIViewController, success: @escaping successBlock, faild: @escaping faildBlock) {
        let requestUrlStr = SingleInstance.api.apiHost + url
        guard let requestUrl = URL(string: requestUrlStr) else {
            DEBUGLOG.NSLog(message: "url is nil")
            DispatchQueue.main.async {
                faild(NSError(domain: "URLError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"]))
            }
            return
        }
        
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        var request = URLRequest(url: requestUrl)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue(gps.encodeURIComponent(), forHTTPHeaderField: "gps")
        request.timeoutInterval = 30.0
        
        // Add authentication headers
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        
        // Build request parameters
        var postData = [
            "barCode": barcode,
            "scanFrom": scanFrom
        ]
        
        if let location = location, location != "null" {
            postData["location"] = location
        }
        
        postData["searchId"] = searchId
        
        if scanFrom == "workflow" {
            postData["workflowId"] = String(workflowId)
            postData["engineId"] = engineId
        } else if scanFrom == "action" {
            postData["assetActionId"] = assetActionId
        }
        
        // Convert parameters to URL-encoded string
        let postString = postData.map { key, value in
            return "\(key)=\(value)"
        }.joined(separator: "&")
        
        request.httpBody = postString.data(using: .utf8)
        
        let session = URLSession.shared
        let dataTask = session.dataTask(with: request) { [weak self] (data, response, error) in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                if let error = error {
                    self.netErrAlert(controller)
                    faild(error)
                    DEBUGLOG.NSLog(message: "Network error: \(error.localizedDescription)")
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    self.netErrAlert(controller)
                    faild(NSError(domain: "HTTPError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid HTTP response"]))
                    return
                }
                
                guard httpResponse.statusCode == 200 else {
                    self.netErrAlert(controller)
                    faild(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP status code: \(httpResponse.statusCode)"]))
                    return
                }
                
                guard let data = data, !data.isEmpty else {
                    self.netErrAlert(controller)
                    faild(NSError(domain: "DataError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Empty response data"]))
                    return
                }
                
                // Process response data
                if let resultDic = Utils.nsdataToJSON(data: data) {
                    // Check for token expiration
                    if let statusCode = resultDic["code"] {
                        if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                            NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                            return
                        }
                    }
                    
                    // Check for error code
                    if let code = resultDic["code"] as? Int, code == 99 {
                        Utils.systemError(resultDic: resultDic, controller: controller)
                        return
                    }
                }
                
                success(data)
            }
        }
        
        dataTask.resume()
    }
    
    
    /// バーコードで資産情報をとる
    ///
    /// - Parameters:
    ///   - barcode: barcode description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func getAssetInfoByBarcode(barcode: String, urlStr: String, method: String = "GET", param: [String:Any] = [:], controller: UIViewController, success: @escaping successBlock, faild: @escaping faildBlock) {
        //         self.success = success
        //         self.faild = faild
        let tempUrlStr:String = SingleInstance.api.apiHost + urlStr
        
        let tempUrl = NSURL.init(string: tempUrlStr) ?? nil
        if tempUrl == nil {
            DEBUGLOG.NSLog(message:"url is nil -------")
            return
        }
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        var request = URLRequest(url: tempUrl! as URL)
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue(gps.encodeURIComponent(), forHTTPHeaderField: "gps")
        request.timeoutInterval = 30.0
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        request.httpMethod = method
        if method == "POST" {
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: param, options: .prettyPrinted)// pass dictionary to data object and set it as
            } catch let error {
                DEBUGLOG.NSLog(message: error.localizedDescription)
            }
        }
        
        let session = URLSession.shared
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with: request, completionHandler: {(data, response, error) -> Void in
            
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    semaphore.signal()
                    DispatchQueue.main.async {
                        self.netErrAlert(controller)
                    }
                }
            }
            if error == nil {
                if data?.isEmpty == true {
                    semaphore.signal()
                    DispatchQueue.main.async {
                        self.netErrAlert(controller)
                    }
                }
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                // token expired
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    semaphore.signal()
                    Utils.systemError(resultDic: resultDic, controller: controller)
                    return
                }
                success(data!)
            }else {
                semaphore.signal()
                self.netErrAlert(controller)
                
                faild(error!)
                
                DEBUGLOG.NSLog(message:error!)
            }
            semaphore.signal()
        }) as URLSessionTask
        dataTask.resume()
        semaphore.wait()
    }
        
    
    /// 関連情報をとる
    ///
    /// - Parameters:
    ///   - barcode: barcode description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func requestForSynchronize(urlStr: String, param: [String: Any], method: String, controller: UIViewController, success: @escaping successBlock,  faild: @escaping faildBlock) {
        //        self.success = success
        //        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        var request: URLRequest?
        if let url = URL(string: encodeUrlString) {
            request = URLRequest(url: url)
        }

        if request == nil {
            return
        }
        
        //        var request = URLRequest(url: url!)
        request!.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request!.setValue("iphone", forHTTPHeaderField: "device")
        request!.setValue("index", forHTTPHeaderField: "pageId")
        request!.httpMethod = method
        request!.timeoutInterval = 10.0
        request!.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        request!.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        request!.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        request!.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        
        //        let session = URLSession.shared
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with: request!, completionHandler: {(data, response, error) -> Void in
            
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    semaphore.signal()
                    self.netErrAlert(controller)
                }
            }
            if error == nil {
                if data?.isEmpty == true {
                    semaphore.signal()
                    self.netErrAlert(controller)
                }
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    semaphore.signal()
                    Utils.systemError(resultDic: resultDic, controller: controller)
                    return
                }
                success(data!)
            }else {
                semaphore.signal()
                self.netErrAlert(controller)
                
                faild(error!)
                
                DEBUGLOG.NSLog(message:error!)
            }
            semaphore.signal()
        }) as URLSessionTask
        dataTask.resume()
        
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
        
    }
    
    
    
    /// 関連情報をとる
    ///
    /// - Parameters:
    ///   - barcode: barcode description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func requestForSynchronizeJson(urlStr: String, param: [String: Any], method: String, controller: UIViewController?, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let url = URL(string:urlString)
        if url == nil {
            DEBUGLOG.NSLog(message:"url is nil -------")
            return
        }
        
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        var request = URLRequest(url: url!)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue(gps.encodeURIComponent(), forHTTPHeaderField: "gps")
        request.timeoutInterval = 3.0
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        let session = URLSession.shared
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with: request, completionHandler: {(data, response, error) -> Void in
            
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    semaphore.signal()
                    if controller != nil {
                        self.netErrAlert(controller!)
                    }
                }
            }
            if error == nil {
                if data?.isEmpty == true {
                    semaphore.signal()
                    if controller != nil {
                        self.netErrAlert(controller!)
                    }
                }
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    semaphore.signal()
                    if controller != nil {
                        Utils.systemError(resultDic: resultDic, controller: controller!)
                    }
                    return
                }
                if self.success != nil && data != nil {
                    self.assetMobileSettingForMobile(resultDic: resultDic, successBlock: self.success!, urlStr: urlStr, data: data!)
                }
            }else {
                semaphore.signal()
                if controller != nil {
                    self.netErrAlert(controller!)
                }
                
                if self.faild != nil {
                    self.faild!(error!)
                }
                
                DEBUGLOG.NSLog(message:error!)
            }
            semaphore.signal()
        }) as URLSessionTask
        dataTask.resume()
        
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
        
    }
    // AR表示設定にある項目がレイアウト設定から全て外す場合は、
    func assetMobileSettingForMobile(resultDic: [String: Any], successBlock:successBlock, urlStr: String, data: Data) {
        if urlStr.range(of: "AssetMobileSetting/getAssetInfoForMobile") != nil {
            if resultDic["assetMobileSettingForMobile"] is [String: Any] && resultDic["assetMobileSettingForMobile"] != nil {
                let assetMobileSettingData = (resultDic["assetMobileSettingForMobile"] as! [String: Any])["assetMobileSetting"] as? [String: Any] ?? [:]
                if assetMobileSettingData["arLevel1ItemId"] is NSNull,
                   assetMobileSettingData["arLevel2ItemId"] is NSNull,
                   assetMobileSettingData["arLevel3ItemId"] is NSNull,
                   assetMobileSettingData["arLevel1SubItemId"] is NSNull,
                   assetMobileSettingData["arLevel2SubItemId"] is NSNull,
                   assetMobileSettingData["arLevel3SubItemId"] is NSNull {
                    let dic: [String: Any?] = ["msg": "成功","code": 0,"assetMobileSettingForMobile": nil]
                    let string = Utils.getJSONStringFromDictionary(dict: dic)
                    if let dataNull = string.data(using: String.Encoding.utf8, allowLossyConversion: false) {
                        successBlock(dataNull)
                    }
                }else{
                    successBlock(data)
                }
            }
        }else {
            successBlock(data)
        }
    }
    
    /// turl取得
    ///
    /// - Parameters:
    ///   - urlStr: urlStr description
    ///   - param: GETだけ対応
    ///   - method: method description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func requestGetTurl(urlStr: String, param: Parameters, method: String, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        let url:NSURL = NSURL.init(string: encodeUrlString)!
        let request:NSMutableURLRequest = NSMutableURLRequest.init(url: url as URL)
        request.httpMethod = method
        request.timeoutInterval = 10.0
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue("mobileKey", forHTTPHeaderField: "mobileValue")
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        }
        
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with:request as URLRequest) {
            (data, response, err) in
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    semaphore.signal()
                    return
                }
            }
            
            if(err == nil && data?.isEmpty == false){
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    semaphore.signal()
                    return
                }
                if self.success != nil {
                    self.success!(data!)
                }
            } else {
                if self.faild != nil {
                    self.faild!(err!)
                }
            }
            semaphore.signal()
        }  as URLSessionTask
        dataTask.resume()
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
    }
    
    /// turl取得
    ///
    /// - Parameters:
    ///   - urlStr: urlStr description
    ///   - param: GETだけ対応
    ///   - method: method description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func requestGetTurlAsync(urlStr: String, param: Parameters, method: String, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        let url:NSURL = NSURL.init(string: encodeUrlString)!
        let request:NSMutableURLRequest = NSMutableURLRequest.init(url: url as URL)
        request.httpMethod = method
        request.timeoutInterval = 10.0
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue("mobileKey", forHTTPHeaderField: "mobileValue")
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        }
        
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let dataTask = session.dataTask(with:request as URLRequest) {
            (data, response, err) in
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    return
                }
            }
            
            if(err == nil && data?.isEmpty == false){
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    return
                }
                if self.success != nil {
                    self.success!(data!)
                }
            } else {
                if self.faild != nil {
                    self.faild!(err!)
                }
            }
        }  as URLSessionTask
        dataTask.resume()
    }
    
    
    /// GET画像URL
    ///
    /// - Parameters:
    ///   - urlStr: urlStr description
    ///   - param: GETだけ対応
    ///   - method: method description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func requestGetImageUrl(urlStr: String, param: Parameters, method: String, controller: UIViewController, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = SingleInstance.api.apiHost + urlStr
        let encodeUrlString: String = urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        let url:NSURL = NSURL.init(string: encodeUrlString)!
        let request:NSMutableURLRequest = NSMutableURLRequest.init(url: url as URL)
        request.httpMethod = method
        request.timeoutInterval = 10.0
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.setValue("iphone", forHTTPHeaderField: "device")
        request.setValue("index", forHTTPHeaderField: "pageId")
        request.setValue("mobileKey", forHTTPHeaderField: "mobileValue")
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String, forHTTPHeaderField: USER_NAME)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String, forHTTPHeaderField: TOKEN)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String, forHTTPHeaderField: USER_ID)
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            request.setValue(SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String, forHTTPHeaderField: TENANT_ID)
        }
        
        let configuration:URLSessionConfiguration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10.0
        let session:URLSession = URLSession(configuration: configuration)
        
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with:request as URLRequest) {
            (data, response, err) in
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
                if httpResponse.statusCode != 200 {
                    semaphore.signal()
                    return
                }
            }
            
            if(err == nil && data?.isEmpty == false){
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: data) ?? [:]
                
                if let statusCode = resultDic["code"] {
                    if (statusCode is Int && (statusCode as! Int) == 2) || (statusCode is String && (statusCode as! String) == "2") {
                        NotificationCenter.default.post(name: .NotificationTokenExpired, object: nil, userInfo: nil)
                    }
                }
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 99 {
                    semaphore.signal()
                    DispatchQueue.main.sync {
                        Utils.systemError(resultDic: resultDic, controller: controller)
                    }
                    return
                }
                if self.success != nil {
                    self.success!(data!)
                }
            } else {
                if self.faild != nil {
                    self.faild!(err!)
                }
            }
            semaphore.signal()
        }  as URLSessionTask
        dataTask.resume()
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
    }
    
    ///  画像アップロード
    ///
    /// - Parameters:
    ///   - barcode: barcode description
    ///   - controller: controller description
    ///   - success: success description
    ///   - tokenTimeOut: tokenTimeOut description
    ///   - tokenInvalid: tokenInvalid description
    ///   - faild: faild description
    func updateImage(url: String, image: UIImage, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = url
        let url = URL(string:urlString)
        if url == nil {
            DEBUGLOG.NSLog(message:"url is nil -------")
            return
        }
        var request = URLRequest(url: url!)
        request.httpMethod = "PUT"
        
        var data = Data()
        if image.jpeg(.lowest) == nil {
            return
        }
        data.append(image.jpeg(.lowest)!)
        
        
        request.httpBody = data
        let session = URLSession.shared
        let semaphore = DispatchSemaphore(value: 0)
        
        let dataTask = session.dataTask(with: request, completionHandler: {(data, response, error) -> Void in
            
            if let httpResponse = response as? HTTPURLResponse {
                DEBUGLOG.NSLog(message:httpResponse.statusCode)
            }
            if error == nil {
                
                if self.success != nil {
                    self.success!(data!)
                }
            }else {
                if self.faild != nil {
                    self.faild!(error!)
                }
                
                DEBUGLOG.NSLog(message:error!)
            }
            semaphore.signal()
        }) as URLSessionTask
        dataTask.resume()
        
        let result = semaphore.wait(timeout: DispatchTime.distantFuture)
        if(result == DispatchTimeoutResult.success)
        {
            DEBUGLOG.NSLog(message: "success")
        }else {
            DEBUGLOG.NSLog(message: "error")
        }
    }
    
    ///  添付ファイルアップロード
    ///   async upload.
    ///
    /// - Parameters:
    ///   - barcode: barcode description
    ///   - controller: controller description
    ///   - success: success description
    ///   - faild: faild description
    func uploadFile(url: String, fileURL: URL, progressCallback: ((Double) -> Void)? = nil, success: @escaping successBlock, faild: @escaping faildBlock) {
        self.success = success
        self.faild = faild
        let urlString:String = url
        guard let url = URL(string: urlString) else {
            DEBUGLOG.NSLog(message:"url is nil -------")
            return
        }
        
        let uploadRequest = Alamofire.upload(fileURL, to: url, method: .put)
        // 监听上传进度
        uploadRequest.uploadProgress { progress in
            // 更新进度条等 UI 元素
            DEBUGLOG.NSLog(message: progress)
            
            if let progressCallback = progressCallback {
                progressCallback(progress.fractionCompleted)
            }
        }

        // 上传成功后的回调
        uploadRequest.response { response in
            if let error = response.error {
                // 上传失败
                DEBUGLOG.NSLog(message: "Upload error")
                DEBUGLOG.NSLog(message: error)
                // 处理错误
                DEBUGLOG.NSLog(message: "Error: \(error)")
                if self.faild != nil {
                    self.faild!(error)
                }
            } else {
                // 上传成功
                DEBUGLOG.NSLog(message: "Upload successful")
                if self.success != nil {
                    self.success!(response.data ?? Data())
                }
            }
        }

        // 开始上传
        uploadRequest.resume()
    }
}


extension UIImage {
    enum JPEGQuality: CGFloat {
        case lowest  = 0
        case low     = 0.25
        case medium  = 0.5
        case high    = 0.75
        case highest = 1
    }
    func jpeg(_ jpegQuality: JPEGQuality) -> Data? {
        return jpegData(compressionQuality: jpegQuality.rawValue)
    }
}


/// HTTP POST 専用
struct ShareApiManager {
    let url: String
    let method: HTTPMethod
    let parameters: Parameters
    
    init(path: String, method: HTTPMethod = .get, parameters: Parameters = [:]) {
        url =  path
        self.method = method
        self.parameters = parameters
    }
    
    func request(showLoading: Bool = true, success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        if showLoading {
            ApiManager.shared.loadingCount += 1
        }
        let headers = headers()
        Alamofire.request(SingleInstance.api.apiHost + url, method: self.method, parameters: self.parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                if showLoading {
                    ApiManager.shared.loadingCount -= 1
                }
                let resultDic = response.result.value as! [String: Any]
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) == 2 {
                    ApiManager.shared.refreshToken(success: { (resultDic) in
                        
                    }) { (err) in
                        DEBUGLOG.NSLog(message: err)
                    }
                }
                success(response.result.value as! [String: Any])
            }else{
                if showLoading {
                    ApiManager.shared.loadingCount -= 1
                }
                
                fail(response.result.error)
                
            }
        }
    }
    
    func headers() -> HTTPHeaders {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        return [
            "Content-Type":"application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
    }
    
    /// POST専用
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func requestForPost(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        
        let headers: HTTPHeaders = [
            "Content-Type":"application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            "gps":gps.encodeURIComponent() ?? "get gps faild",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + url, method: .post, parameters: parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                success(response.result.value as? [String : Any] ?? [:])
            }else{
                fail(response.result.error)
            }
        }
    }
    
    /// POST専用　异步请求 网络请求不再等待信号量完成后继续，而是直接继续执行
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func requestForPostAsync(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        
        let headers: HTTPHeaders = [
            "Content-Type":"application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            "gps":gps.encodeURIComponent() ?? "get gps faild",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        let queue = DispatchQueue.global(qos: .utility)
        Alamofire.request(SingleInstance.api.apiHost + self.url, method: .post, parameters: self.parameters, headers: headers).response(queue: queue) { response in
            if response.error == nil && response.data != nil {
                let tempData = Utils.nsdataToJSON(data: response.data)
                if tempData == nil {
                    NSLog("tempData == nil")
                    fail(nil)
                    return
                }
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: response.data) ?? [:]
                success(resultDic)
            }else {
                fail(response.error)
            }
        }
    }
    
    /// POST専用　リクエストが終わらないと画面がリフレッシュができない
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func requestForPostSynchronize(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let gps = LocationManager.shared.addressInfo ?? "get gps faild"
        
        let headers: HTTPHeaders = [
            "Content-Type":"application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            "gps":gps.encodeURIComponent() ?? "get gps faild",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        let semaphore = DispatchSemaphore(value: 0)
        let queue     = DispatchQueue.global(qos: .userInitiated)
        
        
        Alamofire.request(SingleInstance.api.apiHost + self.url, method: .post, parameters: self.parameters, headers: headers).response(queue: queue) { response in
            
            if response.error == nil && response.data != nil {
                let tempData: [String: Any]? = Utils.nsdataToJSON(data: response.data)
                if tempData == nil {
                    semaphore.signal()
                    NSLog("tempData == nil")
                    return
                }
                let resultDic:[String: Any] = Utils.nsdataToJSON(data: response.data) ?? [:]
                success(resultDic)
                semaphore.signal()
            }else {
                fail(response.error)
                semaphore.signal()
            }
        }
        semaphore.wait()
    }
    
    /// PUT専用
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func requestForPut(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let headers: HTTPHeaders = [
            "Content-Type":"application/json",
            "device": "iphone",
            MOBILE_KEY:MOBILE_VALUE,
            "pageId": "appurtenances-information-detail",
            "Accept-Encoding": "gzip, deflate, br",
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + url, method: .post, parameters: parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                success(response.result.value as? [String: Any] ?? [:])
            }else{
                fail(response.result.error)
            }
        }
    }
    
    /// ログイン専用
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func login(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        let headers: HTTPHeaders = [
            "Content-Type": "application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            MOBILE_KEY:MOBILE_VALUE
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + url, method: .post, parameters: parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                success(response.result.value as? [String: Any] ?? [:])
            }else{
                fail(response.result.error)
            }
        }
    }
    
    /// リフレッシュトークン
    ///
    /// - Parameters:
    ///   - success: success description
    ///   - fail: fail description
    func refreshToken(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let headers: HTTPHeaders = [
            "device": "iphone",
            "pageId": "index",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + url, method: .post, parameters: parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                let resultDic = response.result.value as? [String: Any] ?? [:]
                if resultDic["code"] != nil && resultDic["code"] is Int && (resultDic["code"] as! Int) != 0 {
                    return
                }
                if let mapToken: [String : Any] = resultDic[MAP_TOKEN] as? [String: Any] {
                    if mapToken[ACCESS_TOKEN] != nil {
                        SecureUserDefaults.shared.secureDefaults.set(mapToken[ACCESS_TOKEN], forKey: TOKEN)
                        SecureUserDefaults.shared.secureDefaults.synchronize()
                    }
                    if mapToken[REFRESH_TOKEN] != nil {
                        SecureUserDefaults.shared.secureDefaults.set(mapToken[REFRESH_TOKEN], forKey: REFRESH_TOKEN)
                    }
                }
                success(resultDic)
            }else{
                fail(response.result.error)
                return
            }
        }
    }
    
    func requestForGet(success: @escaping (_ data: [String: Any])-> Void, fail: @escaping (_ error: Error?)-> Void) {
        ApiManager.shared.loadingCount += 1
        var userName:String = ""
        var token: String = ""
        var userId: String = ""
        var tenantId: String = ""
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) != nil {
            userName = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_NAME) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TOKEN) != nil {
            token = (SecureUserDefaults.shared.secureDefaults.object(forKey: TOKEN) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: USER_ID) != nil {
            userId = (SecureUserDefaults.shared.secureDefaults.object(forKey: USER_ID) as? String)!
        }
        if SecureUserDefaults.shared.secureDefaults.string(forKey: TENANT_ID) != nil {
            tenantId = (SecureUserDefaults.shared.secureDefaults.object(forKey: TENANT_ID) as? String)!
        }
        let headers: HTTPHeaders = [
            "Content-Type": "application/x-www-form-urlencoded",
            "device": "iphone",
            "pageId": "index",
            MOBILE_KEY:MOBILE_VALUE,
            USER_NAME: userName,
            TOKEN: token,
            USER_ID: userId,
            TENANT_ID: tenantId
        ]
        
        Alamofire.request(SingleInstance.api.apiHost + url, method: .get, parameters: parameters, headers: headers).responseJSON { response in
            if response.result.isSuccess {
                ApiManager.shared.loadingCount -= 1
                success(response.result.value as? [String : Any] ?? [:])
            }else{
                ApiManager.shared.loadingCount -= 1
                fail(response.result.error)
                
            }
        }
    }
    
}

/// 网络状态协议
protocol NetworkStatusProtocol {
    func isReachable() -> Bool
}

extension NetworkStatusProtocol {
    /// 返回一个布尔值,用于实时监测网络状态
    func isReachable() -> Bool {
        var res: Bool = false
        if let netManager = NetworkReachabilityManager() {
            if netManager.networkReachabilityStatus == .reachable(.wwan) || netManager.networkReachabilityStatus == .reachable(.ethernetOrWiFi) {
                res = true
            }
        }
        return res
    }
    
    func getNetErrMsg() -> String {
        var str = NO_NETWORK
        if isReachable() {
            str = MAINTENANCE
        }
        return str
    }
    
    func netErrAlert(_ controller: UIViewController) {
        let netErrMsg = self.getNetErrMsg()
        AlertManager.shareInstance.showAlertCancel(ALERT_TITLE, message: netErrMsg, cancelTitle: "OK", actionStyle: .default, controller: controller, cancelBlock: { (str) in
        })
    }
}
