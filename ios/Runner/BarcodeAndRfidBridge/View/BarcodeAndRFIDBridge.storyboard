<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Bridge Center Btn View Controller-->
        <scene sceneID="aRQ-F3-ZmD">
            <objects>
                <viewController storyboardIdentifier="BridgeCenterBtnViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="6dQ-Jo-J0H" customClass="BridgeCenterBtnViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="pkz-7n-16u">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="hpc-HW-KeC">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gXy-c9-Ozd">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="LFP-mv-7id"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vJf-nd-4sd">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OLh-uW-okE">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="6gj-UO-pLy"/>
                                            <constraint firstAttribute="height" constant="40" id="E7d-2E-UQ0"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="6dQ-Jo-J0H" eventType="touchUpInside" id="YV3-qC-FUw"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="OLh-uW-okE" firstAttribute="leading" secondItem="vJf-nd-4sd" secondAttribute="leading" constant="16" id="NnI-K6-Y17"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ZZa-Yz-GAg"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="hpc-HW-KeC" secondAttribute="trailing" id="10G-Bh-3Dd"/>
                            <constraint firstAttribute="bottom" secondItem="hpc-HW-KeC" secondAttribute="bottom" id="9P3-nU-sPO"/>
                            <constraint firstItem="hpc-HW-KeC" firstAttribute="leading" secondItem="pkz-7n-16u" secondAttribute="leading" id="BEN-m5-0vb"/>
                            <constraint firstItem="hpc-HW-KeC" firstAttribute="top" secondItem="pkz-7n-16u" secondAttribute="top" id="DDB-Bt-iZI"/>
                            <constraint firstItem="vJf-nd-4sd" firstAttribute="leading" secondItem="ZZa-Yz-GAg" secondAttribute="leading" id="HO2-3f-GfJ"/>
                            <constraint firstItem="ZZa-Yz-GAg" firstAttribute="trailing" secondItem="gXy-c9-Ozd" secondAttribute="trailing" id="PSf-dk-c7k"/>
                            <constraint firstItem="gXy-c9-Ozd" firstAttribute="top" secondItem="vJf-nd-4sd" secondAttribute="bottom" id="PXo-eH-EO8"/>
                            <constraint firstItem="OLh-uW-okE" firstAttribute="top" secondItem="ZZa-Yz-GAg" secondAttribute="top" constant="8" id="iMR-IF-J9y"/>
                            <constraint firstItem="ZZa-Yz-GAg" firstAttribute="trailing" secondItem="vJf-nd-4sd" secondAttribute="trailing" id="j2Y-m2-6Ie"/>
                            <constraint firstItem="vJf-nd-4sd" firstAttribute="top" secondItem="pkz-7n-16u" secondAttribute="top" id="oXx-9w-uPc"/>
                            <constraint firstItem="gXy-c9-Ozd" firstAttribute="leading" secondItem="ZZa-Yz-GAg" secondAttribute="leading" id="wWt-ve-W3f"/>
                            <constraint firstAttribute="bottom" secondItem="gXy-c9-Ozd" secondAttribute="bottom" id="zgR-XN-uho"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="vJf-nd-4sd" id="nNm-n6-Tcl"/>
                        <outlet property="bottomView" destination="gXy-c9-Ozd" id="fsA-de-p1K"/>
                        <outlet property="bottomViewHeight" destination="LFP-mv-7id" id="n1q-C1-iMH"/>
                        <outlet property="closeBtn" destination="OLh-uW-okE" id="YeY-fr-FYY"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="yIW-zP-VAs" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455" y="-367"/>
        </scene>
        <!--Bridge Action View Controller-->
        <scene sceneID="aiv-CY-RUe">
            <objects>
                <viewController storyboardIdentifier="BridgeActionViewController" id="l2P-jl-bo4" customClass="BridgeActionViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="L2O-To-3sa">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="f9c-Et-B8N">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="Smg-0n-2Sd">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OR9-eH-VqC">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="fyI-Bb-oWL"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tLe-sk-reV">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xgr-sT-F3f">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="P8K-XY-FGj"/>
                                            <constraint firstAttribute="width" constant="40" id="rPt-dA-nmb"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="l2P-jl-bo4" eventType="touchUpInside" id="saf-ap-ijn"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Xgr-sT-F3f" firstAttribute="leading" secondItem="tLe-sk-reV" secondAttribute="leading" constant="16" id="Svj-te-yPR"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="z2W-lF-WcC"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="z2W-lF-WcC" firstAttribute="trailing" secondItem="tLe-sk-reV" secondAttribute="trailing" id="9MG-lo-GKO"/>
                            <constraint firstItem="f9c-Et-B8N" firstAttribute="top" secondItem="L2O-To-3sa" secondAttribute="top" id="DUB-cj-yjz"/>
                            <constraint firstItem="f9c-Et-B8N" firstAttribute="leading" secondItem="L2O-To-3sa" secondAttribute="leading" id="GkW-vd-wyc"/>
                            <constraint firstItem="OR9-eH-VqC" firstAttribute="leading" secondItem="z2W-lF-WcC" secondAttribute="leading" id="Iav-jo-V2z"/>
                            <constraint firstAttribute="bottom" secondItem="Smg-0n-2Sd" secondAttribute="bottom" id="NnA-s5-bcM"/>
                            <constraint firstItem="f9c-Et-B8N" firstAttribute="bottom" secondItem="L2O-To-3sa" secondAttribute="bottom" id="Qj5-ik-lBv"/>
                            <constraint firstItem="OR9-eH-VqC" firstAttribute="top" secondItem="tLe-sk-reV" secondAttribute="bottom" id="YZf-wV-BS5"/>
                            <constraint firstItem="Xgr-sT-F3f" firstAttribute="top" secondItem="z2W-lF-WcC" secondAttribute="top" constant="8" id="Yvz-WU-sMF"/>
                            <constraint firstAttribute="bottom" secondItem="OR9-eH-VqC" secondAttribute="bottom" id="aem-9k-XY8"/>
                            <constraint firstItem="tLe-sk-reV" firstAttribute="top" secondItem="L2O-To-3sa" secondAttribute="top" id="eBZ-al-fLa"/>
                            <constraint firstItem="Smg-0n-2Sd" firstAttribute="leading" secondItem="L2O-To-3sa" secondAttribute="leading" id="gjd-oE-Ehx"/>
                            <constraint firstAttribute="trailing" secondItem="Smg-0n-2Sd" secondAttribute="trailing" id="gv1-s3-lba"/>
                            <constraint firstItem="z2W-lF-WcC" firstAttribute="trailing" secondItem="OR9-eH-VqC" secondAttribute="trailing" id="jWU-vY-Ysi"/>
                            <constraint firstItem="Smg-0n-2Sd" firstAttribute="top" secondItem="L2O-To-3sa" secondAttribute="top" id="o4c-uC-ef0"/>
                            <constraint firstItem="tLe-sk-reV" firstAttribute="leading" secondItem="z2W-lF-WcC" secondAttribute="leading" id="pg7-tG-gDb"/>
                            <constraint firstItem="f9c-Et-B8N" firstAttribute="trailing" secondItem="L2O-To-3sa" secondAttribute="trailing" id="uFq-D6-1mG"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="tLe-sk-reV" id="UOu-zF-zzx"/>
                        <outlet property="bottomView" destination="OR9-eH-VqC" id="iIv-GX-xxZ"/>
                        <outlet property="bottomViewHeight" destination="fyI-Bb-oWL" id="ueL-Ni-hSC"/>
                        <outlet property="closeBtn" destination="Xgr-sT-F3f" id="yu8-0X-doA"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Gdu-LB-6Zc" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="407" y="-368"/>
        </scene>
        <!--Bridge Work Flow View Controller-->
        <scene sceneID="p75-gZ-KO3">
            <objects>
                <viewController storyboardIdentifier="BridgeWorkFlowViewController" id="pjP-go-GE0" customClass="BridgeWorkFlowViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="dVw-vD-ghm">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="jbm-fI-Fic">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="KMZ-z4-OrY">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dVw-eP-h23">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="Rih-Xi-stu"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jl8-Ab-Kob">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="94b-R7-2KC">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="MFh-gs-HqM"/>
                                            <constraint firstAttribute="height" constant="40" id="Uai-Wf-YgW"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="pjP-go-GE0" eventType="touchUpInside" id="AB1-5j-aN3"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="94b-R7-2KC" firstAttribute="leading" secondItem="Jl8-Ab-Kob" secondAttribute="leading" constant="16" id="k79-0i-6rC"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="klL-rk-RYy"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Jl8-Ab-Kob" firstAttribute="top" secondItem="dVw-vD-ghm" secondAttribute="top" id="79q-zp-IvT"/>
                            <constraint firstItem="Jl8-Ab-Kob" firstAttribute="leading" secondItem="klL-rk-RYy" secondAttribute="leading" id="9fL-lV-O7P"/>
                            <constraint firstItem="jbm-fI-Fic" firstAttribute="bottom" secondItem="dVw-vD-ghm" secondAttribute="bottom" id="IbS-7u-wux"/>
                            <constraint firstItem="dVw-eP-h23" firstAttribute="leading" secondItem="klL-rk-RYy" secondAttribute="leading" id="Jvt-uu-Ol8"/>
                            <constraint firstItem="jbm-fI-Fic" firstAttribute="leading" secondItem="dVw-vD-ghm" secondAttribute="leading" id="O7f-nU-B7o"/>
                            <constraint firstItem="jbm-fI-Fic" firstAttribute="trailing" secondItem="dVw-vD-ghm" secondAttribute="trailing" id="Pjg-II-Tgr"/>
                            <constraint firstItem="KMZ-z4-OrY" firstAttribute="trailing" secondItem="dVw-vD-ghm" secondAttribute="trailing" id="ZiV-zB-961"/>
                            <constraint firstItem="jbm-fI-Fic" firstAttribute="top" secondItem="dVw-vD-ghm" secondAttribute="top" id="b8q-qb-0jd"/>
                            <constraint firstAttribute="bottom" secondItem="dVw-eP-h23" secondAttribute="bottom" id="bI8-4J-CZk"/>
                            <constraint firstItem="KMZ-z4-OrY" firstAttribute="leading" secondItem="dVw-vD-ghm" secondAttribute="leading" id="erG-cL-UW0"/>
                            <constraint firstItem="klL-rk-RYy" firstAttribute="trailing" secondItem="Jl8-Ab-Kob" secondAttribute="trailing" id="gt3-0s-2Yu"/>
                            <constraint firstItem="klL-rk-RYy" firstAttribute="trailing" secondItem="dVw-eP-h23" secondAttribute="trailing" id="hxM-B5-AZ3"/>
                            <constraint firstItem="KMZ-z4-OrY" firstAttribute="bottom" secondItem="dVw-vD-ghm" secondAttribute="bottom" id="k1d-do-gYt"/>
                            <constraint firstItem="dVw-eP-h23" firstAttribute="top" secondItem="Jl8-Ab-Kob" secondAttribute="bottom" id="nda-vw-KV3"/>
                            <constraint firstItem="94b-R7-2KC" firstAttribute="top" secondItem="klL-rk-RYy" secondAttribute="top" constant="8" id="qgs-Ib-TLb"/>
                            <constraint firstItem="KMZ-z4-OrY" firstAttribute="top" secondItem="dVw-vD-ghm" secondAttribute="top" id="wPi-8c-hGp"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="Jl8-Ab-Kob" id="hO7-Lw-Xma"/>
                        <outlet property="bottomView" destination="dVw-eP-h23" id="amk-KU-IZu"/>
                        <outlet property="bottomViewHeight" destination="Rih-Xi-stu" id="zdQ-6i-8YV"/>
                        <outlet property="closeBtn" destination="94b-R7-2KC" id="bLK-e5-Nox"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ovA-Ox-S2M" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1282" y="-368"/>
        </scene>
        <!--Bridge No Subprocess Work Flow View Controller-->
        <scene sceneID="aRx-Vf-nhi">
            <objects>
                <viewController storyboardIdentifier="BridgeNoSubprocessWorkFlowViewController" id="QA9-sF-i9w" customClass="BridgeNoSubprocessWorkFlowViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cae-3K-TwC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="Bdg-rk-Hzw">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="j9y-7l-77h">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BCl-ai-Euq">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="9v8-oX-mP4"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5py-pc-R2q">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eJl-i1-hBH">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="KLS-B6-zzz"/>
                                            <constraint firstAttribute="width" constant="40" id="Oeq-ua-ffW"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="QA9-sF-i9w" eventType="touchUpInside" id="bGk-sp-UKt"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="eJl-i1-hBH" firstAttribute="leading" secondItem="5py-pc-R2q" secondAttribute="leading" constant="16" id="HQP-4S-6LW"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ZSI-w2-vil"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="ZSI-w2-vil" firstAttribute="trailing" secondItem="BCl-ai-Euq" secondAttribute="trailing" id="0IE-bm-cED"/>
                            <constraint firstItem="5py-pc-R2q" firstAttribute="top" secondItem="cae-3K-TwC" secondAttribute="top" id="6an-mM-E7r"/>
                            <constraint firstItem="j9y-7l-77h" firstAttribute="leading" secondItem="cae-3K-TwC" secondAttribute="leading" id="7G9-pQ-RyT"/>
                            <constraint firstItem="BCl-ai-Euq" firstAttribute="top" secondItem="5py-pc-R2q" secondAttribute="bottom" id="90l-5I-OWr"/>
                            <constraint firstItem="BCl-ai-Euq" firstAttribute="leading" secondItem="ZSI-w2-vil" secondAttribute="leading" id="Ojf-Du-FqG"/>
                            <constraint firstItem="Bdg-rk-Hzw" firstAttribute="leading" secondItem="cae-3K-TwC" secondAttribute="leading" id="RJ7-5T-ZS5"/>
                            <constraint firstAttribute="bottom" secondItem="j9y-7l-77h" secondAttribute="bottom" id="Th3-qG-srj"/>
                            <constraint firstItem="ZSI-w2-vil" firstAttribute="trailing" secondItem="5py-pc-R2q" secondAttribute="trailing" id="YL7-Zb-pfF"/>
                            <constraint firstItem="eJl-i1-hBH" firstAttribute="top" secondItem="ZSI-w2-vil" secondAttribute="top" constant="8" id="YjY-VJ-Ean"/>
                            <constraint firstAttribute="trailing" secondItem="j9y-7l-77h" secondAttribute="trailing" id="Z8k-sw-F0V"/>
                            <constraint firstAttribute="bottom" secondItem="BCl-ai-Euq" secondAttribute="bottom" id="ZJN-sW-YdA"/>
                            <constraint firstItem="5py-pc-R2q" firstAttribute="leading" secondItem="ZSI-w2-vil" secondAttribute="leading" id="a8c-ir-jju"/>
                            <constraint firstItem="Bdg-rk-Hzw" firstAttribute="top" secondItem="cae-3K-TwC" secondAttribute="top" id="b1M-ud-Wjr"/>
                            <constraint firstItem="Bdg-rk-Hzw" firstAttribute="trailing" secondItem="cae-3K-TwC" secondAttribute="trailing" id="dz8-q2-NfG"/>
                            <constraint firstItem="j9y-7l-77h" firstAttribute="top" secondItem="cae-3K-TwC" secondAttribute="top" id="j7h-Un-V1D"/>
                            <constraint firstItem="Bdg-rk-Hzw" firstAttribute="bottom" secondItem="cae-3K-TwC" secondAttribute="bottom" id="vSR-35-I5Q"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="5py-pc-R2q" id="nZg-O1-OkX"/>
                        <outlet property="bottomView" destination="BCl-ai-Euq" id="GHG-t4-kvm"/>
                        <outlet property="bottomViewHeight" destination="9v8-oX-mP4" id="smm-ln-2QM"/>
                        <outlet property="closeBtn" destination="eJl-i1-hBH" id="UEH-yA-i2E"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Dx5-GY-W3q" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2978" y="-368"/>
        </scene>
        <!--Bridge Multiple Scans Work Flow View Controller-->
        <scene sceneID="JcB-rq-AYF">
            <objects>
                <viewController storyboardIdentifier="BridgeMultipleScansWorkFlowViewController" id="rUR-ri-3HV" customClass="BridgeMultipleScansWorkFlowViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fVs-gg-WNM">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="KBt-Eh-PXX">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="Eyf-90-qjK">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2cu-fL-ASp">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ihX-Gv-NVg">
                                        <rect key="frame" x="5" y="22.666666666666629" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="fTr-lh-kBt"/>
                                            <constraint firstAttribute="height" constant="40" id="n8z-Lo-hPw"/>
                                        </constraints>
                                        <color key="tintColor" red="0.80784313730000001" green="0.34901960780000002" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <state key="normal" image="arrow_left"/>
                                        <connections>
                                            <action selector="leftBtnAction:" destination="rUR-ri-3HV" eventType="touchUpInside" id="Wpz-R7-ZTf"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bAj-dv-VIO">
                                        <rect key="frame" x="348" y="22.666666666666629" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="VO5-hL-ust"/>
                                            <constraint firstAttribute="height" constant="40" id="lN6-z4-XYQ"/>
                                        </constraints>
                                        <color key="tintColor" systemColor="systemRedColor"/>
                                        <state key="normal" image="arrow_right"/>
                                        <connections>
                                            <action selector="rightBtnAction:" destination="rUR-ri-3HV" eventType="touchUpInside" id="0HP-az-Osz"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="XZ0-R1-py7">
                                        <rect key="frame" x="20" y="85" width="70" height="45"/>
                                        <color key="backgroundColor" red="0.043137254899999998" green="0.24313725489999999" blue="0.52549019610000003" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="70" id="FY7-4P-ppn"/>
                                            <constraint firstAttribute="height" constant="45" id="GCV-gJ-0lg"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <state key="normal">
                                            <string key="title">未完了
資産</string>
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.shadowOpacity">
                                                <real key="value" value="0.90000000000000002"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="size" keyPath="layer.shadowOffset">
                                                <size key="value" width="3" height="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="titleLabel.textAlignment">
                                                <integer key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="titleLabel.numberOfLines">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="showWillScanAssetBtn:" destination="rUR-ri-3HV" eventType="touchUpInside" id="hAn-ud-xoa"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iRw-eB-CjS" customClass="FontsizeButton" customModule="Runner" customModuleProvider="target">
                                        <rect key="frame" x="100" y="85" width="273" height="45"/>
                                        <color key="backgroundColor" red="0.50196078430000002" green="0.50196078430000002" blue="0.50196078430000002" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="147.5" id="WQG-yX-vkV"/>
                                            <constraint firstAttribute="height" constant="45" id="tF2-zc-CAv"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="スキャンリストへ">
                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.shadowOpacity">
                                                <real key="value" value="0.90000000000000002"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="size" keyPath="layer.shadowOffset">
                                                <size key="value" width="3" height="3"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <variation key="default">
                                            <mask key="constraints">
                                                <exclude reference="WQG-yX-vkV"/>
                                            </mask>
                                        </variation>
                                        <connections>
                                            <action selector="showAssetDetailBtn:" destination="rUR-ri-3HV" eventType="touchUpInside" id="Urx-RI-Fsk"/>
                                        </connections>
                                    </button>
                                    <pickerView contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lwp-Xa-8ea" customClass="FontsizePickerView" customModule="Runner" customModuleProvider="target">
                                        <rect key="frame" x="152" y="-68" width="88" height="200"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <accessibility key="accessibilityConfiguration">
                                            <accessibilityTraits key="traits" notEnabled="YES"/>
                                        </accessibility>
                                    </pickerView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="bAj-dv-VIO" secondAttribute="trailing" constant="5" id="2nx-OK-ZOz"/>
                                    <constraint firstItem="ihX-Gv-NVg" firstAttribute="top" secondItem="2cu-fL-ASp" secondAttribute="top" constant="22.5" id="5AH-Jx-6E8"/>
                                    <constraint firstItem="ihX-Gv-NVg" firstAttribute="leading" secondItem="2cu-fL-ASp" secondAttribute="leading" constant="5" id="AJN-A7-TQu"/>
                                    <constraint firstItem="bAj-dv-VIO" firstAttribute="top" secondItem="2cu-fL-ASp" secondAttribute="top" constant="22.5" id="E1D-SC-u32"/>
                                    <constraint firstItem="XZ0-R1-py7" firstAttribute="top" secondItem="2cu-fL-ASp" secondAttribute="top" constant="85" id="F7s-iy-o0m"/>
                                    <constraint firstAttribute="bottom" secondItem="XZ0-R1-py7" secondAttribute="bottom" constant="25" id="Goj-9V-2bH"/>
                                    <constraint firstItem="XZ0-R1-py7" firstAttribute="leading" secondItem="2cu-fL-ASp" secondAttribute="leading" constant="20" id="N5y-wn-tMS"/>
                                    <constraint firstAttribute="height" constant="150" id="OzU-Rx-7KC"/>
                                    <constraint firstAttribute="trailing" secondItem="iRw-eB-CjS" secondAttribute="trailing" constant="20" id="QiT-Bo-Jdi"/>
                                    <constraint firstItem="iRw-eB-CjS" firstAttribute="leading" secondItem="XZ0-R1-py7" secondAttribute="trailing" constant="10" id="ZlB-m9-IjL"/>
                                    <constraint firstAttribute="bottom" secondItem="iRw-eB-CjS" secondAttribute="bottom" constant="25" id="buH-yF-c2g"/>
                                    <constraint firstItem="bAj-dv-VIO" firstAttribute="centerY" secondItem="ihX-Gv-NVg" secondAttribute="centerY" id="oHK-o9-mRO"/>
                                    <constraint firstItem="iRw-eB-CjS" firstAttribute="top" secondItem="2cu-fL-ASp" secondAttribute="top" constant="85" id="tVa-nK-r9J"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="Goj-9V-2bH"/>
                                        <exclude reference="buH-yF-c2g"/>
                                        <exclude reference="oHK-o9-mRO"/>
                                    </mask>
                                </variation>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="j2F-X2-cBb">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Bz-Zu-07U">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="0Ps-rV-gZN"/>
                                            <constraint firstAttribute="height" constant="40" id="rI1-fN-DGs"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="rUR-ri-3HV" eventType="touchUpInside" id="Ki6-pr-569"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="0Bz-Zu-07U" firstAttribute="leading" secondItem="j2F-X2-cBb" secondAttribute="leading" constant="16" id="o7A-kv-zPL"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Xo0-BK-EeF"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="j2F-X2-cBb" firstAttribute="leading" secondItem="Xo0-BK-EeF" secondAttribute="leading" id="0qp-BK-nq6"/>
                            <constraint firstItem="2cu-fL-ASp" firstAttribute="top" secondItem="j2F-X2-cBb" secondAttribute="bottom" id="75q-4B-wyz"/>
                            <constraint firstItem="KBt-Eh-PXX" firstAttribute="top" secondItem="fVs-gg-WNM" secondAttribute="top" id="FyK-wp-Jed"/>
                            <constraint firstItem="KBt-Eh-PXX" firstAttribute="bottom" secondItem="fVs-gg-WNM" secondAttribute="bottom" id="HON-aF-bAP"/>
                            <constraint firstItem="KBt-Eh-PXX" firstAttribute="trailing" secondItem="fVs-gg-WNM" secondAttribute="trailing" id="HdX-ku-5yz"/>
                            <constraint firstAttribute="bottom" secondItem="2cu-fL-ASp" secondAttribute="bottom" id="RJb-as-EDF"/>
                            <constraint firstAttribute="bottom" secondItem="Eyf-90-qjK" secondAttribute="bottom" id="TkG-et-wJ2"/>
                            <constraint firstItem="Eyf-90-qjK" firstAttribute="leading" secondItem="fVs-gg-WNM" secondAttribute="leading" id="cJl-Cn-HU8"/>
                            <constraint firstItem="Eyf-90-qjK" firstAttribute="top" secondItem="fVs-gg-WNM" secondAttribute="top" id="ctt-fZ-WoH"/>
                            <constraint firstItem="Xo0-BK-EeF" firstAttribute="trailing" secondItem="j2F-X2-cBb" secondAttribute="trailing" id="grE-r4-9Yg"/>
                            <constraint firstItem="Xo0-BK-EeF" firstAttribute="trailing" secondItem="2cu-fL-ASp" secondAttribute="trailing" id="lWU-Zr-GKb"/>
                            <constraint firstItem="KBt-Eh-PXX" firstAttribute="leading" secondItem="fVs-gg-WNM" secondAttribute="leading" id="lkn-xB-b7g"/>
                            <constraint firstItem="j2F-X2-cBb" firstAttribute="top" secondItem="fVs-gg-WNM" secondAttribute="top" id="oU4-B7-xW9"/>
                            <constraint firstItem="0Bz-Zu-07U" firstAttribute="top" secondItem="Xo0-BK-EeF" secondAttribute="top" constant="8" id="vSj-Q8-Kcw"/>
                            <constraint firstAttribute="trailing" secondItem="Eyf-90-qjK" secondAttribute="trailing" id="wdK-Yj-mcI"/>
                            <constraint firstItem="2cu-fL-ASp" firstAttribute="leading" secondItem="Xo0-BK-EeF" secondAttribute="leading" id="z6l-Ee-Brg"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="j2F-X2-cBb" id="sn9-61-5Zs"/>
                        <outlet property="bottomView" destination="2cu-fL-ASp" id="jsS-RZ-Bqj"/>
                        <outlet property="bottomViewHeight" destination="OzU-Rx-7KC" id="v9b-ko-ax4"/>
                        <outlet property="closeBtn" destination="0Bz-Zu-07U" id="jKF-Me-KGJ"/>
                        <outlet property="leftBtn" destination="ihX-Gv-NVg" id="rSh-BS-hq4"/>
                        <outlet property="leftButtonHeight" destination="n8z-Lo-hPw" id="s0t-TK-ZWT"/>
                        <outlet property="listBtn" destination="iRw-eB-CjS" id="z09-sN-gv2"/>
                        <outlet property="listBtnTop" destination="tVa-nK-r9J" id="Sd1-8I-8mg"/>
                        <outlet property="pickerView" destination="lwp-Xa-8ea" id="fNr-ZJ-QEb"/>
                        <outlet property="rightBtn" destination="bAj-dv-VIO" id="P8g-EX-exm"/>
                        <outlet property="rightBtnHeight" destination="lN6-z4-XYQ" id="s3M-eC-KGF"/>
                        <outlet property="uncompleteBtn" destination="XZ0-R1-py7" id="4gI-Gn-5in"/>
                        <outlet property="uncompleteBtnTop" destination="F7s-iy-o0m" id="qp6-xK-HxN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="A7o-9U-Mq5" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3795" y="-368"/>
        </scene>
        <!--Bridge New Work Flow View Controller-->
        <scene sceneID="gZh-HR-2qS">
            <objects>
                <viewController storyboardIdentifier="BridgeNewWorkFlowViewController" id="ifW-6p-v6o" customClass="BridgeNewWorkFlowViewController" customModule="Runner" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="ZW8-5D-Emc">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="m8t-tz-je0">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="SXL-vs-dKM">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nzO-87-FPa">
                                <rect key="frame" x="0.0" y="702" width="393" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="SbW-eE-TAu"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wnw-KU-Ik5">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="702"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HiO-4U-LCt">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="50b-ZT-Uat"/>
                                            <constraint firstAttribute="height" constant="40" id="oDs-hv-2nE"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="ifW-6p-v6o" eventType="touchUpInside" id="eCy-U2-AKI"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="HiO-4U-LCt" firstAttribute="leading" secondItem="wnw-KU-Ik5" secondAttribute="leading" constant="16" id="7zQ-GP-eBf"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="5Np-6B-dhR"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="wnw-KU-Ik5" firstAttribute="top" secondItem="ZW8-5D-Emc" secondAttribute="top" id="0Hj-j4-VqN"/>
                            <constraint firstItem="5Np-6B-dhR" firstAttribute="trailing" secondItem="nzO-87-FPa" secondAttribute="trailing" id="CBe-ch-D0n"/>
                            <constraint firstItem="m8t-tz-je0" firstAttribute="bottom" secondItem="ZW8-5D-Emc" secondAttribute="bottom" id="CGe-7M-2Gh"/>
                            <constraint firstItem="nzO-87-FPa" firstAttribute="leading" secondItem="5Np-6B-dhR" secondAttribute="leading" id="Sus-SU-Kec"/>
                            <constraint firstItem="m8t-tz-je0" firstAttribute="top" secondItem="ZW8-5D-Emc" secondAttribute="top" id="YC3-bf-WA5"/>
                            <constraint firstItem="SXL-vs-dKM" firstAttribute="top" secondItem="ZW8-5D-Emc" secondAttribute="top" id="YcR-pE-iKW"/>
                            <constraint firstItem="SXL-vs-dKM" firstAttribute="bottom" secondItem="ZW8-5D-Emc" secondAttribute="bottom" id="ZLq-h4-TKk"/>
                            <constraint firstItem="m8t-tz-je0" firstAttribute="leading" secondItem="ZW8-5D-Emc" secondAttribute="leading" id="axm-4u-t8E"/>
                            <constraint firstItem="SXL-vs-dKM" firstAttribute="leading" secondItem="ZW8-5D-Emc" secondAttribute="leading" id="bbg-zV-G0c"/>
                            <constraint firstAttribute="bottom" secondItem="nzO-87-FPa" secondAttribute="bottom" id="d0R-Y2-B48"/>
                            <constraint firstItem="nzO-87-FPa" firstAttribute="top" secondItem="wnw-KU-Ik5" secondAttribute="bottom" id="dFW-Wb-pE0"/>
                            <constraint firstItem="HiO-4U-LCt" firstAttribute="top" secondItem="5Np-6B-dhR" secondAttribute="top" constant="8" id="iC6-hu-XpN"/>
                            <constraint firstItem="m8t-tz-je0" firstAttribute="trailing" secondItem="ZW8-5D-Emc" secondAttribute="trailing" id="njP-ee-7W0"/>
                            <constraint firstItem="SXL-vs-dKM" firstAttribute="trailing" secondItem="ZW8-5D-Emc" secondAttribute="trailing" id="onb-YJ-ijr"/>
                            <constraint firstItem="wnw-KU-Ik5" firstAttribute="leading" secondItem="5Np-6B-dhR" secondAttribute="leading" id="wgX-8A-LHP"/>
                            <constraint firstItem="5Np-6B-dhR" firstAttribute="trailing" secondItem="wnw-KU-Ik5" secondAttribute="trailing" id="z29-Uq-4XR"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="wnw-KU-Ik5" id="7ep-Cc-s30"/>
                        <outlet property="bottomView" destination="nzO-87-FPa" id="FlH-wd-4nh"/>
                        <outlet property="bottomViewHeight" destination="SbW-eE-TAu" id="7sm-S4-zrO"/>
                        <outlet property="closeBtn" destination="HiO-4U-LCt" id="n4i-4H-rkH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="IIe-Wk-MAi" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2120" y="-368"/>
        </scene>
    </scenes>
    <resources>
        <image name="arrow_left" width="64" height="64"/>
        <image name="arrow_right" width="64" height="64"/>
        <image name="bg" width="750" height="1334"/>
        <image name="demo" width="443" height="960"/>
        <image name="scan_close" width="50" height="50"/>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
