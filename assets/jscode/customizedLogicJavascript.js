// 将 window.alert 重写为 async 函数
window.alert = async function (message) {
  // 传递正确的 functionName 和 params
  return await callFlutterFunction('showAlert', [message]);
};

/**
 * 调用 Flutter 函数的通用方法
 *
 * @param {string} functionName - 要调用的 Flutter 函数名
 * @param {Array} params - 传递给函数的参数数组
 * @returns {Promise|any} - Flutter 函数的返回结果
 */
async function callFlutterFunction(functionName, params = []) {
  try {
    console.log(`调用 Flutter 函数 开始: ${functionName}, 参数:`, params);
    let result = await window.flutter_inappwebview.callHandler('FlutterFunctionChannel', JSON.stringify({
      function: functionName,
      params: params
    }));
    console.log(`调用 Flutter 函数 结束: ${functionName}, 参数:`, params, "结果:", result);
    return result;
  } catch (error) {
    console.error(`调用 Flutter 函数 异常: ${functionName}, 错误信息:`, error. message, error.stack);
    throw error;
  }
}

/// ==================== 数据操作函数 ====================

/// 设置项目值
async function setValue(itemName, itemValue, instance) {
  return await callFlutterFunction('setValue', [itemName, itemValue]);
}

/// 获取项目值
async function getValue ( itemName, instance ) {
  return await callFlutterFunction('getValue', [itemName]);
}

function getMasterValue(itemName, masterItemName, instance) {
  let value = '';
  let itemDataDict = instance;
  if (instance && instance.itemDataDict) {
    itemDataDict = instance.itemDataDict;
  }
  for (let sectionName in itemDataDict) {
    itemDataDict[sectionName].forEach((item) => {
      if (item.itemName === itemName) {
        item.optionObject.masterDisplayItems.forEach((masterDisplayItem) => {
          if (
            masterDisplayItem &&
            masterDisplayItem.itemName &&
            masterDisplayItem.itemId &&
            masterDisplayItem.itemName === masterItemName
          ) {
            if (item.defaultData.display && masterDisplayItem.itemId) {
              value = item.defaultData.display[masterDisplayItem.itemId];
            }
          }
        });
      }
    });
  }
  return value;
}

async function readonly(itemName, instance) {
  return await callFlutterFunction('readonly', [itemName]);
}

async function canWrite(itemName, instance) {
  return await callFlutterFunction('canWrite', [itemName]);
}

async function showItemMsg(itemName, msg, instance) {
  return await callFlutterFunction('showItemMsg', [itemName, msg]);
}

async function clearItemMsg(itemName, instance) {
  return await callFlutterFunction('clearItemMsg', [itemName]);
}

async function showMsg(type, that, msg) {
  return await callFlutterFunction('showAlert', [msg]);
}

async function generateMaster(
  assetTypeId = this.assetTypeId,
  masterTypeId,
  masterId,
  that
) {
  const url = "secure/MasterDetail?" + "masterTypeId=" + masterTypeId;
  const httpUtilsService = new HttpUtilsService();
  const result = await httpUtilsService.get(url, false, true, false);
  let assetItemList = that.assetTypeItem.assetItemList;
  let masterDetailList = result.masterDetail;
  let tempMasterDic = {};
  assetItemList.forEach((assetItem) => {
    if (assetItem.itemType === "master") {
      let option =
        typeof assetItem.option === "string"
          ? JSON.parse(assetItem.option)
          : assetItem.option;
      if (option.masterTypeId === masterDetailList[0].masterTypeId) {
        let masterDisplayItems = option.masterDisplayItems;
        masterDetailList.forEach((masterDetail) => {
          if (masterDetail.masterId == masterId) {
            let masterDic =
              typeof masterDetail.masterText === "string"
                ? JSON.parse(masterDetail.masterText)
                : masterDetail.masterText;
            masterDisplayItems.forEach((masterDisplayItem) => {
              if (
                Object.keys(masterDic).indexOf(masterDisplayItem["itemName"]) >
                -1
              ) {
                tempMasterDic[masterDisplayItem["itemId"]] =
                  masterDic[masterDisplayItem["itemName"]];
              }
            });
          }
        });
      }
    }
  });
  return {masterId: masterId, display: tempMasterDic};
}

async function getAsset(assetId, that) {
  if (assetId && assetId !== "") {
    const url = "secure/Asset/findById?" + "assetId=" + assetId;
    const httpUtilsService = new HttpUtilsService();
    let result = await httpUtilsService.get(url);
    let asset;
    if (result.arrayAsset.length > 0) {
      asset = result.arrayAsset[0];
    }
    return asset;
  }
}

async function updateAsset(asset, that) {
  const assetText = await getNumbersCommaRemoved(
    asset?.assetText,
    asset?.assetTypeId,
    that.assetService,
    false
  )
    .then((result) => {
      console.log("Processed result:", result);
      return result;
    })
    .catch((error) => {
      console.error("Error:", error);
    });
  let relatesString = await getRelationData(asset.assetId);
  let assetOld = await getAssetTS(asset.assetId, that);
  asset.modifiedDate = assetOld.modifiedDate;
  await saveAssetdata(
    asset.assetTypeId,
    asset.assetId,
    assetText,
    asset.modifiedDate,
    relatesString,
    asset.barcode
  );
}

async function getWorkflowAssetList(that) {
  if (
    that.processInstanceId ||
    that.taskId ||
    that.processInstanceId === "" ||
    that.taskId === ""
  ) {
    return [];
  }
  const processInstanceId = Number(that.processInstanceId);
  const taskId = Number(that.taskId);
  const scanState = undefined;
  const skip = 0;
  const rows = 99999;
  const keyword = undefined;
  const moreThenLimit = false;
  const httpUtilsService = new HttpUtilsService();
  let url =
    "/secure/workflowEngine/mobile/getAssetsByKeywordInWfAssetListForAssignScan";
  url = `${url}?processInstanceId=${processInstanceId}`;
  url = `${url}&taskId=${taskId}`;
  if (_.isNumber(scanState)) {
    url = `${url}&scanState=${scanState}`;
  }
  if (!_.isEmpty(keyword)) {
    url = `${url}&keyword=${encodeURIComponent(keyword)}`;
  }
  url = `${url}&skip=${skip}`;
  url = `${url}&rows=${rows}`;
  const getAssetListData = await httpUtilsService.get(
    url,
    false,
    true,
    true,
    false
  );
  const wfAssetList = getAssetListData.assetListDatas ?? [];
  return wfAssetList;
}

async function insertAsset(asset, that) {
  let result;
  const assetText = await getNumbersCommaRemoved(
    asset?.assetText,
    asset?.assetTypeId,
    false
  );
  await insertAssetData(asset.assetTypeId, asset.barcode, assetText)
    .then(async (data) => {
      result = 1;
    })
    .catch((err) => {
      result = 0;
    });
  return result;
}

function getAssetId(that) {
  if (that.assetId) {
    return that.assetId;
  } else {
    return null;
  }
}

function getAction(that) {
  if (that) {
    return that.actionName;
  }
}

function getWorkflowTaskName(that) {
  if (that.stepName) {
    return that.stepName;
  }
}

async function getAppurtenancesInformation(
  assetId,
  appurtenanceTypeId,
  count,
  that
) {
  const httpUtilsService = new HttpUtilsService();
  let startIndex = 0;
  const keyword = "";
  const appurtenancesInformationTypeId = appurtenanceTypeId;
  const skip = String(startIndex);
  const rows = String(count);
  let tempUrl =
    "secure/AppurtenancesInformation/mobile/appurtenancesInformationByTypeId?" +
    "assetId=" +
    assetId +
    "&appurtenancesInformationTypeId=" +
    appurtenancesInformationTypeId +
    "&skip=" +
    skip +
    "&rows=" +
    rows;
  if (keyword !== "") {
    tempUrl += "&keyword=" + keyword;
  }
  // 履歴情報リストを取得する
  let resultAppurtenancesInfoData = await httpUtilsService.get(tempUrl);
  let appurtenancesInformationList =
    resultAppurtenancesInfoData.appurtenancesInformationList;
  return appurtenancesInformationList;
}

async function insertAppurtenancesInformation(appurtenance, that) {
  const httpUtilsService = new HttpUtilsService();
  const appurtenancesInformationText = await getNumbersCommaRemoved(
    appurtenance?.appurtenancesInformationText,
    appurtenance?.appurtenancesInformationTypeId,
    true
  );

  let map = {};
  const appurtenancesInformationTextObj = JSON.parse(
    appurtenancesInformationText
  );
  const allKeys = Object.keys(appurtenancesInformationTextObj);
  allKeys.forEach((key) => {
    map[key] = appurtenancesInformationTextObj[key];
  });
  const url1 = "secure/User/getMyAccount?";
  const userInfo = await httpUtilsService.get(url1, false, true);
  const user = userInfo.data;
  const firstName = user.firstName;
  const lastName = user.lastName;
  map["ユーザー名"] = lastName + " " + firstName;
  map["新規登録時間"] = getFormatDateTime(new Date());
  map["ロケーション"] = loction;
  const postData = new FormData();
  postData.append("assetId", String(appurtenance.assetId));
  postData.append(
    "appurtenancesInformationTypeId",
    String(appurtenance.appurtenancesInformationTypeId)
  );
  postData.append("appurtenancesInformationText", JSON.stringify(map));
  console.log("***********");
  console.log(postData);
  const url2 = "secure/AppurtenancesInformation/insert?";
  console.log(url2);
  await httpUtilsService.post(url2, postData);
}

async function updateAppurtenancesInformation(appurtenance, that) {
  const httpUtilsService = new HttpUtilsService();
  const appurtenancesInformationText = await getNumbersCommaRemoved(
    appurtenance?.appurtenancesInformationText,
    appurtenance?.appurtenancesInformationTypeId,
    true
  );
  const appurtenanceOld = await getAppurtenancesInformationList(
    appurtenance.appurtenancesInformationId,
    that
  );
  appurtenance.modifiedDate = appurtenanceOld.modifiedDate;
  let appurtenancesInformationTypeId = String(appurtenance.appurtenancesInformationTypeId);
  let appurtenancesInformationId = String(appurtenance.appurtenancesInformationId);
  appurtenancesInformationText;
  let modifiedDate = appurtenance.modifiedDate;
  let assetId = appurtenance.assetId;
  var postData;
  if (assetId) {
    postData = {
      assetId,
      appurtenancesInformationTypeId,
      appurtenancesInformationText,
      appurtenancesInformationId,
      tenantId: tenantIdLocal,
      modifiedDate,
    };
  } else {
    postData = {
      appurtenancesInformationTypeId,
      appurtenancesInformationText,
      appurtenancesInformationId,
      tenantId: tenantIdLocal,
      modifiedDate,
    };
  }
  const url = 'secure/AppurtenancesInformation/update?';
  await httpUtilsService.post(url, postData);
}

async function getMasterValueList(masterTypeId, that) {
  if (masterTypeId) {
    var masterList = [];
    const httpUtilsService = new HttpUtilsService();
    const url = 'secure/MasterDetail?' + 'masterTypeId=' + masterTypeId;
    const result = await httpUtilsService.get(url, false, true, false);
    if (result.code === 0) {
      result.masterDetail.forEach((master) => {
        master['masterText'] = JSON.parse(master['masterText']);
        masterList.push(master);
      });
    }
    return masterList;
  }
}

async function getAssetsByType(assetTypeId, rows, that) {
  if (assetTypeId && rows) {
    let newAssetList = [];
    newAssetList = await getAssetListData(String(0), String(rows), assetTypeId);
    return newAssetList;
  }
}

function dateFormat(date, fmt) {
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'S': date.getMilliseconds(),
  };

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
}

function now() {
  return new Date();
}

// TODO
function toggleSection(sectionName, isShowSection, that, instance) {
  if (that.tempSectionSortItemArray.length == 0) {
    return;
  }
  // 如果 isShowSection 为 true，则添加 sectionName 到 sectionSortItemArray
  if (isShowSection) {
    // 从原始数组里面取出即将被添加的数据
    const sn = that.tempSectionSortItemArray.find((e) => e.sectionName == sectionName);
    // 添加
    that.sectionSortItemArray = Array.from(new Set([...that.sectionSortItemArray, sn]));
  } else {
    // 如果 isShowSection 为 false，则从 sectionSortItemArray 中移除指定 sectionName
    // 删除
    that.sectionSortItemArray = that.sectionSortItemArray.filter((fr) => fr.sectionName != sectionName);
  }
  // 重新创建排序后的 sectionNameArray
  that.sectionNameArray = createSectionNameArray(that.sectionSortItemArray);
}

//todo
function changeListItem(itemName, list, that, instance) {
  // 遍历各个section来寻找项目名相同的那个,进行值的更新
  that.sectionNameArray.forEach((item) => {
    instance[item].forEach((data) => {
      if (data.itemName === itemName) {
        // 获取要被改变的数组的原始数据
        var tempData = [];
        if (data.option) {
          let option = JSON.parse(data.option);
          tempData = _.cloneDeep(option.data);
        }
        // 取两个数组的交集
        var intersectionList = list.filter(function (v) {
          return tempData.indexOf(v) > -1;
        });
        data.optionObject.data = intersectionList ? intersectionList : [];
        // 如果这个详细的项目并不在更新后的list里，删除这个显示值
        if (!intersectionList.includes(data.defaultData)) {
          data.itemValue = '';
          data.defaultData = '';
        }
      }
    });
  });
}

function getAppurtenancesInformationId(that) {
  return that.appurtenancesInformationId ? that.appurtenancesInformationId : null;
}

function getUserInfo() {
  return {
    userDisplayName: lastName + ' ' + firstName,
    userId: userIdLocal,
    userName: userName,
  };
}


// セクションの並び順を調整
function createSectionNameArray(itemArray) {
  function compare(property) {
    return function (obj1, obj2) {
      var value1 = obj1[property];
      var value2 = obj2[property];
      return value1 - value2;
    };
  }

  var sectionNameArray = [];
  var sortObj = itemArray.sort(compare('sectionSort'));
  sortObj.forEach((item) => {
    sectionNameArray.push(item.sectionName);
  });
  return sectionNameArray;
}


async function getAssetListData(skip, rows, assetTypeId, needLoading = true, delay = true) {
  try {
    const httpUtilsService = new HttpUtilsService();
    const url1 = 'secure/AssetMobileSetting/forMobile?' + 'assetTypeId=' + assetTypeId;
    const result1 = await httpUtilsService.get(url1, false, true, true);

    // itemNameを再設定する
    [1, 2, 3].forEach((level) => {
      if (result1.assetMobileSettingForMobile.assetInfo[`assetLevel${level}ItemId`] == 0) {
        result1.assetMobileSettingForMobile.assetInfo[`assetLevel${level}ItemName`] = '資産種類';
      }
      if (result1.assetMobileSettingForMobile.assetInfo[`arLevel${level}ItemId`] == 0) {
        result1.assetMobileSettingForMobile.assetInfo[`arLevel${level}ItemName`] = '資産種類';
      }
    });
    // todo
    this.assetItemList = result1.assetItemList;
    getItemName(result1);

    // 获取资产数据
    const result2 = await this.httpAssetService.httpGetAssetData(skip, rows, assetTypeId, needLoading, delay);
    const assetList = getAssetList(result2.arrayAsset);

    return assetList;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

async function getItemName(data, needLoading = true) {
  // 更新用户角色
  const url = 'secure/UserRole/myRole?';
  const httpUtilsService = new HttpUtilsService();
  const result = await httpUtilsService.get(url, false, needLoading);
  if (result.code === 0) {
    let userRoleList = result.data;
    FlutterChannel.postMessage(JSON.stringify({
      method: "userRoleList",
      params: userRoleList,
    }));
  }
  let assetInfo = data.assetMobileSettingForMobile.assetInfo;
  const tAssetCategory = data.assetMobileSettingForMobile.arInfo;
  const assetItemList = data.assetItemList;
  // 資産の配置項目が存在するかどうかは、資産分類のスクリーニングに使用されます。
  if (tAssetCategory) {
    this.assetCategorys = [];
    this.assetCategorysId = [];
    this.assetCategorysTypes = [];
    for (let i = 0; i < 5; i++) {
      const tAssetCategoryElement = tAssetCategory[`category${i + 1}ItemName`];
      const tAssetCategoryElementId = tAssetCategory[`category${i + 1}ItemId`];
      const tAssetCategoryElementType = tAssetCategory[`category${i + 1}ItemType`];
      const tAssetCategorySubElement = tAssetCategory[`category${i + 1}SubItemName`];
      const tAssetCategorySubElementId = tAssetCategory[`category${i + 1}SubItemId`];
      const tAssetCategorySubElementType = tAssetCategory[`category${i + 1}SubItemType`];
      const tAssetCategorySubElementOption = tAssetCategory[`category${i + 1}SubItemOption`];
      if (tAssetCategoryElement !== undefined && tAssetCategoryElement !== null) {
        for (const assetItem of assetItemList) {
          if (assetItem.itemName === tAssetCategoryElement) {
            this.assetCategorys.push({
              itemId: tAssetCategoryElementId,
              itemDisplayName: assetItem.itemDisplayName,
              itemName: assetItem.itemName,
              itemType: assetItem.itemType,
              optionObj: JSON.parse(assetItem.option),
              itemSubName: tAssetCategorySubElement,
              itemSubId: tAssetCategorySubElementId,
              itemSubType: tAssetCategorySubElementType,
              subOptionObj: isJsonString(tAssetCategorySubElementOption)
                ? JSON.parse(tAssetCategorySubElementOption)
                : null,
            });
          }
        }
        this.assetCategorysId.push(tAssetCategoryElementId);
        this.assetCategorysTypes.push(tAssetCategoryElementType);
      }
    }
  }
  if (Object.keys(assetInfo).length === 0) {
    this.itemName1 = 'assetName';
    this.itemName2 = 'createdDate';
    this.itemName3 = 'passedTime';
    this.isNoSet = true;
  } else {
    this.assetLevel1ItemOptionObject = JSON.parse(assetInfo['assetLevel1ItemOption']);
    this.assetLevel2ItemOptionObject = JSON.parse(assetInfo['assetLevel2ItemOption']);
    this.assetLevel3ItemOptionObject = JSON.parse(assetInfo['assetLevel3ItemOption']);
    this.assetLevel4ItemOptionObject = JSON.parse(assetInfo['assetLevel4ItemOption']);
    this.assetLevel5ItemOptionObject = JSON.parse(assetInfo['assetLevel5ItemOption']);

    this.assetLevel1ItemType = assetInfo['assetLevel1ItemType'];
    this.assetLevel2ItemType = assetInfo['assetLevel2ItemType'];
    this.assetLevel3ItemType = assetInfo['assetLevel3ItemType'];
    this.assetLevel4ItemType = assetInfo['assetLevel4ItemType'];
    this.assetLevel5ItemType = assetInfo['assetLevel5ItemType'];
    this.isNoSet = false;
    if (
      assetInfo.assetLevel1ItemName === undefined &&
      assetInfo.assetLevel2ItemName === undefined &&
      assetInfo.assetLevel3ItemName === undefined &&
      assetInfo.assetLevel4ItemName === undefined &&
      assetInfo.assetLevel5ItemName === undefined
    ) {
      this.itemName1 = 'assetName';
      this.itemName2 = 'createdDate';
      this.itemName3 = 'passedTime';
      this.isNoSet = true;
    } else {
      this.isNoSet = false;
      this.itemName1 = assetInfo.assetLevel1ItemName;
      let isView1 = true;
      if (assetInfo.assetLevel1ItemOption) {
        isView1 = await visiblePermissionsCheck(
          JSON.parse(assetInfo.assetLevel1ItemOption).sectionPrivateGroups
        );
      }
      this.itemName1Permission = isView1;

      this.itemName2 = assetInfo.assetLevel2ItemName;
      let isView2 = true;
      if (assetInfo.assetLevel2ItemOption) {
        isView2 = await visiblePermissionsCheck(
          JSON.parse(assetInfo.assetLevel2ItemOption).sectionPrivateGroups
        );
      }
      this.itemName2Permission = isView2;

      this.itemName3 = assetInfo.assetLevel3ItemName;
      let isView3 = true;
      if (assetInfo.assetLevel3ItemOption) {
        isView3 = await visiblePermissionsCheck(
          JSON.parse(assetInfo.assetLevel3ItemOption).sectionPrivateGroups
        );
      }
      this.itemName3Permission = isView3;

      this.itemName4 = assetInfo.assetLevel4ItemName;
      let isView4 = true;
      if (assetInfo.assetLevel4ItemOption) {
        isView4 = await visiblePermissionsCheck(
          JSON.parse(assetInfo.assetLevel4ItemOption).sectionPrivateGroups
        );
      }
      this.itemName4Permission = isView4;

      this.itemName5 = assetInfo.assetLevel5ItemName;
      let isView5 = true;
      if (assetInfo.assetLevel5ItemOption) {
        isView5 = await visiblePermissionsCheck(
          JSON.parse(assetInfo.assetLevel5ItemOption).sectionPrivateGroups
        );
      }
      this.itemName5Permission = isView5;
    }
  }
}

// todo
function getAssetList(arrayAsset) {
  if (this.itemName1 === undefined && this.itemName2 === undefined && this.itemName3 === undefined) {
    this.itemName1 = 'assetName';
    this.itemName2 = 'createdDate';
    this.itemName3 = 'passedTime';
    this.isNoSet = true;
  } else {
    this.isNoSet = false;
  }

  arrayAsset.forEach((asset) => {
    if (this.itemName1Permission) {
      const {transform, assetText} = this.numberOfDays(
        asset,
        this.itemName1,
        JSON.parse(asset.assetText)[this.itemName1],
        this.assetLevel1ItemType,
        this.assetLevel1ItemOptionObject
      );
      asset.firstText = transform;
      if (!_.isEmpty(assetText)) {
        asset.assetText = assetText;
      }
    } else {
      asset.firstText = '';
    }

    console.log('assetServer => getAssetList => isNoSet', this.isNoSet);

    if (this.isNoSet === true) {
      this.assetItemList.forEach((item) => {
        if (item.itemName === this.itemName2) {
          let secondT = '';
          let assetP = JSON.parse(asset.assetText);
          if (item.itemType === 'dateTime') {
            secondT = getDateTimeFromDateType(assetP[this.itemName2]);
          } else {
            secondT = getDateFromDateType(assetP[this.itemName2]);
          }
          asset.secondText = secondT;
          assetP[this.itemName2] = secondT;
          asset.assetText = JSON.stringify(assetP);
        }
      });
    } else {
      if (this.itemName2Permission) {
        const {transform, assetText} = this.numberOfDays(
          asset,
          this.itemName2,
          JSON.parse(asset.assetText)[this.itemName2],
          this.assetLevel2ItemType,
          this.assetLevel2ItemOptionObject
        );
        asset.secondText = transform;
        if (!_.isEmpty(assetText)) {
          asset.assetText = assetText;
        }
      } else {
        asset.secondText = '';
      }
    }

    if (this.itemName3Permission) {
      const {transform, assetText} = this.numberOfDays(
        asset,
        this.itemName3,
        JSON.parse(asset.assetText)[this.itemName3],
        this.assetLevel3ItemType,
        this.assetLevel3ItemOptionObject
      );
      asset.thirdText = transform;
      if (!_.isEmpty(assetText)) {
        asset.assetText = assetText;
      }
    } else {
      asset.thirdText = '';
    }

    if (this.itemName4Permission) {
      const {transform, assetText} = this.numberOfDays(
        asset,
        this.itemName4,
        JSON.parse(asset.assetText)[this.itemName4],
        this.assetLevel4ItemType,
        this.assetLevel4ItemOptionObject
      );
      asset.fouthText = transform;
      if (!_.isEmpty(assetText)) {
        asset.assetText = assetText;
      }
    } else {
      asset.fouthText = '';
    }

    if (this.itemName5Permission) {
      const {transform, assetText} = this.numberOfDays(
        asset,
        this.itemName5,
        JSON.parse(asset.assetText)[this.itemName5],
        this.assetLevel5ItemType,
        this.assetLevel5ItemOptionObject
      );
      asset.fifthText = transform;
      if (!_.isEmpty(assetText)) {
        asset.assetText = assetText;
      }
    } else {
      asset.fifthText = '';
    }

    // Handling the home image
    const assetTextParsed = JSON.parse(asset.assetText);
    if (assetTextParsed.homeImage !== undefined) {
      console.log(assetTextParsed.homeImage);
      if (assetTextParsed.homeImage !== '') {
        console.log(assetTextParsed.homeImage[0].turl);
        asset.homeImage = assetTextParsed.homeImage[0].turl;
      }
    }
  });

  this.assetList = arrayAsset;
}


async function visiblePermissionsCheck(sectionPrivateGroups) {
  let isChecked = false;
  try {
    // todo
    const data = userRoleListLocal;
    let userRoleList = data;
    let roleList = [];
    userRoleList.forEach((userRole) => {
      roleList.push(String(userRole.roleId));
    });
    if (sectionPrivateGroups !== undefined) {
      if (sectionPrivateGroups.includes(',')) {
        let sectionPrivateGroupsList = sectionPrivateGroups.split(',');
        let intersectionArr = roleList.filter(function (v) {
          return sectionPrivateGroupsList.indexOf(v) !== -1;
        });
        if (intersectionArr.length > 0) {
          isChecked = true;
        }
      } else {
        if (sectionPrivateGroups === '') {
          isChecked = true;
        } else {
          if (roleList.includes(sectionPrivateGroups)) {
            isChecked = true;
          }
        }
      }
    } else {
      isChecked = true;
    }
  } catch (err) {
    console.error(err);
  }
  return isChecked;
}


async function getAppurtenancesInformationList(
  appurtenancesInformationId,
  that
) {
  let data;
  const url =
    "secure/AppurtenancesInformation/getAppurtenancesInformationListById?" +
    "appurtenancesInformationId=" +
    appurtenancesInformationId;
  const httpUtilsService = new HttpUtilsService();
  const appurtenanceOld = await httpUtilsService.get(url);
  if (appurtenanceOld.appurtenancesInformationList.length > 0) {
    data = appurtenanceOld.appurtenancesInformationList[0];
  }
  return data;
}

async function insertAssetData(assetTypeId, barcode, assetText) {
  // turlをDBに送らないようにする
  let assetDic = JSON.parse(assetText);
  for (let key in assetDic) {
    let value = assetDic[key];
    if (Array.isArray(value)) {
      value.forEach((subValue) => {
        if (subValue.turl !== undefined) {
          subValue.turl = "";
        }
      });
    }
  }
  let assetTextStr = JSON.stringify(assetDic);
  try {
    const httpUtilsService = new HttpUtilsService();
    let postData;
    if (barcode) {
      postData = {
        assetTypeId: assetTypeId,
        assetText: String(assetTextStr),
        barcode: barcode,
      };
    } else {
      postData = {
        assetTypeId: assetTypeId,
        assetText: String(assetTextStr),
      };
    }
    const url = "secure/Asset/insert?";
    const data = await httpUtilsService.post(url, postData);
    return data;
  } catch (err) {
    console.error(err);
    throw err;
  }
}

function isJsonString(str) {
  try {
    if (typeof JSON.parse(str) === 'object') {
      return true;
    }
  } catch (e) {
  }
  return false;
}

async function saveAssetdata(
  assetTypeId,
  assetId,
  assetText,
  modifiedDate,
  relationAssetIdList,
  barcode
) {
  const url = "secure/Asset/update";
  var postData = {};
  postData["assetId"] = assetId;
  postData["assetTypeId"] = assetTypeId;
  postData["assetText"] = assetText;
  if (modifiedDate) {
    postData["modifiedDate"] = modifiedDate;
  }
  if (barcode) {
    postData["barcode"] = barcode;
  }
  if (relationAssetIdList) {
    postData["relationAssetIdList"] = relationAssetIdList;
  }
  const httpUtilsService = new HttpUtilsService();
  const result = httpUtilsService.post(url, postData);
  return result;
}

async function getNumbersCommaRemoved(
  assetText,
  assetTypeId,
  isAppurtenancesInfo
) {
  const httpUtilsService = new HttpUtilsService();
  // 深拷贝
  let assetTextHoc = JSON.parse(JSON.stringify(assetText));
  if (typeof assetTextHoc == "string") {
    // string 转 obj
    assetTextHoc = JSON.parse(assetTextHoc);
  }

  let assetTypeIdStr = assetTypeId;
  if (typeof assetTypeIdStr != "string") {
    try {
      assetTypeIdStr = assetTypeIdStr.toString().trim();
    } catch (error) {
      assetTypeIdStr = "";
    }
  }

  // 用户可能没有传递 assetTypeId 或者非纯数字类型
  if (assetTypeIdStr == "") {
    throw `typeId${isAppurtenancesInfo ? "履历情报" : "资产"}为空`;
  }
  if (isNaN(parseInt(assetTypeIdStr))) {
    throw `typeId${isAppurtenancesInfo ? "履历情报" : "资产"
    }非纯数字，不符合规则`;
  }

  let assetItemList;
  if (isAppurtenancesInfo) {
    const url =
      "/secure/AppurtenancesInformationMobileSetting/forMobile?" +
      "&appurtenancesInformationTypeId=" +
      assetTypeIdStr;
    const appurtenancesInformationType = await httpUtilsService.get(url);
    assetItemList = appurtenancesInformationType["layoutSettingList"];
  } else {
    const url = "secure/AssetItem?" + "assetTypeId=" + assetTypeIdStr;
    const assetItemResult = await httpUtilsService.get(url);
    assetItemList = assetItemResult["assetItemList"];
  }

  if (assetItemList.length == 0) {
    throw `assetItemList${isAppurtenancesInfo ? "履历情报" : "资产"
    }assetItemList为空，可能是typeId不正确在DB中没找到`;
  }

  const strConversion = (strTo) => {
    const tmp = strTo;
    if (tmp.trim() == "-0") {
      return "0";
    }
    if (tmp.startsWith("+")) {
      return tmp.substring(1);
    }
    return tmp;
  };

  // 以下代码专门为了解决ASCHK-25523
  // 主要是在执行JS之前把数字项目逗号去掉
  // 因为有可能在JS里面会包含新规资产
  // 后台规定新规资产时数字不能有逗号
  // 通貨/数値  の場合のチェック
  const throughVal = ["currency", "number", "calculate"];
  for (let index = 0; index < assetItemList.length; index++) {
    const item = assetItemList[index];
    let valName = assetTextHoc[item.itemName];
    if (!throughVal.includes(item.itemType)) {
      continue;
    }

    if (typeof valName != "string") {
      try {
        valName = valName.toString();
      } catch (error) {
        valName = "";
      }
    }
    if (valName == "") {
      continue;
    }
    if (item.itemType === "currency" || item.itemType === "number") {
      let a = strConversion(valName).split(",").join("");
      assetTextHoc[item.itemName] = a;
      continue;
    }
    // 計算 の場合のチェック
    if (item.itemType === "calculate") {
      let a = valName.split(",").join("");
      assetTextHoc[item.itemName] = a;
      continue;
    }
  }
  return JSON.stringify(assetTextHoc);
}

async function getRelationData(assetId) {
  const httpUtilsService = new HttpUtilsService();
  // 相关信息设置
  const url = "secure/Asset/getAssetRelationList?" + "assetId=" + assetId;
  let result2 = await httpUtilsService.get(url);
  var dict = {}; // 不需要类型注解
  result2.assetRelationList.forEach((assetRelation) => {
    // 数据设置
    if (Object.keys(dict).includes(String(assetRelation.assetTypeId))) {
      dict[assetRelation.assetTypeId].push(assetRelation);
    } else {
      var itemList = [];
      itemList.push(assetRelation);
      dict[String(assetRelation.assetTypeId)] = itemList;
    }
  });

  var relateListString = "";
  for (let item of Object.keys(dict)) {
    dict[item].forEach((data) => {
      relateListString = relateListString + data.assetId + ",";
    });
  }
  return relateListString;
}

function getFormatDateReturnDate(date) {
  const formattedDateTimeStr = getFormatDate(date);
  return new Date(formattedDateTimeStr);
}

function getFormatDateTime(date) {
  const dateObject = validateDateInput(date);
  if (dateObject == undefined || dateObject == null) {
    return date;
  }
  return formatAndReturn(dateObject, "yyyy/MM/dd HH:mm");
}

function getFormatDate(date) {
  const dateObject = validateDateInput(date);
  if (dateObject === undefined || dateObject === null) {
    return date;
  }
  return formatAndReturn(dateObject, "yyyy/MM/dd");
}

function validateDateInput(date) {
  let dateObject;
  if (typeof date === "string") {
    dateObject = parseDateString(date);
  } else {
    dateObject = date;
  }
  if (isValid(dateObject)) {
    return dateObject;
  }
  return undefined;
}

function isValid(date) {
  return date instanceof Date && !isNaN(date.getTime());
}

function parseDateString(dateString) {
  if (dateString.includes("/")) {
    dateString = dateString.replace(/\//g, "-");
  }
  if (dateString.includes(" ")) {
    dateString = dateString.replace(" ", "T");
  }

  const parsedDate = new Date(dateString);

  if (!isNaN(parsedDate.getTime())) {
    return parsedDate;
  }
  return undefined;
}

function formatAndReturn(date, formatStr) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  // 根据传入的 formatStr 格式化日期
  return formatStr
    .replace("yyyy", year)
    .replace("MM", month)
    .replace("dd", day)
    .replace("HH", hours)
    .replace("mm", minutes);
}

function dayHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setDate(date.getDate() + quantity);

  // 根据原始输入格式决定返回格式
  // 如果原始输入包含时间信息，返回完整的日期时间格式
  // 否则只返回日期格式
  if (typeof dateStr === 'string' && dateStr.includes(' ')) {
    // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
    return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
  } else {
    // 原始输入只有日期，返回 yyyy/MM/dd 格式
    return formatAndReturn(date, 'yyyy/MM/dd');
  }
}

function monthHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  let day = date.getDate();
  date.setMonth(date.getMonth() + quantity);
  let day2 = date.getDate();
  if (day > day2) {
    date = new Date(date.getFullYear(), date.getMonth(), 0);
  }

  // 根据原始输入格式决定返回格式
  //  if (typeof dateStr === 'string' && dateStr.includes(' ')) {
  //    // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
  //    return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
  //  } else {
  // 原始输入只有日期，返回 yyyy/MM/dd 格式
  return formatAndReturn(date, 'yyyy/MM/dd');
  //  }
}

function yearHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setFullYear(date.getFullYear() + quantity);

  // 根据原始输入格式决定返回格式
  // if (typeof dateStr === 'string' && dateStr.includes(' ')) {
  //   // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
  //   return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
  // } else {
  // 原始输入只有日期，返回 yyyy/MM/dd 格式
  return formatAndReturn(date, 'yyyy/MM/dd');
  // }
}

function nowByTS() {
  return new Date();
}

function dayBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }

  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (24 * 3600 * 1000));
  return days;
}

function monthBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (30 * 24 * 3600 * 1000));
  return days;
}

function yearBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
    console.log("date1", date1);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
    console.log("date1", date2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (365 * 24 * 3600 * 1000));
  return days;
}

console.log('customizedLogicJavascript.js loaded successfully');
