import 'dart:io';

import 'package:asset_force_mobile_v2/core/network/interceptors/base_url_interceptor.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/error_interceptor.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/request_interceptor.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/response_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

class DioUtil {
  // 单例模式
  static final DioUtil _instance = DioUtil._internal();

  factory DioUtil() {
    return _instance;
  }

  late Dio _dio;

  DioUtil._internal() {
    _dio = Dio(
      BaseOptions(
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
        headers: {'Content-Type': 'application/json', 'Accept': 'application/json'},
      ),
    );

    _dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient()
          ..findProxy = (uri) {
            return 'PROXY ***********:9090';
          }
          ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
        return client;
      },
    );

    // 添加拦截器
    _dio.interceptors.add(BaseUrlInterceptor());
    _dio.interceptors.add(RequestInterceptor());
    _dio.interceptors.add(ResponseInterceptor());
    _dio.interceptors.add(ErrorInterceptor());
  }

  /// 获取 Dio 实例
  Dio get dio => _dio;

  /// 发起 GET 请求
  Future<Response> get(String path, {Map<String, dynamic>? queryParams}) async {
    return await _dio.get(path, queryParameters: queryParams);
  }

  /// 发起 POST 请求
  Future<Response> post(String path, {dynamic data, bool useFormUrlEncoded = false}) async {
    return await _dio.post(
      path,
      data: useFormUrlEncoded ? FormData.fromMap(data) : data,
      options: Options(contentType: useFormUrlEncoded ? Headers.formUrlEncodedContentType : Headers.jsonContentType),
    );
  }

  /// 发起 PUT 请求
  Future<Response> put(String path, {dynamic data}) async {
    return await _dio.put(path, data: data);
  }

  /// 发起 DELETE 请求
  Future<Response> delete(String path, {dynamic data, Map<String, dynamic>? queryParameters}) async {
    return await _dio.delete(path, data: data, queryParameters: queryParameters);
  }

  /// 上传文件字节数组
  Future<Response> uploadBytes(
    String path, {
    required List<int> bytes,
    Map<String, dynamic>? headers,
    ProgressCallback? onSendProgress,
  }) async {
    return await _dio.put(
      path,
      data: bytes,
      options: Options(headers: {'Content-Type': 'application/octet-stream', if (headers != null) ...headers}),
      onSendProgress: onSendProgress,
    );
  }
}
