import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';

/// JavaScript 与 Flutter 之间的桥接服务
///
/// 负责处理从 JavaScript 调用 Flutter 函数的请求
/// 提供统一的函数调用接口和错误处理机制
class JsFlutterBridgeService {
  /// 函数注册表
  final JsBridgeFunctionRegistry _functionRegistry;

  /// 数据管理器
  final JsBridgeDataManager _dataManager;

  /// 状态管理器
  final JsBridgeStateManager _stateManager;

  /// UI管理器
  final JsBridgeUiManager _uiManager;

  /// 资产仓库
  final AssetRepository _assetRepository;

  /// 构造函数，支持依赖注入
  JsFlutterBridgeService({
    required JsBridgeFunctionRegistry functionRegistry,
    required JsBridgeDataManager dataManager,
    required JsBridgeStateManager stateManager,
    required JsBridgeUiManager uiManager,
    required AssetRepository assetRepository,
  }) : _functionRegistry = functionRegistry,
       _dataManager = dataManager,
       _stateManager = stateManager,
       _uiManager = uiManager,
       _assetRepository = assetRepository;

  /// 设置关联的控制器
  ///
  /// 参数:
  /// * [controller] - 要关联的 AfCustomizeViewController 实例
  void setController(AfCustomizeViewController controller) {
    _dataManager.setController(controller);
  }

  /// 刷新项目名称缓存
  ///
  /// 当 assetDict 数据发生变化时调用，确保缓存与实际数据同步
  void refreshItemNameCache() {
    _dataManager.refreshItemNameCache();
  }

  /// 清理资源
  ///
  /// 释放缓存和控制器引用
  void dispose() {
    _dataManager.dispose();
  }

  /// 清除关联的控制器
  void clearController() {
    _dataManager.clearController();
  }

  /// 检查控制器是否已设置
  bool get hasController => _dataManager.hasController;

  /// 获取控制器实例
  AfCustomizeViewController? get controller => _dataManager.controller;

  /// 设置批量更新管理器
  /// 由当前执行的 JsExecutor 调用，设置其批量更新管理器
  void setBatchUpdateManager(BatchUpdateManager? manager) {
    _stateManager.setBatchUpdateManager(manager);
  }

  /// 设置代码执行完成回调函数
  /// 由 JsExecutor 调用，用于在代码执行成功时通知执行器
  void setCodeExecutionSuccessCallback(void Function()? callback) {
    _stateManager.setCodeExecutionSuccessCallback(callback);
  }

  /// 清除批量更新管理器
  void clearBatchUpdateManager() {
    _stateManager.clearBatchUpdateManager();
  }

  /// 检查是否有批量更新管理器
  bool get hasBatchUpdateManager => _stateManager.hasBatchUpdateManager;

  /// 设置克隆数据模式
  ///
  /// 参数:
  /// * [enabled] - 是否启用克隆数据模式
  /// * [clonedData] - 克隆的数据字典（启用时传入，禁用时传入 null）
  ///
  /// 功能说明:
  /// 当启用克隆数据模式时，setValue 和 getValue 等操作都在克隆数据上进行，
  /// 不会影响真实数据。当禁用时，需要手动同步变化到真实数据。
  void setClonedDataMode(bool enabled, dynamic clonedData) {
    _stateManager.setClonedDataMode(enabled, clonedData);
  }

  /// 调用指定的 Flutter 函数
  ///
  /// 参数:
  /// * [functionName] - JavaScript 中调用的函数名
  /// * [params] - 传递给函数的参数列表
  ///
  /// 返回:
  /// * [Future<dynamic>] - 函数执行结果
  ///
  /// 异常:
  /// * 如果函数不存在，返回错误信息
  /// * 如果函数执行失败，返回错误信息
  Future<dynamic> callFunction(String functionName, List<dynamic> params) async {
    try {
      // 检查函数是否支持
      if (!_functionRegistry.isFunctionSupported(functionName)) {
        final errorMsg = '不支持的函数: $functionName';
        LogUtil.w('JsFlutterBridgeService: $errorMsg');
        return {'error': errorMsg, 'code': 'FUNCTION_NOT_FOUND'};
      }

      // 根据函数名调用对应的实现
      switch (functionName) {
        // 消息框
        case 'showAlert':
        case 'showMsg':
          return await _uiManager.showAlert(params);

        // 数据操作函数
        case 'setValue':
          return await _setValue(params);
        case 'getValue':
          return await _getValue(params);
        case 'getMasterValue':
          return await _getMasterValue(params);

        // 读写控制函数
        case 'readonly':
          return await _readonly(params);
        case 'canWrite':
          return await _canWrite(params);

        // 消息显示函数
        case 'showItemMsg':
          return await _showItemMsg(params);
        case 'clearItemMsg':
          return await _clearItemMsg(params);

        // 资产操作函数 - 需要 Flutter HTTP 服务和状态管理
        case 'getAsset':
          return await _getAsset(params);
        case 'updateAsset':
          return await _updateAsset(params);
        case 'insertAsset':
          return await _insertAsset(params);
        case 'getAssetId':
          return await _getAssetId(params);
        case 'getAssetsByType':
          return await _getAssetsByType(params);
        case 'saveAssetdata':
          return await _saveAssetdata(params);
        case 'insertAssetData':
          return await _insertAssetData(params);

        // 工作流函数 - 需要 Flutter 服务集成
        case 'getWorkflowAssetList':
          return await _getWorkflowAssetList(params);
        case 'getAction':
          return await _getAction(params);
        case 'getWorkflowTaskName':
          return await _getWorkflowTaskName(params);

        // 附属信息函数 - 需要 Flutter HTTP 服务
        case 'getAppurtenancesInformation':
          return await _getAppurtenancesInformation(params);
        case 'insertAppurtenancesInformation':
          return await _insertAppurtenancesInformation(params);
        case 'updateAppurtenancesInformation':
          return await _updateAppurtenancesInformation(params);
        case 'getAppurtenancesInformationId':
          return await _getAppurtenancesInformationId(params);
        case 'getAppurtenancesInformationList':
          return await _getAppurtenancesInformationList(params);

        // 主数据函数 - 需要 Flutter HTTP 服务和复杂数据处理
        case 'generateMaster':
          return await _generateMaster(params);
        case 'getMasterValueList':
          return await _getMasterValueList(params);

        // 界面控制函数 - 需要 Flutter UI 状态管理
        case 'toggleSection':
          return await _toggleSection(params);
        case 'changeListItem':
          return await _changeListItem(params);

        // 复杂工具函数 - 需要 Flutter 服务集成
        case 'getUserInfo':
          return await _getUserInfo(params);

        // SQLクエリ結果を取得
        case 'getSQLQueryResults':
          return await _getSQLQueryResults(params);

        // OpenAPIを呼び出し
        case 'callOpenAPI':
          return await _callOpenAPI(params);

        // APIプロキシ呼び出し
        case 'apiProxy':
          return await _apiProxy(params);

        // 日付フォーマット変換
        case 'dateFormat':
          return await _dateFormat(params);
        case 'now':
          return await _now(params);

        // 代码执行函数 - 需要 Flutter 服务集成
        case 'code_execution_start':
          return await _codeExecutionStart(params);
        case 'code_execution_success':
          return await _codeExecutionSuccess(params);

        default:
          final errorMsg = '函数实现未找到: $functionName';
          LogUtil.e('JsFlutterBridgeService: $errorMsg');
          return {'error': errorMsg, 'code': 'IMPLEMENTATION_NOT_FOUND'};
      }
    } catch (e, stackTrace) {
      final errorMsg = '函数调用失败: $functionName - $e';
      LogUtil.e('JsFlutterBridgeService: $errorMsg', error: e, stackTrace: stackTrace);
      return {'error': errorMsg, 'code': 'EXECUTION_ERROR'};
    }
  }

  // ==================== 数据操作函数 ====================

  /// 设置项目值
  ///
  /// 参数格式: [itemName, itemValue]
  ///
  /// 功能说明:
  /// 1. 根据 itemName 在 assetDict 中查找对应的项目
  /// 2. 更新项目的 defaultData、valueForShow 和 itemValue
  /// 3. 触发重新计算和 JavaScript 执行
  /// 4. 通知 Flutter UI 更新
  Future<dynamic> _setValue(List<dynamic> params) async {
    try {
      // 检查控制器是否已设置
      if (!hasController) {
        final errorMsg = '控制器未设置，无法执行 setValue';
        LogUtil.w('JsFlutterBridgeService: $errorMsg');
        return {'error': errorMsg, 'code': 'CONTROLLER_NOT_SET'};
      }

      // 参数验证
      if (params.length < 2) {
        final errorMsg = 'setValue 参数不足，需要 itemName 和 itemValue';
        LogUtil.w('JsFlutterBridgeService: $errorMsg');
        return {'error': errorMsg, 'code': 'INVALID_PARAMS'};
      }

      final String itemName = params[0].toString();
      final dynamic itemValue = params[1];

      // 查找并更新项目值
      final result = await _dataManager.findAndUpdateItem(itemName, itemValue);

      if (result['success'] == true) {
        return itemValue;
      } else {
        LogUtil.w('JsFlutterBridgeService: setValue 未找到项目 $itemName');
        return null;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'setValue 执行失败: $e';
      LogUtil.e('JsFlutterBridgeService: $errorMsg', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 获取项目值
  ///
  /// 参数格式: [itemName]
  ///
  /// 功能说明:
  /// 在克隆数据模式下，会从克隆的数据字典中读取值，
  /// 确保读取操作能够获取到最新的写入值，保证数据一致性
  Future<dynamic> _getValue(List<dynamic> params) async {
    if (params.isEmpty) {
      LogUtil.w('JsFlutterBridgeService: getValue 参数为空');
      return null;
    }

    final String itemName = params[0].toString();
    LogUtil.d('JsFlutterBridgeService: getValue 查找项目 - $itemName');

    // 优先从批量更新管理器获取值
    if (hasBatchUpdateManager) {
      final value = _stateManager.batchUpdateManager!.getValue(itemName);
      if (value != null) {
        LogUtil.d('JsFlutterBridgeService: 从批量更新管理器获取值 - $itemName: $value');
        return value;
      }
    } else if (_stateManager.isClonedDataMode && _stateManager.clonedItemDataDict != null) {
      // 向后兼容：从克隆数据中读取
      final value = _dataManager.getValueFromClonedData(itemName, _stateManager.clonedItemDataDict);
      if (value != null) {
        LogUtil.d('JsFlutterBridgeService: 从克隆数据获取值 - $itemName: $value');
        return value;
      }
    }

    // 查找项目并返回其当前值
    final result = await _dataManager.findItem(itemName);
    if (result['success'] == true) {
      final RxAssetItemWrapper item = result['item'];
      final value = item.defaultData;
      LogUtil.d('JsFlutterBridgeService: getValue 找到项目 $itemName，值: $value');
      return value; // 直接返回项目的原始值
    } else {
      LogUtil.w('JsFlutterBridgeService: getValue 未找到项目 $itemName，原因: ${result['reason']}');
      return null;
    }
  }

  /// 获取主数据值
  ///
  /// 参数格式: [itemName, masterItemName]
  Future<dynamic> _getMasterValue(List<dynamic> params) async {
    if (params.length < 2) {
      return '';
    }

    final String itemName = params[0].toString();
    final String masterItemName = params[1].toString();

    if (!hasController) {
      return '';
    }

    // 遍历控制器的 assetDict 查找匹配的项目
    for (final sectionName in controller!.assetDict.keys) {
      final items = controller!.assetDict[sectionName] ?? [];

      for (final item in items) {
        if (item.itemName == itemName) {
          final optionObject = item.optionObject;
          if (optionObject != null && optionObject.masterDisplayItems != null) {
            for (final masterDisplayItem in optionObject.masterDisplayItems!) {
              if (masterDisplayItem.itemName == masterItemName) {
                final display = item.defaultData.value?.display;
                if (display != null && masterDisplayItem.itemId != null) {
                  return display[masterDisplayItem.itemId] ?? '';
                }
              }
            }
          }
        }
      }
    }

    return '';
  }

  // ==================== 权限控制函数 ====================

  /// 设置只读
  ///
  /// 参数格式: [itemName]
  Future<dynamic> _readonly(List<dynamic> params) async {
    try {
      if (params.isEmpty) {
        LogUtil.w('JsFlutterBridgeService: readonly 缺少必要参数: itemName');
        return null;
      }

      final String itemName = params[0].toString();

      // 查找项目并设置只读状态
      final result = await _dataManager.findItem(itemName);
      if (result['success'] == true) {
        final RxAssetItemWrapper item = result['item'];
        item.setReadOnly(true);
        LogUtil.d('JsFlutterBridgeService: readonly 设置项目 $itemName 为只读');
        return true;
      } else {
        LogUtil.w('JsFlutterBridgeService: readonly 未找到项目 $itemName');
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'readonly 执行失败: $e';
      LogUtil.e('JsFlutterBridgeService: $errorMsg', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 设置可写
  ///
  /// 参数格式: [itemName]
  Future<dynamic> _canWrite(List<dynamic> params) async {
    try {
      if (params.isEmpty) {
        LogUtil.w('JsFlutterBridgeService: readonly 缺少必要参数: itemName');
        return null;
      }

      final String itemName = params[0].toString();

      // 查找项目并设置只读状态
      final result = await _dataManager.findItem(itemName);
      if (result['success'] == true) {
        final RxAssetItemWrapper item = result['item'];
        item.setReadOnly(false);
        LogUtil.d('JsFlutterBridgeService: readonly 设置项目 $itemName 为可写');
        return true;
      } else {
        LogUtil.w('JsFlutterBridgeService: readonly 未找到项目 $itemName');
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'readonly 执行失败: $e';
      LogUtil.e('JsFlutterBridgeService: $errorMsg', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // ==================== 消息显示函数 ====================

  /// 显示项目消息
  ///
  /// 参数格式: [itemName, msg]
  Future<dynamic> _showItemMsg(List<dynamic> params) async {
    if (params.length < 2) {
      return null;
    }

    final String itemName = params[0].toString();
    final String msg = params[1].toString();

    final result = await _dataManager.findItem(itemName);
    LogUtil.d('JsFlutterBridgeService: showItemMsg 找到项目 $itemName，值: $result');
    if (result['success'] == true) {
      if (result['item'] is RxAssetItemWrapper) {
        (result['item'] as RxAssetItemWrapper).setShowMessage(msg);
      }
    }
    return null;
  }

  /// 清除项目消息
  ///0
  /// 参数格式: [itemName]
  Future<dynamic> _clearItemMsg(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    final String itemName = params[0].toString();

    final result = await _dataManager.findItem(itemName);
    LogUtil.d('JsFlutterBridgeService: showItemMsg 找到项目 $itemName，值: $result');
    if (result['success'] == true) {
      if (result['item'] is RxAssetItemWrapper) {
        (result['item'] as RxAssetItemWrapper).clearMessage();
      }
    }

    return null;
  }

  // ==================== 资产操作函数 ====================

  /// 获取资产
  ///
  /// 参数格式: [assetId, that]
  Future<dynamic> _getAsset(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    final assetId = params[0];

    if (assetId == null || assetId.toString().isEmpty) {
      return null;
    }

    try {
      final response = await _assetRepository.getAssetById(assetId: int.parse(assetId.toString()));
      return response.arrayAsset?.isNotEmpty == true ? response.arrayAsset![0] : null;
    } catch (e, stackTrace) {
      LogUtil.e('JsFlutterBridgeService._getAsset failed: $e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 更新资产
  ///
  /// 参数格式: [asset, that]
  Future<dynamic> _updateAsset(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    // TODO: 实现更新资产的逻辑

    return null;
  }

  /// 插入资产
  ///
  /// 参数格式: [asset, that]
  Future<dynamic> _insertAsset(List<dynamic> params) async {
    if (params.isEmpty) {
      return 0; // 失败时返回 0
    }

    // TODO: 实现插入资产的逻辑

    return 0;
  }

  /// 获取资产ID
  ///
  /// 参数格式: [that]
  Future<dynamic> _getAssetId(List<dynamic> params) async {
    // TODO: 从控制器或参数中获取资产ID

    return null;
  }

  /// 按类型获取资产
  ///
  /// 参数格式: [assetTypeId, rows, that]
  Future<dynamic> _getAssetsByType(List<dynamic> params) async {
    if (params.length < 2) {
      return [];
    }

    // TODO: 实现按类型获取资产的逻辑

    return [];
  }

  /// 保存资产数据
  ///
  /// 参数格式: [assetTypeId, assetId, assetText, modifiedDate, relationAssetIdList, barcode]
  Future<dynamic> _saveAssetdata(List<dynamic> params) async {
    if (params.length < 3) {
      return null;
    }

    // TODO: 实现保存资产数据的逻辑

    return null;
  }

  /// 插入资产数据
  ///
  /// 参数格式: [assetTypeId, barcode, assetText]
  Future<dynamic> _insertAssetData(List<dynamic> params) async {
    if (params.length < 2) {
      return null;
    }

    // TODO: 实现插入资产数据的逻辑

    return null;
  }

  // ==================== 工作流函数 ====================

  /// 获取工作流资产列表
  ///
  /// 参数格式: [that]
  Future<dynamic> _getWorkflowAssetList(List<dynamic> params) async {
    // TODO: 实现获取工作流资产列表的逻辑

    return [];
  }

  /// 获取动作
  ///
  /// 参数格式: [that]
  Future<dynamic> _getAction(List<dynamic> params) async {
    // TODO: 从控制器获取 actionName

    return null;
  }

  /// 获取工作流任务名
  ///
  /// 参数格式: [that]
  Future<dynamic> _getWorkflowTaskName(List<dynamic> params) async {
    // TODO: 从控制器获取 stepName

    return null;
  }

  // ==================== 附属信息函数 ====================

  /// 获取附属信息
  ///
  /// 参数格式: [assetId, appurtenanceTypeId, count, that]
  Future<dynamic> _getAppurtenancesInformation(List<dynamic> params) async {
    if (params.length < 3) {
      return [];
    }

    // TODO: 实现获取附属信息的逻辑

    return [];
  }

  /// 插入附属信息
  ///
  /// 参数格式: [appurtenance, that]
  Future<dynamic> _insertAppurtenancesInformation(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    // TODO: 实现插入附属信息的逻辑

    return null;
  }

  /// 更新附属信息
  ///
  /// 参数格式: [appurtenance, that]
  Future<dynamic> _updateAppurtenancesInformation(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    // TODO: 实现更新附属信息的逻辑

    return null;
  }

  /// 获取附属信息ID
  ///
  /// 参数格式: [that]
  Future<dynamic> _getAppurtenancesInformationId(List<dynamic> params) async {
    // TODO: 从控制器获取 appurtenancesInformationId

    return null;
  }

  /// 获取附属信息列表
  ///
  /// 参数格式: [appurtenancesInformationId, that]
  Future<dynamic> _getAppurtenancesInformationList(List<dynamic> params) async {
    if (params.isEmpty) {
      return null;
    }

    // TODO: 实现获取附属信息列表的逻辑

    return null;
  }

  // ==================== 主数据函数 ====================

  /// 生成主数据
  ///
  /// 参数格式: [assetTypeId, masterTypeId, masterId, that]
  Future<dynamic> _generateMaster(List<dynamic> params) async {
    if (params.length < 3) {
      return null;
    }

    // TODO: 实现生成主数据的逻辑

    return null;
  }

  /// 获取主数据值列表
  ///
  /// 参数格式: [masterTypeId, that]
  Future<dynamic> _getMasterValueList(List<dynamic> params) async {
    if (params.isEmpty) {
      return [];
    }

    final masterTypeId = params[0];

    if (masterTypeId == null) {
      return [];
    }

    // TODO: 实现获取主数据值列表的逻辑

    return [];
  }

  /// 获取支持的函数列表
  ///
  /// 返回:
  /// * [List<String>] - 支持的函数名列表
  List<String> getSupportedFunctions() {
    return _functionRegistry.getSupportedFunctions();
  }

  // ==================== 工具函数 ====================

  /// 获取用户信息
  ///
  /// 参数格式: []
  Future<dynamic> _getUserInfo(List<dynamic> params) async {
    // JavaScript 版本返回用户信息对象
    // return {
    //   userDisplayName: lastName + ' ' + firstName,
    //   userId: userIdLocal,
    //   userName: userName,
    // };

    // TODO: 从实际的用户服务获取用户信息
    return {'userDisplayName': 'User Name', 'userId': 0, 'userName': 'username'};
  }

  // ==================== 界面控制函数 ====================

  /// 切换区域显示状态 - 需要 Flutter UI 状态管理
  ///
  /// 参数格式: [sectionName, isShowSection, that, instance]
  /// 功能: 控制页面区域(section)的显示/隐藏，影响Flutter页面渲染
  Future<dynamic> _toggleSection(List<dynamic> params) async {
    if (params.length < 2) {
      return null;
    }

    // TODO: 实现切换区域显示状态的逻辑

    return null;
  }

  /// 动态更新列表项选项 - 需要 Flutter UI 状态管理
  ///
  /// 参数格式: [itemName, list, that, instance]
  /// 功能: 动态更新列表项的选项数据，影响Flutter组件选项列表
  Future<dynamic> _changeListItem(List<dynamic> params) async {
    if (params.length < 2) {
      return null;
    }

    // TODO: 实现动态更新列表项选项的逻辑

    return null;
  }

  /// 检查函数是否支持
  ///
  /// 参数:
  /// * [functionName] - 要检查的函数名
  ///
  /// 返回:
  /// * [bool] - 是否支持该函数
  bool isFunctionSupported(String functionName) {
    return _functionRegistry.isFunctionSupported(functionName);
  }

  /// 代码执行成功
  Future<dynamic> _codeExecutionSuccess(List params) async {
    await _stateManager.handleCodeExecutionSuccess();
    return '';
  }

  /// 代码执行开始
  Future<dynamic> _codeExecutionStart(List params) async {
    _stateManager.handleCodeExecutionStart();
    return '';
  }

  /// 日付フォーマット変換
  Future<dynamic> _dateFormat(List params) async {
    if (params.length < 2) {
      return null;
    }
  }

  /// 現在日付を取得
  Future<dynamic> _now(List params) async {
    return DateTime.now().toIso8601String();
  }

  /// SQLクエリ結果を取得
  Future<dynamic> _getSQLQueryResults(List params) async {
    if (params.length < 2) {
      return null;
    }
  }

  /// APIプロキシ呼び出し
  Future<dynamic> _apiProxy(List params) async {
    if (params.length < 2) {
      return null;
    }
  }

  /// OpenAPIを呼び出し
  Future<dynamic> _callOpenAPI(List params) async {
    if (params.length < 2) {
      return null;
    }
  }
}
