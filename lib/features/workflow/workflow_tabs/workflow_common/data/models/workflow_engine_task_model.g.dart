// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_engine_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowEngineTaskModel _$WorkflowEngineTaskModelFromJson(
  Map<String, dynamic> json,
) => WorkflowEngineTaskModel(
  processInstanceId: json['processInstanceId'] as String?,
  processDefinitionId: json['processDefinitionId'] as String?,
  wfName: json['wfName'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  workflowName: json['workflowName'] as String?,
  id: json['id'] as String?,
  name: json['name'] as String?,
  state: json['state'] as String?,
  createdDate: json['createdDate'] as String?,
  endDate: json['endDate'] as String?,
  assignedBy: json['assignedBy'] as String?,
  approvedBy: json['approvedBy'] as String?,
  approvedByUserId: json['approvedByUserId'] as String?,
  wfState: json['wfState'] as String?,
  assetListTitle: json['assetListTitle'] as String?,
  deadlineDate: json['deadlineDate'] as String?,
  wfExpireTime: json['wfExpireTime'] as String?,
  workflowType: json['workflowType'] as String?,
  workflowTypeCode: json['workflowTypeCode'] as String?,
  firstWorkflowAssetTypeName: json['firstWorkflowAssetTypeName'] as String?,
  taskDefKey: json['taskDefKey'] as String?,
  assetTypeIdWithFirstWf: (json['assetTypeIdWithFirstWf'] as num?)?.toInt(),
  isMyselfInputTask: json['isMyselfInputTask'] as bool?,
);

Map<String, dynamic> _$WorkflowEngineTaskModelToJson(
  WorkflowEngineTaskModel instance,
) => <String, dynamic>{
  'processInstanceId': instance.processInstanceId,
  'processDefinitionId': instance.processDefinitionId,
  'wfName': instance.wfName,
  'workflowId': instance.workflowId,
  'workflowName': instance.workflowName,
  'id': instance.id,
  'name': instance.name,
  'state': instance.state,
  'createdDate': instance.createdDate,
  'endDate': instance.endDate,
  'assignedBy': instance.assignedBy,
  'approvedBy': instance.approvedBy,
  'approvedByUserId': instance.approvedByUserId,
  'wfState': instance.wfState,
  'assetListTitle': instance.assetListTitle,
  'deadlineDate': instance.deadlineDate,
  'wfExpireTime': instance.wfExpireTime,
  'workflowType': instance.workflowType,
  'workflowTypeCode': instance.workflowTypeCode,
  'firstWorkflowAssetTypeName': instance.firstWorkflowAssetTypeName,
  'taskDefKey': instance.taskDefKey,
  'assetTypeIdWithFirstWf': instance.assetTypeIdWithFirstWf,
  'isMyselfInputTask': instance.isMyselfInputTask,
};
