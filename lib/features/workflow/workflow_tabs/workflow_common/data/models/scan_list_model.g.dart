// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scan_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScanListModel _$ScanListModelFromJson(
  Map<String, dynamic> json,
) => ScanListModel(
  registeredAssetListModel:
      (json['registeredAssetListModel'] as List<dynamic>?)
          ?.map(
            (e) => RegisteredAssetListModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  scanType: $enumDecodeNullable(_$ScanTypeEnumMap, json['scanType']),
  assetTypeId: json['assetTypeId'] as String?,
);

Map<String, dynamic> _$ScanListModelToJson(ScanListModel instance) =>
    <String, dynamic>{
      'registeredAssetListModel':
          instance.registeredAssetListModel?.map((e) => e.toJson()).toList(),
      'scanType': _$ScanTypeEnumMap[instance.scanType],
      'assetTypeId': instance.assetTypeId,
    };

const _$ScanTypeEnumMap = {
  ScanType.barCode: 'barCode',
  ScanType.RFID: 'RFID',
  ScanType.ocrSerialNo: 'ocrSerialNo',
};

HomeImageModel _$HomeImageModelFromJson(Map<String, dynamic> json) =>
    HomeImageModel(
      uploadDate: json['uploadDate'] as String?,
      uploadUserName: json['uploadUserName'] as String?,
      fileName: json['fileName'] as String?,
      lastModifiedDate: json['lastModifiedDate'] as String?,
      size: json['size'] as String?,
      isHomeImage: json['isHomeImage'] as bool?,
      url: json['url'] as String?,
      turl: json['turl'] as String?,
    );

Map<String, dynamic> _$HomeImageModelToJson(HomeImageModel instance) =>
    <String, dynamic>{
      'uploadDate': instance.uploadDate,
      'uploadUserName': instance.uploadUserName,
      'fileName': instance.fileName,
      'lastModifiedDate': instance.lastModifiedDate,
      'size': instance.size,
      'isHomeImage': instance.isHomeImage,
      'url': instance.url,
      'turl': instance.turl,
    };

RegisteredAssetListModel _$RegisteredAssetListModelFromJson(
  Map<String, dynamic> json,
) =>
    RegisteredAssetListModel(
        dependentRelationAssetIdList:
            json['dependentRelationAssetIdList'] as List<dynamic>?,
        externalCode: json['externalCode'] as String?,
        willChangedAmount: (json['willChangedAmount'] as num?)?.toInt(),
        assetName: json['assetName'] as String?,
        layoutNo: json['layoutNo'] as String?,
        modifiedById: json['modifiedById'] as String?,
        assetScanedCount: _rxIntFromJson(json['assetScanedCount']),
        rfid: json['rfid'] as String?,
        tenantId: json['tenantId'] as String?,
        copyAppurtenancesInformationTypeIds:
            json['copyAppurtenancesInformationTypeIds'] as String?,
        assetId: (json['assetId'] as num?)?.toInt(),
        state: json['state'] as String?,
        createdById: json['createdById'] as String?,
        barcode: json['barcode'] as String?,
        jurisdiction: json['jurisdiction'] as String?,
        assetReservationStatusList:
            json['assetReservationStatusList'] as List<dynamic>?,
        assetAmount: _rxIntFromJson(json['assetAmount']),
        insertAssetRelationColumnData:
            json['insertAssetRelationColumnData'] as List<dynamic>?,
        workflowId: (json['workflowId'] as num?)?.toInt(),
        assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
        relationAssetDataList: json['relationAssetDataList'] as List<dynamic>?,
        modifiedDate: json['modifiedDate'] as String?,
        groupIds: json['groupIds'] as String?,
        assetLocation: json['assetLocation'] as String?,
        relationNotUpdateFlag: json['relationNotUpdateFlag'] as bool?,
        relationAssetIdList: json['relationAssetIdList'] as List<dynamic>?,
        relationFlg: json['relationFlg'] as bool?,
        message: json['message'] as String?,
        createdDate: json['createdDate'] as String?,
        assetItemList:
            (json['assetItemList'] as List<dynamic>?)
                ?.map(
                  (e) => SharedAssetItem.fromJson(e as Map<String, dynamic>),
                )
                .toList(),
        interactionOperation: json['interactionOperation'] as String?,
        assetTypeName: json['assetTypeName'] as String?,
        homeImage:
            json['homeImage'] == null
                ? null
                : HomeImageModel.fromJson(
                  json['homeImage'] as Map<String, dynamic>,
                ),
        assetText: json['assetText'],
        savedAssetAmount: (json['savedAssetAmount'] as num?)?.toInt(),
        mpFlag: json['mpFlag'] as String?,
        scanState: _rxStringFromJson(json['scanState']),
        processStepName: json['processStepName'] as String?,
        step: json['step'] as String?,
        status: json['status'] as String?,
        isSelect: json['isSelect'] as bool?,
      )
      ..assetTotalCount = (json['assetTotalCount'] as num?)?.toInt()
      ..tempAssetCount = (json['tempAssetCount'] as num?)?.toInt()
      ..assetAmountBeforeScan = (json['assetAmountBeforeScan'] as num?)?.toInt()
      ..isApprovalSys = json['isApprovalSys'] as bool?
      ..oldAssetAmount = (json['oldAssetAmount'] as num?)?.toInt();

Map<String, dynamic> _$RegisteredAssetListModelToJson(
  RegisteredAssetListModel instance,
) => <String, dynamic>{
  'layoutNo': instance.layoutNo,
  'modifiedById': instance.modifiedById,
  'assetScanedCount': _rxIntToJson(instance.assetScanedCount),
  'assetTotalCount': instance.assetTotalCount,
  'rfid': instance.rfid,
  'tenantId': instance.tenantId,
  'copyAppurtenancesInformationTypeIds':
      instance.copyAppurtenancesInformationTypeIds,
  'assetId': instance.assetId,
  'state': instance.state,
  'createdById': instance.createdById,
  'barcode': instance.barcode,
  'jurisdiction': instance.jurisdiction,
  'assetReservationStatusList': instance.assetReservationStatusList,
  'assetAmount': _rxIntToJson(instance.assetAmount),
  'tempAssetCount': instance.tempAssetCount,
  'insertAssetRelationColumnData': instance.insertAssetRelationColumnData,
  'workflowId': instance.workflowId,
  'assetTypeId': instance.assetTypeId,
  'relationAssetDataList': instance.relationAssetDataList,
  'modifiedDate': instance.modifiedDate,
  'groupIds': instance.groupIds,
  'assetLocation': instance.assetLocation,
  'relationNotUpdateFlag': instance.relationNotUpdateFlag,
  'relationAssetIdList': instance.relationAssetIdList,
  'relationFlg': instance.relationFlg,
  'message': instance.message,
  'createdDate': instance.createdDate,
  'dependentRelationAssetIdList': instance.dependentRelationAssetIdList,
  'externalCode': instance.externalCode,
  'willChangedAmount': instance.willChangedAmount,
  'assetName': instance.assetName,
  'assetItemList': instance.assetItemList?.map((e) => e.toJson()).toList(),
  'interactionOperation': instance.interactionOperation,
  'assetTypeName': instance.assetTypeName,
  'assetText': instance.assetText,
  'homeImage': instance.homeImage?.toJson(),
  'assetAmountBeforeScan': instance.assetAmountBeforeScan,
  'isApprovalSys': instance.isApprovalSys,
  'oldAssetAmount': instance.oldAssetAmount,
  'savedAssetAmount': instance.savedAssetAmount,
  'mpFlag': instance.mpFlag,
  'scanState': _rxStringToJson(instance.scanState),
  'processStepName': instance.processStepName,
  'step': instance.step,
  'status': instance.status,
  'isSelect': instance.isSelect,
};

ScanListItemModel _$ScanListItemModelFromJson(Map<String, dynamic> json) =>
    ScanListItemModel(
      value: json['value'] as String?,
      itemDisplayName: json['itemDisplayName'] as String?,
      itemType: json['itemType'] as String?,
      itemId: (json['itemId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ScanListItemModelToJson(ScanListItemModel instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'value': instance.value,
    };
