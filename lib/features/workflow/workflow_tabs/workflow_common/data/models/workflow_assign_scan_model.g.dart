// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_assign_scan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowAssignScanModel _$WorkflowAssignScanModelFromJson(
  Map<String, dynamic> json,
) => WorkflowAssignScanModel(
  assetListDatas:
      (json['assetListDatas'] as List<dynamic>?)
          ?.map(
            (e) => RegisteredAssetListModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  noScanningAssetCountInKeywordSearch:
      (json['noScanningAssetCountInKeywordSearch'] as num?)?.toInt(),
  allAssetCountInKeywordSearch:
      (json['allAssetCountInKeywordSearch'] as num?)?.toInt(),
  totalSavedAssetAmountCount:
      (json['totalSavedAssetAmountCount'] as num?)?.toInt(),
  totalAssetAmountCount: (json['totalAssetAmountCount'] as num?)?.toInt(),
  scannedAssetCount: (json['scannedAssetCount'] as num?)?.toInt(),
  noScanningAssetCount: (json['noScanningAssetCount'] as num?)?.toInt(),
  allAssetCount: (json['allAssetCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$WorkflowAssignScanModelToJson(
  WorkflowAssignScanModel instance,
) => <String, dynamic>{
  'assetListDatas': instance.assetListDatas?.map((e) => e.toJson()).toList(),
  'noScanningAssetCountInKeywordSearch':
      instance.noScanningAssetCountInKeywordSearch,
  'allAssetCountInKeywordSearch': instance.allAssetCountInKeywordSearch,
  'totalSavedAssetAmountCount': instance.totalSavedAssetAmountCount,
  'totalAssetAmountCount': instance.totalAssetAmountCount,
  'scannedAssetCount': instance.scannedAssetCount,
  'noScanningAssetCount': instance.noScanningAssetCount,
  'allAssetCount': instance.allAssetCount,
};
