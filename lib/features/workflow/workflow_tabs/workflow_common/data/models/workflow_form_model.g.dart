// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_form_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowFormModel _$WorkflowFormModelFromJson(Map<String, dynamic> json) =>
    WorkflowFormModel(
        tenantId: json['tenantId'] as String?,
        workflowId: (json['workflowId'] as num?)?.toInt(),
        engineId: json['engineId'] as String?,
        itemId: (json['itemId'] as num?)?.toInt() ?? 1,
        itemName: json['itemName'] as String?,
        itemDisplayName: json['itemDisplayName'] as String?,
        itemType: json['itemType'] as String?,
        option: json['option'] as String?,
        defaultData: json['defaultData'] as String?,
        inputFlg: json['inputFlg'] as String?,
        mobileFlg: json['mobileFlg'] as String?,
        sectionName: json['sectionName'] as String?,
        sectionType: json['sectionType'] as String?,
        sectionSort: (json['sectionSort'] as num?)?.toInt() ?? 1,
        positionX: (json['positionX'] as num?)?.toInt() ?? 1,
        positionY: (json['positionY'] as num?)?.toInt() ?? 1,
        width: (json['width'] as num?)?.toInt() ?? 1,
        height: (json['height'] as num?)?.toInt() ?? 1,
        createdById: json['createdById'] as String?,
        createdDate: json['createdDate'] as String?,
        modifiedById: json['modifiedById'] as String?,
        modifiedDate: json['modifiedDate'] as String?,
        itemIdStr: json['itemIdStr'] as String?,
        uneditableFlg: json['uneditableFlg'] as String?,
        DBType: json['DBType'] as String?,
      )
      ..optionObject = json['optionObject'] as Map<String, dynamic>?
      ..masterId = json['masterId'] as String?;

Map<String, dynamic> _$WorkflowFormModelToJson(WorkflowFormModel instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'workflowId': instance.workflowId,
      'engineId': instance.engineId,
      'itemId': instance.itemId,
      'itemName': instance.itemName,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'option': instance.option,
      'defaultData': instance.defaultData,
      'inputFlg': instance.inputFlg,
      'mobileFlg': instance.mobileFlg,
      'sectionName': instance.sectionName,
      'sectionType': instance.sectionType,
      'sectionSort': instance.sectionSort,
      'positionX': instance.positionX,
      'positionY': instance.positionY,
      'width': instance.width,
      'height': instance.height,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'itemIdStr': instance.itemIdStr,
      'uneditableFlg': instance.uneditableFlg,
      'DBType': instance.DBType,
      'optionObject': instance.optionObject,
      'masterId': instance.masterId,
    };
