// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_asset_column_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowAssetColumnModel _$WorkflowAssetColumnModelFromJson(
  Map<String, dynamic> json,
) => WorkflowAssetColumnModel(
  tenantId: json['tenantId'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  engineId: json['engineId'] as String?,
  workflowVersion: (json['workflowVersion'] as num?)?.toInt(),
  columnType: (json['columnType'] as num?)?.toInt(),
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  itemId: (json['itemId'] as num?)?.toInt(),
  subItemId: (json['subItemId'] as num?)?.toInt(),
  itemSort: (json['itemSort'] as num?)?.toInt(),
  itemName: json['itemName'] as String?,
  itemDisplayName: json['itemDisplayName'] as String?,
  subItemDisplayName: json['subItemDisplayName'] as String?,
  itemType: json['itemType'] as String?,
  itemOption: json['itemOption'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  subItemType: json['subItemType'] as String?,
  workflowName: json['workflowName'] as String?,
);

Map<String, dynamic> _$WorkflowAssetColumnModelToJson(
  WorkflowAssetColumnModel instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'workflowId': instance.workflowId,
  'engineId': instance.engineId,
  'workflowVersion': instance.workflowVersion,
  'columnType': instance.columnType,
  'assetTypeId': instance.assetTypeId,
  'itemId': instance.itemId,
  'subItemId': instance.subItemId,
  'itemSort': instance.itemSort,
  'itemName': instance.itemName,
  'itemDisplayName': instance.itemDisplayName,
  'subItemDisplayName': instance.subItemDisplayName,
  'itemType': instance.itemType,
  'itemOption': instance.itemOption,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'subItemType': instance.subItemType,
  'workflowName': instance.workflowName,
};
