// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_subprocess_product_asset_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowSubprocessProductAssetModel
_$WorkflowSubprocessProductAssetModelFromJson(Map<String, dynamic> json) =>
    WorkflowSubprocessProductAssetModel(
      taskId: json['taskId'] as String?,
      assetId: json['assetId'] as String?,
      stepName: json['stepName'] as String?,
      assetText: json['assetText'] as String?,
      barcode: json['barcode'] as String?,
      assignedBy: json['assignedBy'] as String?,
      updateTime: json['updateTime'] as String?,
    );

Map<String, dynamic> _$WorkflowSubprocessProductAssetModelToJson(
  WorkflowSubprocessProductAssetModel instance,
) => <String, dynamic>{
  'taskId': instance.taskId,
  'assetId': instance.assetId,
  'stepName': instance.stepName,
  'assetText': instance.assetText,
  'barcode': instance.barcode,
  'assignedBy': instance.assignedBy,
  'updateTime': instance.updateTime,
};
