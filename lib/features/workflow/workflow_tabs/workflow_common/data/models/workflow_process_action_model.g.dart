// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_process_action_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowProcessActionModel _$WorkflowProcessActionModelFromJson(
  Map<String, dynamic> json,
) => WorkflowProcessActionModel(
  tenantId: json['tenantId'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  taskDefKey: json['taskDefKey'] as String?,
  rejectionStatus: json['rejectionStatus'] as String?,
  sendBackStatus: json['sendBackStatus'] as String?,
  sendBackDestination: json['sendBackDestination'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  isPermission: json['isPermission'] as bool?,
  isSuspended: json['isSuspended'] as bool?,
  cancelStatus: json['cancelStatus'] as bool?,
  isMyselfInputTask: json['isMyselfInputTask'] as bool?,
  possibleOfClaim: json['possibleOfClaim'] as bool?,
  canBeCommitScanTask: json['canBeCommitScanTask'] as bool?,
  taskName: json['taskName'] as String?,
  workflowName: json['workflowName'] as String?,
  isMultiScanTask: json['isMultiScanTask'] as bool?,
  allMustScan: json['allMustScan'] as bool?,
  scanTaskIsLocked: json['scanTaskIsLocked'] as bool?,
  saveTemporaryButtonWithScanTask:
      json['saveTemporaryButtonWithScanTask'] as bool?,
  engineId: json['engineId'] as String?,
  newEngineId: json['newEngineId'] as String?,
);

Map<String, dynamic> _$WorkflowProcessActionModelToJson(
  WorkflowProcessActionModel instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'workflowId': instance.workflowId,
  'taskDefKey': instance.taskDefKey,
  'rejectionStatus': instance.rejectionStatus,
  'sendBackStatus': instance.sendBackStatus,
  'sendBackDestination': instance.sendBackDestination,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'isPermission': instance.isPermission,
  'isSuspended': instance.isSuspended,
  'cancelStatus': instance.cancelStatus,
  'isMyselfInputTask': instance.isMyselfInputTask,
  'possibleOfClaim': instance.possibleOfClaim,
  'canBeCommitScanTask': instance.canBeCommitScanTask,
  'taskName': instance.taskName,
  'workflowName': instance.workflowName,
  'isMultiScanTask': instance.isMultiScanTask,
  'allMustScan': instance.allMustScan,
  'scanTaskIsLocked': instance.scanTaskIsLocked,
  'saveTemporaryButtonWithScanTask': instance.saveTemporaryButtonWithScanTask,
  'engineId': instance.engineId,
  'newEngineId': instance.newEngineId,
};
