// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterDetailModel _$MasterDetailModelFromJson(Map<String, dynamic> json) =>
    MasterDetailModel(
      masterId: (json['masterId'] as num?)?.toInt(),
      tenantId: json['tenantId'] as String?,
      masterTypeId: (json['masterTypeId'] as num?)?.toInt(),
      masterText: json['masterText'] as String?,
      detailSort: (json['detailSort'] as num?)?.toInt(),
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      state: json['state'] as String?,
      count: (json['count'] as num?)?.toInt(),
      excelLineNum: (json['excelLineNum'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MasterDetailModelToJson(MasterDetailModel instance) =>
    <String, dynamic>{
      'masterId': instance.masterId,
      'tenantId': instance.tenantId,
      'masterTypeId': instance.masterTypeId,
      'masterText': instance.masterText,
      'detailSort': instance.detailSort,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'state': instance.state,
      'count': instance.count,
      'excelLineNum': instance.excelLineNum,
    };
