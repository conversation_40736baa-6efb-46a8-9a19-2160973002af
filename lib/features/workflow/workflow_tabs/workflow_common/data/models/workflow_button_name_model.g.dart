// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_button_name_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowButtonNameModel _$WorkflowButtonNameModelFromJson(
  Map<String, dynamic> json,
) => WorkflowButtonNameModel(
  tenantId: json['tenantId'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  engineId: json['engineId'] as String?,
  taskDefKey: json['taskDefKey'] as String?,
  transitButtonName: json['transitButtonName'] as String?,
  addedButtonFirst: json['addedButtonFirst'] as String?,
  addedButtonSecond: json['addedButtonSecond'] as String?,
  rejectButtonName: json['rejectButtonName'] as String?,
  sendBackButtonName: json['sendBackButtonName'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  workflowName: json['workflowName'] as String?,
);

Map<String, dynamic> _$WorkflowButtonNameModelToJson(
  WorkflowButtonNameModel instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'workflowId': instance.workflowId,
  'engineId': instance.engineId,
  'taskDefKey': instance.taskDefKey,
  'transitButtonName': instance.transitButtonName,
  'addedButtonFirst': instance.addedButtonFirst,
  'addedButtonSecond': instance.addedButtonSecond,
  'rejectButtonName': instance.rejectButtonName,
  'sendBackButtonName': instance.sendBackButtonName,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'workflowName': instance.workflowName,
};
