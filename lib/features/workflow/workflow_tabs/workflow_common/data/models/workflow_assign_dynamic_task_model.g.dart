// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_assign_dynamic_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowAssignDynamicTaskModel _$WorkflowAssignDynamicTaskModelFromJson(
  Map<String, dynamic> json,
) => WorkflowAssignDynamicTaskModel(
  sequenceId: (json['sequenceId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  procDefId: json['procDefId'] as String?,
  procInstId: json['procInstId'] as String?,
  taskDefKey: json['taskDefKey'] as String?,
  taskDefName: json['taskDefName'] as String?,
  assignDynamicType: json['assignDynamicType'] as String?,
  authorizerId: (json['authorizerId'] as num?)?.toInt(),
  authorizerName: json['authorizerName'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  assignDynamicTask: json['assignDynamicTask'] as String?,
  elementClassification: json['elementClassification'] as String?,
  createdById: (json['createdById'] as num?)?.toInt(),
  createdDate: json['createdDate'] as String?,
  modifiedById: (json['modifiedById'] as num?)?.toInt(),
  modifiedDate: json['modifiedDate'] as String?,
);

Map<String, dynamic> _$WorkflowAssignDynamicTaskModelToJson(
  WorkflowAssignDynamicTaskModel instance,
) => <String, dynamic>{
  'sequenceId': instance.sequenceId,
  'tenantId': instance.tenantId,
  'procDefId': instance.procDefId,
  'procInstId': instance.procInstId,
  'taskDefKey': instance.taskDefKey,
  'taskDefName': instance.taskDefName,
  'assignDynamicType': instance.assignDynamicType,
  'authorizerId': instance.authorizerId,
  'authorizerName': instance.authorizerName,
  'workflowId': instance.workflowId,
  'assignDynamicTask': instance.assignDynamicTask,
  'elementClassification': instance.elementClassification,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
