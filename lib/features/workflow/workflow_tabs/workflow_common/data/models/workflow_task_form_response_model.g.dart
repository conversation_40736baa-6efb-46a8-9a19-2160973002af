// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_task_form_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowTaskFormResponseModel _$WorkflowTaskFormResponseModelFromJson(
  Map<String, dynamic> json,
) => WorkflowTaskFormResponseModel(
  form:
      (json['form'] as List<dynamic>?)
          ?.map(
            (e) => WorkflowEngineTaskFormItemModel.fromJson(
              e as Map<String, dynamic>,
            ),
          )
          .toList(),
  actions:
      json['actions'] == null
          ? null
          : WorkflowProcessActionModel.fromJson(
            json['actions'] as Map<String, dynamic>,
          ),
  workflowButtonName:
      json['workflowButtonName'] == null
          ? null
          : WorkflowButtonNameModel.fromJson(
            json['workflowButtonName'] as Map<String, dynamic>,
          ),
  assetListDatas:
      (json['assetListDatas'] as List<dynamic>?)
          ?.map(
            (e) =>
                WorkflowAssetListDataModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  noScanningAssetCount: (json['noScanningAssetCount'] as num?)?.toInt(),
  allAssetCount: (json['allAssetCount'] as num?)?.toInt(),
  taskType: json['taskType'] as String?,
  assetTypeName: json['assetTypeName'] as String?,
  assetListId: (json['assetListId'] as num?)?.toInt(),
  inputAssetListFlag: json['inputAssetListFlag'] as String?,
  workflowScript: json['workflowScript'] as String?,
  autoFetchSearchId: (json['autoFetchSearchId'] as num?)?.toInt(),
  assignDynamicType: json['assignDynamicType'] as String?,
  workflowEngineAmountProcess:
      json['workflowEngineAmountProcess'] == null
          ? null
          : WorkflowEngineAmountProcessModel.fromJson(
            json['workflowEngineAmountProcess'] as Map<String, dynamic>,
          ),
  workflowLogicScript: json['workflowLogicScript'] as String?,
  commonJS: json['commonJS'] as String?,
  assetListTitle: json['assetListTitle'] as String?,
  assetTypeIdWithMultiScan: (json['assetTypeIdWithMultiScan'] as num?)?.toInt(),
  assignDynamicFlag: json['assignDynamicFlag'] as bool?,
  currentTaskInSubProcess: json['currentTaskInSubProcess'] as bool?,
  currentUserSubProcessTaskIsCompleted:
      json['currentUserSubProcessTaskIsCompleted'] as bool?,
  hasPassedSubProcess: json['hasPassedSubProcess'] as bool?,
  hasScanTask: json['hasScanTask'] as bool?,
  hasSubProcess: json['hasSubProcess'] as bool?,
  isFirstWfWithAssetList: json['isFirstWfWithAssetList'] as bool?,
  isRentalWorkflow: json['isRentalWorkflow'] as bool?,
  nonSubProcess: json['nonSubProcess'] as bool?,
  noScanningAssetList:
      (json['noScanningAssetList'] as List<dynamic>?)
          ?.map(
            (e) =>
                WorkflowAssetListDataModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  productAssetTypeId: (json['productAssetTypeId'] as num?)?.toInt(),
  productAssetTypeName: json['productAssetTypeName'] as String?,
  updateAmountShow: json['updateAmountShow'] as bool?,
  reportInfoList:
      (json['reportInfoList'] as List<dynamic>?)
          ?.map((e) => WorkflowFormOfReport.fromJson(e as Map<String, dynamic>))
          .toList(),
  subformId: (json['subformId'] as num?)?.toInt(),
  subformVersion: (json['subformVersion'] as num?)?.toInt(),
  subProcessProductAssetList:
      (json['subProcessProductAssetList'] as List<dynamic>?)
          ?.map(
            (e) => WorkflowSubprocessProductAssetModel.fromJson(
              e as Map<String, dynamic>,
            ),
          )
          .toList(),
  subProcessReportInfoList:
      (json['subProcessReportInfoList'] as List<dynamic>?)
          ?.map((e) => WorkflowFormOfReport.fromJson(e as Map<String, dynamic>))
          .toList(),
  thirdWfProcessDefinitionId: json['thirdWfProcessDefinitionId'] as String?,
  totalAssetAmountCount: (json['totalAssetAmountCount'] as num?)?.toInt(),
  totalSavedAssetAmountCount:
      (json['totalSavedAssetAmountCount'] as num?)?.toInt(),
  wfAssignDynamicTasks:
      (json['wfAssignDynamicTasks'] as List<dynamic>?)
          ?.map(
            (e) => WorkflowAssignDynamicTaskModel.fromJson(
              e as Map<String, dynamic>,
            ),
          )
          .toList(),
  workflowState: json['workflowState'] as String?,
);

Map<String, dynamic> _$WorkflowTaskFormResponseModelToJson(
  WorkflowTaskFormResponseModel instance,
) => <String, dynamic>{
  'form': instance.form,
  'actions': instance.actions,
  'workflowButtonName': instance.workflowButtonName,
  'assetListDatas': instance.assetListDatas,
  'noScanningAssetCount': instance.noScanningAssetCount,
  'allAssetCount': instance.allAssetCount,
  'taskType': instance.taskType,
  'thirdWfProcessDefinitionId': instance.thirdWfProcessDefinitionId,
  'assetListId': instance.assetListId,
  'assetListTitle': instance.assetListTitle,
  'isFirstWfWithAssetList': instance.isFirstWfWithAssetList,
  'workflowEngineAmountProcess': instance.workflowEngineAmountProcess,
  'isRentalWorkflow': instance.isRentalWorkflow,
  'nonSubProcess': instance.nonSubProcess,
  'workflowState': instance.workflowState,
  'noScanningAssetList': instance.noScanningAssetList,
  'assetTypeName': instance.assetTypeName,
  'reportInfoList': instance.reportInfoList,
  'assetTypeIdWithMultiScan': instance.assetTypeIdWithMultiScan,
  'inputAssetListFlag': instance.inputAssetListFlag,
  'hasScanTask': instance.hasScanTask,
  'updateAmountShow': instance.updateAmountShow,
  'wfAssignDynamicTasks': instance.wfAssignDynamicTasks,
  'assignDynamicFlag': instance.assignDynamicFlag,
  'hasSubProcess': instance.hasSubProcess,
  'subProcessProductAssetList': instance.subProcessProductAssetList,
  'productAssetTypeName': instance.productAssetTypeName,
  'productAssetTypeId': instance.productAssetTypeId,
  'currentTaskInSubProcess': instance.currentTaskInSubProcess,
  'currentUserSubProcessTaskIsCompleted':
      instance.currentUserSubProcessTaskIsCompleted,
  'subformId': instance.subformId,
  'subformVersion': instance.subformVersion,
  'hasPassedSubProcess': instance.hasPassedSubProcess,
  'assignDynamicType': instance.assignDynamicType,
  'subProcessReportInfoList': instance.subProcessReportInfoList,
  'totalSavedAssetAmountCount': instance.totalSavedAssetAmountCount,
  'totalAssetAmountCount': instance.totalAssetAmountCount,
  'workflowScript': instance.workflowScript,
  'autoFetchSearchId': instance.autoFetchSearchId,
  'workflowLogicScript': instance.workflowLogicScript,
  'commonJS': instance.commonJS,
};

WorkflowEngineTaskFormItemModel _$WorkflowEngineTaskFormItemModelFromJson(
  Map<String, dynamic> json,
) => WorkflowEngineTaskFormItemModel(
  formField: json['formField'],
  workflowForm:
      json['workflowForm'] == null
          ? null
          : AssetItemListModel.fromJson(
            json['workflowForm'] as Map<String, dynamic>,
          ),
  variable: json['variable'],
);

Map<String, dynamic> _$WorkflowEngineTaskFormItemModelToJson(
  WorkflowEngineTaskFormItemModel instance,
) => <String, dynamic>{
  'formField': instance.formField,
  'workflowForm': instance.workflowForm,
  'variable': instance.variable,
};

WorkflowEngineAmountProcessModel _$WorkflowEngineAmountProcessModelFromJson(
  Map<String, dynamic> json,
) => WorkflowEngineAmountProcessModel(
  isUpdateAmount: json['isUpdateAmount'] as String?,
  amountActionTaskUpdateItemId: json['amountActionTaskUpdateItemId'] as String?,
  amountActionTaskUpdateItemName:
      json['amountActionTaskUpdateItemName'] as String?,
  amountActionAssetList: json['amountActionAssetList'] as String?,
  amountActionTaskExecuted: json['amountActionTaskExecuted'] as bool?,
);

Map<String, dynamic> _$WorkflowEngineAmountProcessModelToJson(
  WorkflowEngineAmountProcessModel instance,
) => <String, dynamic>{
  'isUpdateAmount': instance.isUpdateAmount,
  'amountActionTaskUpdateItemId': instance.amountActionTaskUpdateItemId,
  'amountActionTaskUpdateItemName': instance.amountActionTaskUpdateItemName,
  'amountActionAssetList': instance.amountActionAssetList,
  'amountActionTaskExecuted': instance.amountActionTaskExecuted,
};

WorkflowFormOfReport _$WorkflowFormOfReportFromJson(
  Map<String, dynamic> json,
) => WorkflowFormOfReport(
  itemName: json['itemName'] as String,
  variable: json['variable'],
);

Map<String, dynamic> _$WorkflowFormOfReportToJson(
  WorkflowFormOfReport instance,
) => <String, dynamic>{
  'itemName': instance.itemName,
  'variable': instance.variable,
};
