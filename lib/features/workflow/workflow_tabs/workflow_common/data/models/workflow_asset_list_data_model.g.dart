// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_asset_list_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowAssetListDataModel _$WorkflowAssetListDataModelFromJson(
  Map<String, dynamic> json,
) => WorkflowAssetListDataModel(
  assetListId: (json['ASSET_LIST_ID'] as num?)?.toInt(),
  assetId: (json['ASSET_ID'] as num?)?.toInt(),
  tenantId: json['TENANT_ID'] as String?,
  processId: json['PROCESS_ID'] as String?,
  processState: json['processState'] as String?,
  processStateText: json['processStateText'] as String?,
  assetText: json['ASSET_TEXT'] as String?,
  assetCount: (json['ASSET_COUNT'] as num?)?.toInt(),
  createdById: json['CREATED_BY_ID'] as String?,
  createdDate: json['CREATED_DATE'] as String?,
  modifiedById: json['MODIFIED_BY_ID'] as String?,
  modifiedDate: json['MODIFIED_DATE'] as String?,
  scanState: json['SCAN_STATE'] as String?,
  scanSort: (json['SCAN_SORT'] as num?)?.toInt(),
  scanTarget: json['scanTarget'] as bool?,
  barcode: json['barcode'] as String?,
  processStepName: json['processStepName'] as String?,
  mpFlag: json['mpFlag'] as String?,
  parentAssets: json['parentAssets'] as String?,
  assetAmount: (json['ASSET_AMOUNT'] as num?)?.toInt(),
  savedAssetAmount: (json['SAVED_ASSET_AMOUNT'] as num?)?.toInt() ?? 0,
  oldAssetText: json['oldAssetText'] as String?,
  assetItemList:
      (json['assetItemList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
  location: json['location'] as String?,
  assetScannedCount: (json['assetScannedCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$WorkflowAssetListDataModelToJson(
  WorkflowAssetListDataModel instance,
) => <String, dynamic>{
  'ASSET_LIST_ID': instance.assetListId,
  'ASSET_ID': instance.assetId,
  'TENANT_ID': instance.tenantId,
  'PROCESS_ID': instance.processId,
  'processState': instance.processState,
  'processStateText': instance.processStateText,
  'ASSET_TEXT': instance.assetText,
  'ASSET_COUNT': instance.assetCount,
  'CREATED_BY_ID': instance.createdById,
  'CREATED_DATE': instance.createdDate,
  'MODIFIED_BY_ID': instance.modifiedById,
  'MODIFIED_DATE': instance.modifiedDate,
  'SCAN_STATE': instance.scanState,
  'SCAN_SORT': instance.scanSort,
  'scanTarget': instance.scanTarget,
  'barcode': instance.barcode,
  'processStepName': instance.processStepName,
  'mpFlag': instance.mpFlag,
  'parentAssets': instance.parentAssets,
  'ASSET_AMOUNT': instance.assetAmount,
  'SAVED_ASSET_AMOUNT': instance.savedAssetAmount,
  'oldAssetText': instance.oldAssetText,
  'assetItemList': instance.assetItemList,
  'location': instance.location,
  'assetScannedCount': instance.assetScannedCount,
};
