// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_engine_amount_process_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowEngineAmountProcessModel _$WorkflowEngineAmountProcessModelFromJson(
  Map<String, dynamic> json,
) => WorkflowEngineAmountProcessModel(
  isUpdateAmount: json['isUpdateAmount'] as String?,
  amountActionTaskUpdateItemId: json['amountActionTaskUpdateItemId'] as String?,
  amountActionTaskUpdateItemName:
      json['amountActionTaskUpdateItemName'] as String?,
  amountActionAssetList: json['amountActionAssetList'] as String?,
  amountActionTaskExecuted: json['amountActionTaskExecuted'] as bool?,
);

Map<String, dynamic> _$WorkflowEngineAmountProcessModelToJson(
  WorkflowEngineAmountProcessModel instance,
) => <String, dynamic>{
  'isUpdateAmount': instance.isUpdateAmount,
  'amountActionTaskUpdateItemId': instance.amountActionTaskUpdateItemId,
  'amountActionTaskUpdateItemName': instance.amountActionTaskUpdateItemName,
  'amountActionAssetList': instance.amountActionAssetList,
  'amountActionTaskExecuted': instance.amountActionTaskExecuted,
};
