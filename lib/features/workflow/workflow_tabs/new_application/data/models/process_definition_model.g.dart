// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'process_definition_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProcessDefinitionModel _$ProcessDefinitionModelFromJson(
  Map<String, dynamic> json,
) => ProcessDefinitionModel(
  processDefinitionId: json['processDefinitionId'] as String?,
  processInstanceId: json['processInstanceId'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  workflowName: json['workflowName'] as String,
  workflowTypeCode: json['workflowTypeCode'] as String?,
  assetTypeName: json['assetTypeName'] as String?,
  assetTypeIdWithFirstWf: (json['assetTypeIdWithFirstWf'] as num?)?.toInt(),
  comment: json['comment'] as String?,
  assetActionList: json['assetActionList'] as Map<String, dynamic>?,
  firstWorkflowAssetTypeName: json['firstWorkflowAssetTypeName'] as String?,
  name: json['name'] as String?,
);

Map<String, dynamic> _$ProcessDefinitionModelToJson(
  ProcessDefinitionModel instance,
) => <String, dynamic>{
  'processDefinitionId': instance.processDefinitionId,
  'processInstanceId': instance.processInstanceId,
  'workflowId': instance.workflowId,
  'workflowName': instance.workflowName,
  'workflowTypeCode': instance.workflowTypeCode,
  'assetTypeName': instance.assetTypeName,
  'assetTypeIdWithFirstWf': instance.assetTypeIdWithFirstWf,
  'comment': instance.comment,
  'assetActionList': instance.assetActionList,
  'firstWorkflowAssetTypeName': instance.firstWorkflowAssetTypeName,
  'name': instance.name,
};
