// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_new_form_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowNewFormModel _$WorkflowNewFormModelFromJson(
  Map<String, dynamic> json,
) =>
    WorkflowNewFormModel(
        assignDynamicType: json['assignDynamicType'] as String?,
        autoFetchSearchId: (json['autoFetchSearchId'] as num?)?.toInt(),
        groupSectionFlg: json['groupSectionFlg'] as bool?,
        form:
            (json['form'] as List<dynamic>?)
                ?.map(
                  (e) => WorkflowEngineInputTaskFormItem.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList(),
        inputAssetListFlag: json['inputAssetListFlag'] as String?,
        stepName: json['stepName'] as String?,
        updateAmountShow: json['updateAmountShow'] as bool?,
        workflowEngineAmountProcess:
            json['workflowEngineAmountProcess'] == null
                ? null
                : WorkflowEngineAmountProcessModel.fromJson(
                  json['workflowEngineAmountProcess'] as Map<String, dynamic>,
                ),
        commonJS: json['commonJS'] as String?,
        workflowButtonName:
            json['workflowButtonName'] == null
                ? null
                : WorkflowButtonNameModel.fromJson(
                  json['workflowButtonName'] as Map<String, dynamic>,
                ),
        workflowLogicScript: json['workflowLogicScript'] as String?,
        workflowScript: json['workflowScript'] as String?,
      )
      ..code = (json['code'] as num?)?.toInt()
      ..msg = json['msg'] as String?;

Map<String, dynamic> _$WorkflowNewFormModelToJson(
  WorkflowNewFormModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'form': instance.form?.map((e) => e.toJson()).toList(),
  'autoFetchSearchId': instance.autoFetchSearchId,
  'stepName': instance.stepName,
  'workflowEngineAmountProcess': instance.workflowEngineAmountProcess?.toJson(),
  'inputAssetListFlag': instance.inputAssetListFlag,
  'groupSectionFlg': instance.groupSectionFlg,
  'assignDynamicType': instance.assignDynamicType,
  'updateAmountShow': instance.updateAmountShow,
  'workflowButtonName': instance.workflowButtonName?.toJson(),
  'workflowScript': instance.workflowScript,
  'workflowLogicScript': instance.workflowLogicScript,
  'commonJS': instance.commonJS,
};

WorkflowEngineInputTaskFormItem _$WorkflowEngineInputTaskFormItemFromJson(
  Map<String, dynamic> json,
) => WorkflowEngineInputTaskFormItem(
  formField: json['formField'] as bool?,
  workflowForm:
      json['workflowForm'] == null
          ? null
          : WorkflowFormModel.fromJson(
            json['workflowForm'] as Map<String, dynamic>,
          ),
  variable: json['variable'],
);

Map<String, dynamic> _$WorkflowEngineInputTaskFormItemToJson(
  WorkflowEngineInputTaskFormItem instance,
) => <String, dynamic>{
  'formField': instance.formField,
  'workflowForm': instance.workflowForm?.toJson(),
  'variable': instance.variable,
};
