// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_display_option_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterDisplayOptionModel _$MasterDisplayOptionModelFromJson(
  Map<String, dynamic> json,
) => MasterDisplayOptionModel(
  check: json['check'] as String?,
  readonly: json['readonly'] as String?,
  checkboxOptions:
      (json['checkboxOptions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  checkboxMultiFlg: json['checkboxMultiFlg'] as String?,
  radioOptions:
      (json['radioOptions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  maxlength: json['maxlength'] as String?,
  data: (json['data'] as List<dynamic>?)?.map((e) => e as String).toList(),
  numberDecimalPoint: json['numberDecimalPoint'] as String?,
  numberCommaDecimalPoint: json['numberCommaDecimalPoint'] as String?,
);

Map<String, dynamic> _$MasterDisplayOptionModelToJson(
  MasterDisplayOptionModel instance,
) => <String, dynamic>{
  'check': instance.check,
  'readonly': instance.readonly,
  'checkboxOptions': instance.checkboxOptions,
  'checkboxMultiFlg': instance.checkboxMultiFlg,
  'radioOptions': instance.radioOptions,
  'maxlength': instance.maxlength,
  'data': instance.data,
  'numberDecimalPoint': instance.numberDecimalPoint,
  'numberCommaDecimalPoint': instance.numberCommaDecimalPoint,
};
