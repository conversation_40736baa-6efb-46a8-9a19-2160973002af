// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImageModel _$ImageModelFromJson(Map<String, dynamic> json) => ImageModel(
  url: json['url'] as String?,
  turl: json['turl'] as String?,
  loaded: json['loaded'] as bool?,
  fileName: json['fileName'] as String?,
  uploadDate: json['uploadDate'] as String?,
  isHomeImage: json['isHomeImage'] as bool?,
);

Map<String, dynamic> _$ImageModelToJson(ImageModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'fileName': instance.fileName,
      'uploadDate': instance.uploadDate,
      'isHomeImage': instance.isHomeImage,
    };
