// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileModel _$FileModelFromJson(Map<String, dynamic> json) => FileModel(
  url: json['url'] as String?,
  size: (json['size'] as num?)?.toInt(),
  turl: json['turl'] as String?,
  total: (json['total'] as num?)?.toInt(),
  loaded: (json['loaded'] as num?)?.toInt(),
  status: json['status'] as String?,
  fileName: json['fileName'] as String?,
  progress: (json['progress'] as num?)?.toInt(),
  uploadDate: json['uploadDate'] as String?,
  uploadUserName: json['uploadUserName'] as String?,
);

Map<String, dynamic> _$FileModelToJson(FileModel instance) => <String, dynamic>{
  'url': instance.url,
  'size': instance.size,
  'turl': instance.turl,
  'total': instance.total,
  'loaded': instance.loaded,
  'status': instance.status,
  'fileName': instance.fileName,
  'progress': instance.progress,
  'uploadDate': instance.uploadDate,
  'uploadUserName': instance.uploadUserName,
};
