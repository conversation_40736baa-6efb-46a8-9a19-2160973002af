// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tab_barcode_scan_asset_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TabBarcodeScanAssetListModel _$TabBarcodeScanAssetListModelFromJson(
  Map<String, dynamic> json,
) => TabBarcodeScanAssetListModel(
  unregisterBarcodeList: _barcodeItemListFromJson(
    json['unregisterBarcodeList'] as List?,
  ),
  showARInfo: _fromJsonItems(json['showARInfo']),
  assetId: _fromJsonToInt(json['assetId']),
  assetTypeId: _fromJsonToInt(json['assetTypeId']),
  type: _fromJsonToInt(json['type']),
  barCode: json['barCode'] as String?,
);

Map<String, dynamic> _$TabBarcodeScanAssetListModelToJson(
  TabBarcodeScanAssetListModel instance,
) => <String, dynamic>{
  'unregisterBarcodeList': _barcodeItemListToJson(
    instance.unregisterBarcodeList,
  ),
  'showARInfo': _toJsonItems(instance.showARInfo),
  'assetId': _toJsonFromInt(instance.assetId),
  'assetTypeId': _toJsonFromInt(instance.assetTypeId),
  'type': _toJsonFromInt(instance.type),
  'barCode': instance.barCode,
};

BarcodeItem _$BarcodeItemFromJson(Map<String, dynamic> json) => BarcodeItem(
  barCode: json['barCode'] as String?,
  location: json['location'] as String?,
  isRfid: json['isRfid'] as bool?,
  isAssetScan: json['isAssetScan'] as bool?,
  rfid: json['rfid'] as String?,
  scanTechnical: json['scanTechnical'] as String?,
);

Map<String, dynamic> _$BarcodeItemToJson(BarcodeItem instance) =>
    <String, dynamic>{
      'barCode': instance.barCode,
      'location': instance.location,
      'isRfid': instance.isRfid,
      'isAssetScan': instance.isAssetScan,
      'rfid': instance.rfid,
      'scanTechnical': instance.scanTechnical,
    };

ArInfoItem _$ArInfoItemFromJson(Map<String, dynamic> json) => ArInfoItem(
  scanTechnical: json['scanTechnical'] as String?,
  assetId: _fromJsonToInt(json['assetId']),
  scanItems: _scanItemListFromJson(json['scanItems'] as List?),
  quantity: (json['quantity'] as num?)?.toInt(),
  arColor: json['arColor'] as String?,
  homeImageMobileDsiplayFlg: json['homeImageMobileDsiplayFlg'] as bool?,
  count: (json['count'] as num?)?.toInt(),
  assetName: json['assetName'] as String?,
  assetTypeId: _fromJsonToInt(json['assetTypeId']),
  barCode: json['barCode'] as String?,
  location: json['location'] as String?,
  homeImageUrlForShow: json['homeImageUrlForShow'] as String?,
);

Map<String, dynamic> _$ArInfoItemToJson(ArInfoItem instance) =>
    <String, dynamic>{
      'scanTechnical': instance.scanTechnical,
      'assetId': _toJsonFromInt(instance.assetId),
      'scanItems': _scanItemListToJson(instance.scanItems),
      'quantity': instance.quantity,
      'arColor': instance.arColor,
      'homeImageMobileDsiplayFlg': instance.homeImageMobileDsiplayFlg,
      'count': instance.count,
      'assetName': instance.assetName,
      'assetTypeId': _toJsonFromInt(instance.assetTypeId),
      'barCode': instance.barCode,
      'location': instance.location,
      'homeImageUrlForShow': instance.homeImageUrlForShow,
    };

ScanItem _$ScanItemFromJson(Map<String, dynamic> json) => ScanItem(
  itemName: json['itemName'] as String?,
  itemType: json['itemType'] as String?,
  itemVal: json['itemVal'] as String?,
  itemOption: json['itemOption'] as String?,
);

Map<String, dynamic> _$ScanItemToJson(ScanItem instance) => <String, dynamic>{
  'itemName': instance.itemName,
  'itemType': instance.itemType,
  'itemVal': instance.itemVal,
  'itemOption': instance.itemOption,
};
