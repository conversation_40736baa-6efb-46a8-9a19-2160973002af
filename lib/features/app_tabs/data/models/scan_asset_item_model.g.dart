// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scan_asset_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetInfoByAssetIdRepositoryModel _$AssetInfoByAssetIdRepositoryModelFromJson(
  Map<String, dynamic> json,
) => AssetInfoByAssetIdRepositoryModel(
  assetList:
      (json['assetList'] as List<dynamic>?)
          ?.map((e) => ScanAssetItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
);

Map<String, dynamic> _$AssetInfoByAssetIdRepositoryModelToJson(
  AssetInfoByAssetIdRepositoryModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'assetList': instance.assetList,
};

ScanAssetItemModel _$ScanAssetItemModelFromJson(Map<String, dynamic> json) =>
    ScanAssetItemModel(
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      assetTypeName: json['assetTypeName'] as String?,
      assetId: (json['assetId'] as num?)?.toInt(),
      assetName: json['assetName'] as String?,
      location: json['location'] as String?,
      barcode: json['barcode'] as String?,
      assetItemList:
          (json['assetItemList'] as List<dynamic>?)
              ?.map((e) => ShowAssetItem.fromJson(e as Map<String, dynamic>))
              .toList(),
      homeImageUrl: json['homeImageUrl'] as String?,
    );

Map<String, dynamic> _$ScanAssetItemModelToJson(ScanAssetItemModel instance) =>
    <String, dynamic>{
      'assetTypeId': instance.assetTypeId,
      'assetTypeName': instance.assetTypeName,
      'assetId': instance.assetId,
      'assetName': instance.assetName,
      'location': instance.location,
      'barcode': instance.barcode,
      'assetItemList': instance.assetItemList,
      'homeImageUrl': instance.homeImageUrl,
    };

ShowAssetItem _$ShowAssetItemFromJson(Map<String, dynamic> json) =>
    ShowAssetItem(
      itemId: (json['itemId'] as num?)?.toInt(),
      itemDisplayName: json['itemDisplayName'] as String?,
      itemType: json['itemType'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$ShowAssetItemToJson(ShowAssetItem instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'value': instance.value,
    };
