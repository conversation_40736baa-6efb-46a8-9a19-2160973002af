// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'un_permission_response_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UnPermissionResponseEntity _$UnPermissionResponseEntityFromJson(
  Map<String, dynamic> json,
) => UnPermissionResponseEntity(
  json['msg'] as String,
  (json['code'] as num).toInt(),
  (json['unPermissionList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null
                ? null
                : UnPermissionResponseUnPermissionListEntity.fromJson(
                  e as Map<String, dynamic>,
                ),
      )
      .toList(),
);

Map<String, dynamic> _$UnPermissionResponseEntityToJson(
  UnPermissionResponseEntity instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'unPermissionList': instance.unPermissionList,
};

UnPermissionResponseUnPermissionListEntity
_$UnPermissionResponseUnPermissionListEntityFromJson(
  Map<String, dynamic> json,
) => UnPermissionResponseUnPermissionListEntity(
  json['roleId'],
  json['tenantId'],
  json['type'],
  json['functionId'] as String?,
  json['pageComponent'],
  json['pageId'],
  (json['resourceId'] as num?)?.toInt(),
  json['resource'] as String?,
  json['createdById'],
  json['createdDate'],
  json['modifiedById'],
  json['modifiedDate'],
);

Map<String, dynamic> _$UnPermissionResponseUnPermissionListEntityToJson(
  UnPermissionResponseUnPermissionListEntity instance,
) => <String, dynamic>{
  'roleId': instance.roleId,
  'tenantId': instance.tenantId,
  'type': instance.type,
  'functionId': instance.functionId,
  'pageComponent': instance.pageComponent,
  'pageId': instance.pageId,
  'resourceId': instance.resourceId,
  'resource': instance.resource,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
