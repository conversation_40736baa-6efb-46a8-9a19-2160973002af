// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_item_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryItemUIModel _$CategoryItemUIModelFromJson(Map<String, dynamic> json) =>
    CategoryItemUIModel(
      categoryText: json['categoryText'] as String,
      count: (json['count'] as num).toInt(),
      value: json['value'] as String,
      isAll: json['isAll'] as bool? ?? false,
    );

Map<String, dynamic> _$CategoryItemUIModelToJson(
  CategoryItemUIModel instance,
) => <String, dynamic>{
  'categoryText': instance.categoryText,
  'count': instance.count,
  'value': instance.value,
  'isAll': instance.isAll,
};
