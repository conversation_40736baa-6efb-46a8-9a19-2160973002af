// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_search_category_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetSearchCategoryUIModel _$AssetSearchCategoryUIModelFromJson(
  Map<String, dynamic> json,
) => AssetSearchCategoryUIModel(
  subItemId: (json['subItemId'] as num?)?.toInt(),
  subItemName: json['subItemName'] as String?,
  searchData: json['searchData'] as String,
  itemId: (json['itemId'] as num).toInt(),
  itemName: json['itemName'] as String,
  searchLogic: json['searchLogic'] as String,
  method: json['method'] as String?,
);

Map<String, dynamic> _$AssetSearchCategoryUIModelToJson(
  AssetSearchCategoryUIModel instance,
) => <String, dynamic>{
  'subItemId': instance.subItemId,
  'subItemName': instance.subItemName,
  'searchData': instance.searchData,
  'itemId': instance.itemId,
  'itemName': instance.itemName,
  'searchLogic': instance.searchLogic,
  'method': instance.method,
};
