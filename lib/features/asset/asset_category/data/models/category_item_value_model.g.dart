// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_item_value_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryItemValueModel _$CategoryItemValueModelFromJson(
  Map<String, dynamic> json,
) => CategoryItemValueModel(
  assetMobileSetting: json['assetMobileSetting'] as Map<String, dynamic>?,
  arColor: json['arColor'] as String?,
  asset: json['asset'] as Map<String, dynamic>?,
  categoryItemValue: json['categoryItemValue'] as String,
  count: (json['count'] as num).toInt(),
  homeImageMobileDisplayFlg: json['homeImageMobileDsiplayFlg'] as bool,
  categoryText: json['categoryText'] as String?,
);

Map<String, dynamic> _$CategoryItemValueModelToJson(
  CategoryItemValueModel instance,
) => <String, dynamic>{
  if (instance.assetMobileSetting case final value?)
    'assetMobileSetting': value,
  'arColor': instance.arColor,
  if (instance.asset case final value?) 'asset': value,
  'categoryItemValue': instance.categoryItemValue,
  'count': instance.count,
  'homeImageMobileDsiplayFlg': instance.homeImageMobileDisplayFlg,
  'categoryText': instance.categoryText,
};
