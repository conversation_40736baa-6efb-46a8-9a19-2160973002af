// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryModel _$CategoryModelFromJson(Map<String, dynamic> json) =>
    CategoryModel(
      itemId: (json['itemId'] as num).toInt(),
      itemDisplayName: json['itemDisplayName'] as String,
      itemName: json['itemName'] as String,
      itemType: json['itemType'] as String,
      optionObj: json['optionObj'] as Map<String, dynamic>?,
      itemSubName: json['itemSubName'] as String?,
      itemSubId: (json['itemSubId'] as num?)?.toInt(),
      itemSubType: json['itemSubType'] as String?,
      subOptionObj:
          json['subOptionObj'] == null
              ? null
              : SubOptionObjModel.fromJson(
                json['subOptionObj'] as Map<String, dynamic>,
              ),
      value: json['value'] as String?,
      selectedCategoryValue: json['selectedCategoryValue'] as String?,
      subOption: json['subOption'] as String?,
      itemSubDisplayName: json['itemSubDisplayName'] as String?,
    );

Map<String, dynamic> _$CategoryModelToJson(CategoryModel instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'itemDisplayName': instance.itemDisplayName,
      'itemName': instance.itemName,
      'itemType': instance.itemType,
      'optionObj': instance.optionObj,
      'itemSubName': instance.itemSubName,
      'itemSubId': instance.itemSubId,
      'itemSubType': instance.itemSubType,
      'subOptionObj': instance.subOptionObj?.toJson(),
      'value': instance.value,
      'selectedCategoryValue': instance.selectedCategoryValue,
      'subOption': instance.subOption,
      'itemSubDisplayName': instance.itemSubDisplayName,
    };

OptionObjModel _$OptionObjModelFromJson(
  Map<String, dynamic> json,
) => OptionObjModel(
  data: (json['data'] as List<dynamic>?)?.map((e) => e as String).toList(),
  calculate: json['calculate'] as String?,
  checkboxOptions:
      (json['checkboxOptions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  checkboxMultiFlg: json['checkboxMultiFlg'] as String?,
  dateType: json['dateType'] as String?,
  calculateDecimalPoint: json['calculateDecimalPoint'],
  calculateCommaDecimalPoint: json['calculateCommaDecimalPoint'],
  unit: json['unit'] as String?,
  check: json['check'] as String?,
  readonly: json['readonly'] as String?,
  masterTypeId: (json['masterTypeId'] as num?)?.toInt(),
  masterChainFlg: json['masterChainFlg'] as String?,
  masterDisplayItems:
      (json['masterDisplayItems'] as List<dynamic>?)
          ?.map(
            (e) => MasterDisplayItemModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  sectionPrivateGroups: json['sectionPrivateGroups'] as String?,
  sectionPrivateEditGroups: json['sectionPrivateEditGroups'] as String?,
  maxlength: json['maxlength'] as String?,
  calculateType: json['calculateType'] as String?,
  currencyType: json['currencyType'] as String?,
  currencyDecimalPoint: OptionObjModel._stringify(json['currencyDecimalPoint']),
  radioOptions:
      (json['radioOptions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  color: json['color'] as String?,
  fontSize: json['fontSize'] as String?,
  fontStyle: json['fontStyle'] as String?,
  fontWeight: json['fontWeight'] as String?,
  lineHeight: json['lineHeight'] as String?,
  mainImageAutoSetFlg: json['mainImageAutoSetFlg'] as String?,
  unitFlg: json['unitFlg'] as String?,
  percentage: json['percentage'] as String?,
  numberDecimalPoint: json['numberDecimalPoint'] as String?,
  numberCommaDecimalPoint: json['numberCommaDecimalPoint'] as String?,
  appurtenancesInformationTypeId:
      (json['appurtenancesInformationTypeId'] as num?)?.toInt(),
);

Map<String, dynamic> _$OptionObjModelToJson(OptionObjModel instance) =>
    <String, dynamic>{
      'check': instance.check,
      'readonly': instance.readonly,
      'masterTypeId': instance.masterTypeId,
      'masterChainFlg': instance.masterChainFlg,
      'masterDisplayItems':
          instance.masterDisplayItems?.map((e) => e.toJson()).toList(),
      'sectionPrivateGroups': instance.sectionPrivateGroups,
      'sectionPrivateEditGroups': instance.sectionPrivateEditGroups,
      'maxlength': instance.maxlength,
      'calculateType': instance.calculateType,
      'currencyType': instance.currencyType,
      'currencyDecimalPoint': instance.currencyDecimalPoint,
      'radioOptions': instance.radioOptions,
      'color': instance.color,
      'fontSize': instance.fontSize,
      'fontStyle': instance.fontStyle,
      'fontWeight': instance.fontWeight,
      'lineHeight': instance.lineHeight,
      'data': instance.data,
      'checkboxOptions': instance.checkboxOptions,
      'checkboxMultiFlg': instance.checkboxMultiFlg,
      'mainImageAutoSetFlg': instance.mainImageAutoSetFlg,
      'unitFlg': instance.unitFlg,
      'percentage': instance.percentage,
      'numberDecimalPoint': instance.numberDecimalPoint,
      'numberCommaDecimalPoint': instance.numberCommaDecimalPoint,
      'calculateDecimalPoint': instance.calculateDecimalPoint,
      'calculateCommaDecimalPoint': instance.calculateCommaDecimalPoint,
      'calculate': instance.calculate,
      'unit': instance.unit,
      'dateType': instance.dateType,
      'appurtenancesInformationTypeId': instance.appurtenancesInformationTypeId,
    };

MasterDisplayItemModel _$MasterDisplayItemModelFromJson(
  Map<String, dynamic> json,
) => MasterDisplayItemModel(
  itemDisplayName: json['itemDisplayName'] as String?,
  itemId: (json['itemId'] as num?)?.toInt(),
  option: json['option'] as String?,
  itemName: json['itemName'] as String?,
  itemType: json['itemType'] as String?,
  itemValue: json['itemValue'],
);

Map<String, dynamic> _$MasterDisplayItemModelToJson(
  MasterDisplayItemModel instance,
) => <String, dynamic>{
  'itemDisplayName': instance.itemDisplayName,
  'itemId': instance.itemId,
  'option': instance.option,
  'itemName': instance.itemName,
  'itemType': instance.itemType,
  'itemValue': instance.itemValue,
};

SubOptionObjModel _$SubOptionObjModelFromJson(Map<String, dynamic> json) =>
    SubOptionObjModel(
      check: json['check'] as String?,
      readonly: json['readonly'] as String?,
      maxLength: json['maxLength'] as String?,
    );

Map<String, dynamic> _$SubOptionObjModelToJson(SubOptionObjModel instance) =>
    <String, dynamic>{
      'check': instance.check,
      'readonly': instance.readonly,
      'maxLength': instance.maxLength,
    };
