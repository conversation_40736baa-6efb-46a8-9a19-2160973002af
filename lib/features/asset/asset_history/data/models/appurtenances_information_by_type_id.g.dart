// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appurtenances_information_by_type_id.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppurtenancesInformationListResponse
_$AppurtenancesInformationListResponseFromJson(Map<String, dynamic> json) =>
    AppurtenancesInformationListResponse(
      code: (json['code'] as num).toInt(),
      msg: json['msg'] as String,
      items:
          (json['appurtenancesInformationList'] as List<dynamic>?)
              ?.map(
                (e) =>
                    e == null
                        ? null
                        : AppurtenancesInformationItem.fromJson(
                          e as Map<String, dynamic>,
                        ),
              )
              .toList(),
    );

Map<String, dynamic> _$AppurtenancesInformationListResponseToJson(
  AppurtenancesInformationListResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'appurtenancesInformationList': instance.items,
};

AppurtenancesInformationItem _$AppurtenancesInformationItemFromJson(
  Map<String, dynamic> json,
) => AppurtenancesInformationItem(
  appurtenancesInformationId:
      (json['appurtenancesInformationId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  assetId: (json['assetId'] as num?)?.toInt(),
  assetName: json['assetName'] as String?,
  assetTypeName: json['assetTypeName'] as String?,
  groupIds: json['groupIds'] as String?,
  appurtenancesInformationTypeId:
      (json['appurtenancesInformationTypeId'] as num?)?.toInt(),
  appurtenancesInformationTypeName:
      json['appurtenancesInformationTypeName'] as String?,
  manageTime: json['manageTime'] as String?,
  userName: json['userName'] as String?,
  location: json['location'] as String?,
  appurtenancesInformationText: json['appurtenancesInformationText'] as String?,
  assetText: json['assetText'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  barCode: json['barCode'] as String?,
  assetLocation: json['assetLocation'] as String?,
  state: json['state'],
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  option: json['option'] as String?,
  sysSetFlg: json['sysSetFlg'] as String?,
  interactionOperation: json['interactionOperation'] as String?,
  excelLineNum: (json['excelLineNum'] as num?)?.toInt(),
);

Map<String, dynamic> _$AppurtenancesInformationItemToJson(
  AppurtenancesInformationItem instance,
) => <String, dynamic>{
  'appurtenancesInformationId': instance.appurtenancesInformationId,
  'tenantId': instance.tenantId,
  'assetId': instance.assetId,
  'assetName': instance.assetName,
  'assetTypeName': instance.assetTypeName,
  'groupIds': instance.groupIds,
  'appurtenancesInformationTypeId': instance.appurtenancesInformationTypeId,
  'appurtenancesInformationTypeName': instance.appurtenancesInformationTypeName,
  'manageTime': instance.manageTime,
  'userName': instance.userName,
  'location': instance.location,
  'appurtenancesInformationText': instance.appurtenancesInformationText,
  'assetText': instance.assetText,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'barCode': instance.barCode,
  'assetLocation': instance.assetLocation,
  'state': instance.state,
  'assetTypeId': instance.assetTypeId,
  'option': instance.option,
  'sysSetFlg': instance.sysSetFlg,
  'interactionOperation': instance.interactionOperation,
  'excelLineNum': instance.excelLineNum,
};
