// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appurtenances_mobile_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppurtenancesMobileSettingResponse _$AppurtenancesMobileSettingResponseFromJson(
  Map<String, dynamic> json,
) => AppurtenancesMobileSettingResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
  appurtenancesInformationType:
      json['appurtenancesInformationType'] == null
          ? null
          : AppurtenancesInfoType.fromJson(
            json['appurtenancesInformationType'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$AppurtenancesMobileSettingResponseToJson(
  AppurtenancesMobileSettingResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'appurtenancesInformationType': instance.appurtenancesInformationType,
};

AppurtenancesInfoType _$AppurtenancesInfoTypeFromJson(
  Map<String, dynamic> json,
) => AppurtenancesInfoType(
  (json['appurtenancesInformationTypeId'] as num?)?.toInt(),
  json['commonFlg'] as String?,
  json['tenantId'] as String?,
  json['appurtenancesInformationTypeName'] as String?,
  json['mobileScanInsertFlg'] as String?,
  json['mobileDisplayCondition'] as String?,
  json['mobileDisplaySort'] as String?,
  json['createdByName'] as String?,
  json['itemFlg'] as String?,
  json['layoutTempSaveFlg'] as String?,
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedByName'] as String?,
  json['modifiedDate'] as String?,
  json['displayModifiedDate'] as String?,
  (json['alertCount'] as num?)?.toInt(),
  (json['appurtenancesInformationMobileColumnList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null
                ? null
                : AppurtenancesMobileColumn.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
  json['appurtenancesInformationMobileSetting'],
  json['appurtenancesInformationUnEditFlg'] as bool?,
);

Map<String, dynamic> _$AppurtenancesInfoTypeToJson(
  AppurtenancesInfoType instance,
) => <String, dynamic>{
  'appurtenancesInformationTypeId': instance.appurtenancesInformationTypeId,
  'commonFlg': instance.commonFlg,
  'tenantId': instance.tenantId,
  'appurtenancesInformationTypeName': instance.appurtenancesInformationTypeName,
  'mobileScanInsertFlg': instance.mobileScanInsertFlg,
  'mobileDisplayCondition': instance.mobileDisplayCondition,
  'mobileDisplaySort': instance.mobileDisplaySort,
  'createdByName': instance.createdByName,
  'itemFlg': instance.itemFlg,
  'layoutTempSaveFlg': instance.layoutTempSaveFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedByName': instance.modifiedByName,
  'modifiedDate': instance.modifiedDate,
  'displayModifiedDate': instance.displayModifiedDate,
  'alertCount': instance.alertCount,
  'appurtenancesInformationMobileColumnList':
      instance.appurtenancesInformationMobileColumnList,
  'appurtenancesInformationMobileSetting':
      instance.appurtenancesInformationMobileSetting,
  'appurtenancesInformationUnEditFlg':
      instance.appurtenancesInformationUnEditFlg,
};

AppurtenancesMobileColumn _$AppurtenancesMobileColumnFromJson(
  Map<String, dynamic> json,
) => AppurtenancesMobileColumn(
  json['tenantId'] as String?,
  (json['appurtenancesInformationTypeId'] as num?)?.toInt(),
  (json['itemId'] as num?)?.toInt(),
  (json['subItemId'] as num?)?.toInt(),
  json['itemName'] as String?,
  json['subItemName'] as String?,
  (json['itemSort'] as num?)?.toInt(),
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['itemOption'] as String?,
  json['itemType'] as String?,
);

Map<String, dynamic> _$AppurtenancesMobileColumnToJson(
  AppurtenancesMobileColumn instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'appurtenancesInformationTypeId': instance.appurtenancesInformationTypeId,
  'itemId': instance.itemId,
  'subItemId': instance.subItemId,
  'itemName': instance.itemName,
  'subItemName': instance.subItemName,
  'itemSort': instance.itemSort,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'itemOption': instance.itemOption,
  'itemType': instance.itemType,
};
