// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appurtenances_information_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppurtenancesInformationResponse _$AppurtenancesInformationResponseFromJson(
  Map<String, dynamic> json,
) => AppurtenancesInformationResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
  appurtenancesInformationMobileSettingForMobile:
      json['appurtenancesInformationMobileSettingForMobile'] == null
          ? null
          : AppurtenancesMobileSetting.fromJson(
            json['appurtenancesInformationMobileSettingForMobile']
                as Map<String, dynamic>,
          ),
  layoutSettingList:
      (json['layoutSettingList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : SharedLayoutSetting.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
);

Map<String, dynamic> _$AppurtenancesInformationResponseToJson(
  AppurtenancesInformationResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'appurtenancesInformationMobileSettingForMobile':
      instance.appurtenancesInformationMobileSettingForMobile,
  'layoutSettingList': instance.layoutSettingList,
};

AppurtenancesMobileSetting _$AppurtenancesMobileSettingFromJson(
  Map<String, dynamic> json,
) => AppurtenancesMobileSetting(
  json['appurtenancesInformationInfo'] == null
      ? null
      : AppurtenancesInfo.fromJson(
        json['appurtenancesInformationInfo'] as Map<String, dynamic>,
      ),
);

Map<String, dynamic> _$AppurtenancesMobileSettingToJson(
  AppurtenancesMobileSetting instance,
) => <String, dynamic>{
  'appurtenancesInformationInfo': instance.appurtenancesInformationInfo,
};

AppurtenancesInfo _$AppurtenancesInfoFromJson(Map<String, dynamic> json) =>
    AppurtenancesInfo(
      (json['appurtenancesInformationSubItemId4'] as num?)?.toInt(),
      (json['appurtenancesInformationSubItemId3'] as num?)?.toInt(),
      (json['appurtenancesInformationSubItemId5'] as num?)?.toInt(),
      json['appurtenancesInformationItemName1'] as String?,
      json['appurtenancesInformationItemName2'] as String?,
      json['appurtenancesInformationItemName3'] as String?,
      (json['appurtenancesInformationSubItemId2'] as num?)?.toInt(),
      json['appurtenancesInformationItemName4'] as String?,
      (json['appurtenancesInformationSubItemId1'] as num?)?.toInt(),
      json['appurtenancesInformationItemName5'] as String?,
      (json['appurtenancesInformationItemId5'] as num?)?.toInt(),
      json['appurtenancesInformationItemType1'] as String?,
      json['appurtenancesInformationItemType2'] as String?,
      json['appurtenancesInformationItemType3'] as String?,
      json['appurtenancesInformationItemType4'] as String?,
      json['appurtenancesInformationItemType5'] as String?,
      json['appurtenancesInformationItemOption2'] as String?,
      json['appurtenancesInformationItemOption1'] as String?,
      (json['appurtenancesInformationItemId3'] as num?)?.toInt(),
      (json['appurtenancesInformationItemId4'] as num?)?.toInt(),
      json['appurtenancesInformationItemOption5'] as String?,
      (json['appurtenancesInformationItemId1'] as num?)?.toInt(),
      json['appurtenancesInformationItemOption4'] as String?,
      (json['appurtenancesInformationItemId2'] as num?)?.toInt(),
      json['appurtenancesInformationItemOption3'] as String?,
    );

Map<String, dynamic> _$AppurtenancesInfoToJson(
  AppurtenancesInfo instance,
) => <String, dynamic>{
  'appurtenancesInformationSubItemId4':
      instance.appurtenancesInformationSubItemId4,
  'appurtenancesInformationSubItemId3':
      instance.appurtenancesInformationSubItemId3,
  'appurtenancesInformationSubItemId5':
      instance.appurtenancesInformationSubItemId5,
  'appurtenancesInformationItemName1':
      instance.appurtenancesInformationItemName1,
  'appurtenancesInformationItemName2':
      instance.appurtenancesInformationItemName2,
  'appurtenancesInformationItemName3':
      instance.appurtenancesInformationItemName3,
  'appurtenancesInformationSubItemId2':
      instance.appurtenancesInformationSubItemId2,
  'appurtenancesInformationItemName4':
      instance.appurtenancesInformationItemName4,
  'appurtenancesInformationSubItemId1':
      instance.appurtenancesInformationSubItemId1,
  'appurtenancesInformationItemName5':
      instance.appurtenancesInformationItemName5,
  'appurtenancesInformationItemId5': instance.appurtenancesInformationItemId5,
  'appurtenancesInformationItemType1':
      instance.appurtenancesInformationItemType1,
  'appurtenancesInformationItemType2':
      instance.appurtenancesInformationItemType2,
  'appurtenancesInformationItemType3':
      instance.appurtenancesInformationItemType3,
  'appurtenancesInformationItemType4':
      instance.appurtenancesInformationItemType4,
  'appurtenancesInformationItemType5':
      instance.appurtenancesInformationItemType5,
  'appurtenancesInformationItemOption2':
      instance.appurtenancesInformationItemOption2,
  'appurtenancesInformationItemOption1':
      instance.appurtenancesInformationItemOption1,
  'appurtenancesInformationItemId3': instance.appurtenancesInformationItemId3,
  'appurtenancesInformationItemId4': instance.appurtenancesInformationItemId4,
  'appurtenancesInformationItemOption5':
      instance.appurtenancesInformationItemOption5,
  'appurtenancesInformationItemId1': instance.appurtenancesInformationItemId1,
  'appurtenancesInformationItemOption4':
      instance.appurtenancesInformationItemOption4,
  'appurtenancesInformationItemId2': instance.appurtenancesInformationItemId2,
  'appurtenancesInformationItemOption3':
      instance.appurtenancesInformationItemOption3,
};
