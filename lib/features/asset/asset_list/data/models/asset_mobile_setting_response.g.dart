// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_mobile_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetMobileResponse _$AssetMobileResponseFromJson(Map<String, dynamic> json) =>
    AssetMobileResponse(
      assetItemList:
          (json['assetItemList'] as List<dynamic>?)
              ?.map(
                (e) => SharedLayoutSetting.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      assetMobileSettingForMobile:
          json['assetMobileSettingForMobile'] == null
              ? null
              : AssetMobileSettingForMobile.fromJson(
                json['assetMobileSettingForMobile'] as Map<String, dynamic>,
              ),
      code: (json['code'] as num).toInt(),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$AssetMobileResponseToJson(
  AssetMobileResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'assetItemList': instance.assetItemList?.map((e) => e.toJson()).toList(),
  'assetMobileSettingForMobile': instance.assetMobileSettingForMobile?.toJson(),
};
