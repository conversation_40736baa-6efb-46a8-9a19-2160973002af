// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_relation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetRelation _$AssetRelationFromJson(Map<String, dynamic> json) =>
    AssetRelation(
      tenantId: json['tenantId'] as String?,
      assetId: (json['assetId'] as num?)?.toInt(),
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      relationAssetId: (json['relationAssetId'] as num?)?.toInt(),
      assetText: json['assetText'] as String?,
      relationType: json['relationType'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      assetTypeName: json['assetTypeName'] as String?,
      state: json['state'] as String?,
    );

Map<String, dynamic> _$AssetRelationToJson(AssetRelation instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'assetId': instance.assetId,
      if (instance.assetTypeId case final value?) 'assetTypeId': value,
      'relationAssetId': instance.relationAssetId,
      if (instance.assetText case final value?) 'assetText': value,
      'relationType': instance.relationType,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      if (instance.assetTypeName case final value?) 'assetTypeName': value,
      if (instance.state case final value?) 'state': value,
    };
