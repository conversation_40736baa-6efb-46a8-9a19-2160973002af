// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_relation_column.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetRelationColumn _$AssetRelationColumnFromJson(Map<String, dynamic> json) =>
    AssetRelationColumn(
      tenantId: json['tenantId'] as String?,
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      relationTypeId: (json['relationTypeId'] as num?)?.toInt(),
      relationItemId: (json['relationItemId'] as num?)?.toInt(),
      relationSubItemId: (json['relationSubItemId'] as num?)?.toInt(),
      userId: json['userId'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
    );

Map<String, dynamic> _$AssetRelationColumnToJson(
  AssetRelationColumn instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'assetTypeId': instance.assetTypeId,
  'relationTypeId': instance.relationTypeId,
  'relationItemId': instance.relationItemId,
  'relationSubItemId': instance.relationSubItemId,
  'userId': instance.userId,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
