// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_reservation_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetReservationStatus _$AssetReservationStatusFromJson(
  Map<String, dynamic> json,
) => AssetReservationStatus(
  tenantId: json['tenantId'] as String?,
  reservationNo: (json['reservationNo'] as num?)?.toInt(),
  assetId: (json['assetId'] as num?)?.toInt(),
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  startDate: json['startDate'] as String?,
  endDate: json['endDate'] as String?,
  eventTypeId: (json['eventTypeId'] as num?)?.toInt(),
  reservationName: json['reservationName'] as String?,
  reservationComment: json['reservationComment'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  reservationText: json['reservationText'] as String?,
  unitDay: json['unitDay'] as String?,
  extraCommonText: json['extraCommonText'] as String?,
  alertSetting: json['alertSetting'] as String?,
);

Map<String, dynamic> _$AssetReservationStatusToJson(
  AssetReservationStatus instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'reservationNo': instance.reservationNo,
  'assetId': instance.assetId,
  'assetTypeId': instance.assetTypeId,
  'startDate': instance.startDate,
  'endDate': instance.endDate,
  'eventTypeId': instance.eventTypeId,
  'reservationName': instance.reservationName,
  'reservationComment': instance.reservationComment,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'reservationText': instance.reservationText,
  'unitDay': instance.unitDay,
  'extraCommonText': instance.extraCommonText,
  'alertSetting': instance.alertSetting,
};
