// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_mobile_setting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetMobileSettingForMobile _$AssetMobileSettingForMobileFromJson(
  Map<String, dynamic> json,
) => AssetMobileSettingForMobile(
  arInfo:
      json['arInfo'] == null
          ? null
          : ArInfo.fromJson(json['arInfo'] as Map<String, dynamic>),
  assetInfo:
      json['assetInfo'] == null
          ? null
          : AssetInfo.fromJson(json['assetInfo'] as Map<String, dynamic>),
);

Map<String, dynamic> _$AssetMobileSettingForMobileToJson(
  AssetMobileSettingForMobile instance,
) => <String, dynamic>{
  'arInfo': instance.arInfo?.toJson(),
  'assetInfo': instance.assetInfo?.toJson(),
};

ArInfo _$ArInfoFromJson(Map<String, dynamic> json) => ArInfo(
  category5ItemId: (json['category5ItemId'] as num?)?.toInt(),
  category5ItemName: json['category5ItemName'] as String?,
  category5ItemType: json['category5ItemType'] as String?,
  category5ItemOption: json['category5ItemOption'] as String?,
  category5SubItemId: (json['category5SubItemId'] as num?)?.toInt(),
  category5SubItemName: json['category5SubItemName'] as String?,
  category5SubItemType: json['category5SubItemType'] as String?,
  category5SubItemOption: json['category5SubItemOption'] as String?,
  category4ItemId: (json['category4ItemId'] as num?)?.toInt(),
  category4ItemName: json['category4ItemName'] as String?,
  category4ItemType: json['category4ItemType'] as String?,
  category4ItemOption: json['category4ItemOption'] as String?,
  category4SubItemId: (json['category4SubItemId'] as num?)?.toInt(),
  category4SubItemName: json['category4SubItemName'] as String?,
  category4SubItemType: json['category4SubItemType'] as String?,
  category4SubItemOption: json['category4SubItemOption'] as String?,
  category3ItemId: (json['category3ItemId'] as num?)?.toInt(),
  category3ItemName: json['category3ItemName'] as String?,
  category3ItemType: json['category3ItemType'] as String?,
  category3ItemOption: json['category3ItemOption'] as String?,
  category3SubItemId: (json['category3SubItemId'] as num?)?.toInt(),
  category3SubItemName: json['category3SubItemName'] as String?,
  category3SubItemType: json['category3SubItemType'] as String?,
  category3SubItemOption: json['category3SubItemOption'] as String?,
  category2ItemId: (json['category2ItemId'] as num?)?.toInt(),
  category2ItemName: json['category2ItemName'] as String?,
  category2ItemType: json['category2ItemType'] as String?,
  category2ItemOption: json['category2ItemOption'] as String?,
  category2SubItemId: (json['category2SubItemId'] as num?)?.toInt(),
  category2SubItemName: json['category2SubItemName'] as String?,
  category2SubItemType: json['category2SubItemType'] as String?,
  category2SubItemOption: json['category2SubItemOption'] as String?,
  category1ItemId: (json['category1ItemId'] as num?)?.toInt(),
  category1ItemName: json['category1ItemName'] as String?,
  category1ItemType: json['category1ItemType'] as String?,
  category1ItemOption: json['category1ItemOption'] as String?,
  category1SubItemId: (json['category1SubItemId'] as num?)?.toInt(),
  category1SubItemName: json['category1SubItemName'] as String?,
  category1SubItemType: json['category1SubItemType'] as String?,
  category1SubItemOption: json['category1SubItemOption'] as String?,
  arLevel3BgColor: json['arLevel3BgColor'] as String?,
  arLevel1ItemId: (json['arLevel1ItemId'] as num?)?.toInt(),
  arLevel2ItemId: (json['arLevel2ItemId'] as num?)?.toInt(),
  arLevel3ItemId: (json['arLevel3ItemId'] as num?)?.toInt(),
  arLevel1ItemName: json['arLevel1ItemName'] as String?,
  arLevel2ItemName: json['arLevel2ItemName'] as String?,
  arLevel3ItemName: json['arLevel3ItemName'] as String?,
  arLevel1ItemType: json['arLevel1ItemType'] as String?,
  arLevel2ItemType: json['arLevel2ItemType'] as String?,
  arLevel3ItemType: json['arLevel3ItemType'] as String?,
  arLevel1ItemOption: json['arLevel1ItemOption'] as String?,
  arLevel2ItemOption: json['arLevel2ItemOption'] as String?,
  arLevel3ItemOption: json['arLevel3ItemOption'] as String?,
  arLevel1SubItemId: (json['arLevel1SubItemId'] as num?)?.toInt(),
  arLevel2SubItemId: (json['arLevel2SubItemId'] as num?)?.toInt(),
  arLevel3SubItemId: (json['arLevel3SubItemId'] as num?)?.toInt(),
);

Map<String, dynamic> _$ArInfoToJson(ArInfo instance) => <String, dynamic>{
  'category5ItemId': instance.category5ItemId,
  'category5ItemName': instance.category5ItemName,
  'category5ItemType': instance.category5ItemType,
  'category5ItemOption': instance.category5ItemOption,
  'category5SubItemId': instance.category5SubItemId,
  'category5SubItemName': instance.category5SubItemName,
  'category5SubItemType': instance.category5SubItemType,
  'category5SubItemOption': instance.category5SubItemOption,
  'category4ItemId': instance.category4ItemId,
  'category4ItemName': instance.category4ItemName,
  'category4ItemType': instance.category4ItemType,
  'category4ItemOption': instance.category4ItemOption,
  'category4SubItemId': instance.category4SubItemId,
  'category4SubItemName': instance.category4SubItemName,
  'category4SubItemType': instance.category4SubItemType,
  'category4SubItemOption': instance.category4SubItemOption,
  'category3ItemId': instance.category3ItemId,
  'category3ItemName': instance.category3ItemName,
  'category3ItemType': instance.category3ItemType,
  'category3ItemOption': instance.category3ItemOption,
  'category3SubItemId': instance.category3SubItemId,
  'category3SubItemName': instance.category3SubItemName,
  'category3SubItemType': instance.category3SubItemType,
  'category3SubItemOption': instance.category3SubItemOption,
  'category2ItemId': instance.category2ItemId,
  'category2ItemName': instance.category2ItemName,
  'category2ItemType': instance.category2ItemType,
  'category2ItemOption': instance.category2ItemOption,
  'category2SubItemId': instance.category2SubItemId,
  'category2SubItemName': instance.category2SubItemName,
  'category2SubItemType': instance.category2SubItemType,
  'category2SubItemOption': instance.category2SubItemOption,
  'category1ItemId': instance.category1ItemId,
  'category1ItemName': instance.category1ItemName,
  'category1ItemType': instance.category1ItemType,
  'category1ItemOption': instance.category1ItemOption,
  'category1SubItemId': instance.category1SubItemId,
  'category1SubItemName': instance.category1SubItemName,
  'category1SubItemType': instance.category1SubItemType,
  'category1SubItemOption': instance.category1SubItemOption,
  'arLevel3BgColor': instance.arLevel3BgColor,
  'arLevel1ItemId': instance.arLevel1ItemId,
  'arLevel2ItemId': instance.arLevel2ItemId,
  'arLevel3ItemId': instance.arLevel3ItemId,
  'arLevel1ItemName': instance.arLevel1ItemName,
  'arLevel2ItemName': instance.arLevel2ItemName,
  'arLevel3ItemName': instance.arLevel3ItemName,
  'arLevel1ItemType': instance.arLevel1ItemType,
  'arLevel2ItemType': instance.arLevel2ItemType,
  'arLevel3ItemType': instance.arLevel3ItemType,
  'arLevel1ItemOption': instance.arLevel1ItemOption,
  'arLevel2ItemOption': instance.arLevel2ItemOption,
  'arLevel3ItemOption': instance.arLevel3ItemOption,
  'arLevel1SubItemId': instance.arLevel1SubItemId,
  'arLevel2SubItemId': instance.arLevel2SubItemId,
  'arLevel3SubItemId': instance.arLevel3SubItemId,
};

AssetInfo _$AssetInfoFromJson(Map<String, dynamic> json) => AssetInfo(
  assetLevel4SubItemId: (json['assetLevel4SubItemId'] as num?)?.toInt(),
  assetLevel5SubItemId: (json['assetLevel5SubItemId'] as num?)?.toInt(),
  assetLevel2ItemOption: json['assetLevel2ItemOption'] as String?,
  assetLevel1ItemId: (json['assetLevel1ItemId'] as num?)?.toInt(),
  assetLevel1ItemName: json['assetLevel1ItemName'] as String?,
  assetLevel1ItemOption: json['assetLevel1ItemOption'] as String?,
  assetLevel1ItemType: json['assetLevel1ItemType'] as String?,
  assetLevel1SubItemId: (json['assetLevel1SubItemId'] as num?)?.toInt(),
  assetLevel2ItemId: (json['assetLevel2ItemId'] as num?)?.toInt(),
  assetLevel2ItemName: json['assetLevel2ItemName'] as String?,
  assetLevel2ItemType: json['assetLevel2ItemType'] as String?,
  assetLevel2SubItemId: (json['assetLevel2SubItemId'] as num?)?.toInt(),
  assetLevel3ItemId: (json['assetLevel3ItemId'] as num?)?.toInt(),
  assetLevel3ItemName: json['assetLevel3ItemName'] as String?,
  assetLevel3ItemOption: json['assetLevel3ItemOption'] as String?,
  assetLevel3ItemType: json['assetLevel3ItemType'] as String?,
  assetLevel3SubItemId: (json['assetLevel3SubItemId'] as num?)?.toInt(),
  assetLevel4ItemId: (json['assetLevel4ItemId'] as num?)?.toInt(),
  assetLevel4ItemName: json['assetLevel4ItemName'] as String?,
  assetLevel4ItemOption: json['assetLevel4ItemOption'] as String?,
  assetLevel4ItemType: json['assetLevel4ItemType'] as String?,
  assetLevel5ItemId: (json['assetLevel5ItemId'] as num?)?.toInt(),
  assetLevel5ItemName: json['assetLevel5ItemName'] as String?,
  assetLevel5ItemOption: json['assetLevel5ItemOption'] as String?,
  assetLevel5ItemType: json['assetLevel5ItemType'] as String?,
  assetLevel1ItemDisplayName: json['assetLevel1ItemDisplayName'] as String?,
  assetLevel2ItemDisplayName: json['assetLevel2ItemDisplayName'] as String?,
  assetLevel3ItemDisplayName: json['assetLevel3ItemDisplayName'] as String?,
  assetLevel4ItemDisplayName: json['assetLevel4ItemDisplayName'] as String?,
  assetLevel5ItemDisplayName: json['assetLevel5ItemDisplayName'] as String?,
);

Map<String, dynamic> _$AssetInfoToJson(AssetInfo instance) => <String, dynamic>{
  'assetLevel1ItemId': instance.assetLevel1ItemId,
  'assetLevel2ItemId': instance.assetLevel2ItemId,
  'assetLevel3ItemId': instance.assetLevel3ItemId,
  'assetLevel4ItemId': instance.assetLevel4ItemId,
  'assetLevel5ItemId': instance.assetLevel5ItemId,
  'assetLevel1SubItemId': instance.assetLevel1SubItemId,
  'assetLevel2SubItemId': instance.assetLevel2SubItemId,
  'assetLevel3SubItemId': instance.assetLevel3SubItemId,
  'assetLevel4SubItemId': instance.assetLevel4SubItemId,
  'assetLevel5SubItemId': instance.assetLevel5SubItemId,
  'assetLevel1ItemType': instance.assetLevel1ItemType,
  'assetLevel2ItemType': instance.assetLevel2ItemType,
  'assetLevel3ItemType': instance.assetLevel3ItemType,
  'assetLevel4ItemType': instance.assetLevel4ItemType,
  'assetLevel5ItemType': instance.assetLevel5ItemType,
  'assetLevel1ItemName': instance.assetLevel1ItemName,
  'assetLevel2ItemName': instance.assetLevel2ItemName,
  'assetLevel3ItemName': instance.assetLevel3ItemName,
  'assetLevel4ItemName': instance.assetLevel4ItemName,
  'assetLevel5ItemName': instance.assetLevel5ItemName,
  'assetLevel1ItemDisplayName': instance.assetLevel1ItemDisplayName,
  'assetLevel2ItemDisplayName': instance.assetLevel2ItemDisplayName,
  'assetLevel3ItemDisplayName': instance.assetLevel3ItemDisplayName,
  'assetLevel4ItemDisplayName': instance.assetLevel4ItemDisplayName,
  'assetLevel5ItemDisplayName': instance.assetLevel5ItemDisplayName,
  'assetLevel1ItemOption': instance.assetLevel1ItemOption,
  'assetLevel2ItemOption': instance.assetLevel2ItemOption,
  'assetLevel3ItemOption': instance.assetLevel3ItemOption,
  'assetLevel4ItemOption': instance.assetLevel4ItemOption,
  'assetLevel5ItemOption': instance.assetLevel5ItemOption,
};
