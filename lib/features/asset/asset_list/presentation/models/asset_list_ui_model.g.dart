// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_list_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoadDataResult _$LoadDataResultFromJson(Map<String, dynamic> json) =>
    LoadDataResult(
      assetCategoryList:
          (json['assetCategoryList'] as List<dynamic>)
              .map((e) => CategoryModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      isMoreThenLimit: json['isMoreThenLimit'] as bool,
      searchAssetCount: (json['searchAssetCount'] as num).toInt(),
      sumAssetCount: (json['sumAssetCount'] as num).toInt(),
      isLoading: json['isLoading'] as bool,
      assetUIModelList:
          (json['assetUIModelList'] as List<dynamic>?)
              ?.map((e) => AssetUIModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      assetErrorMsg: json['assetErrorMsg'] as String?,
    );

Map<String, dynamic> _$LoadDataResultToJson(LoadDataResult instance) =>
    <String, dynamic>{
      'isMoreThenLimit': instance.isMoreThenLimit,
      'searchAssetCount': instance.searchAssetCount,
      'sumAssetCount': instance.sumAssetCount,
      'isLoading': instance.isLoading,
      'assetUIModelList':
          instance.assetUIModelList?.map((e) => e.toJson()).toList(),
      'assetErrorMsg': instance.assetErrorMsg,
      'assetCategoryList':
          instance.assetCategoryList.map((e) => e.toJson()).toList(),
    };
