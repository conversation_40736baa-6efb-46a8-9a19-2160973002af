// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetUIModel _$AssetUIModelFromJson(Map<String, dynamic> json) => AssetUIModel(
  assetDisplayList:
      (json['assetDisplayList'] as List<dynamic>?)
          ?.map((e) => AssetUIDisplay.fromJson(e as Map<String, dynamic>))
          .toList(),
  imageUrl: json['imageUrl'] as String?,
  assetId: (json['assetId'] as num).toInt(),
);

Map<String, dynamic> _$AssetUIModelToJson(AssetUIModel instance) =>
    <String, dynamic>{
      'assetDisplayList':
          instance.assetDisplayList?.map((e) => e.toJson()).toList(),
      'imageUrl': instance.imageUrl,
      'assetId': instance.assetId,
    };

AssetUIDisplay _$AssetUIDisplayFromJson(Map<String, dynamic> json) =>
    AssetUIDisplay(
      title: json['title'] as String,
      content: json['content'] as String,
    );

Map<String, dynamic> _$AssetUIDisplayToJson(AssetUIDisplay instance) =>
    <String, dynamic>{'title': instance.title, 'content': instance.content};
