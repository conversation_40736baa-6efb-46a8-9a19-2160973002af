// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageUIModel _$MessageUIModelFromJson(Map<String, dynamic> json) =>
    MessageUIModel(
      activeStartDate: json['activeStartDate'] as String,
      notificationTitle: json['notificationTitle'] as String,
      fileInformation: json['fileInformation'] as String? ?? '',
      notificationBody: json['notificationBody'] as String? ?? '',
      readStatus: (json['readStatus'] as num?)?.toInt() ?? 0,
      notificationId: (json['notificationId'] as num?)?.toInt() ?? 0,
      category: (json['category'] as num?)?.toInt() ?? 0,
      files:
          (json['files'] as List<dynamic>?)
              ?.map(
                (e) => MessageFileUIModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
    );

Map<String, dynamic> _$MessageUIModelToJson(MessageUIModel instance) =>
    <String, dynamic>{
      'notificationId': instance.notificationId,
      'readStatus': instance.readStatus,
      'category': instance.category,
      'notificationTitle': instance.notificationTitle,
      'notificationBody': instance.notificationBody,
      'fileInformation': instance.fileInformation,
      'activeStartDate': instance.activeStartDate,
      'files': instance.files,
    };
