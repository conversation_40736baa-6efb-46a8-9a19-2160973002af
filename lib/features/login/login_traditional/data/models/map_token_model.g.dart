// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'map_token_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MapTokenModel _$MapTokenModelFromJson(Map<String, dynamic> json) =>
    MapTokenModel(
      ticket: json['ticket'] as String?,
      accessToken: json['accessToken'] as String?,
      tenantAdmin: json['tenantAdmin'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      refreshToken: json['refreshToken'] as String?,
    );

Map<String, dynamic> _$MapTokenModelToJson(MapTokenModel instance) =>
    <String, dynamic>{
      if (instance.ticket case final value?) 'ticket': value,
      if (instance.accessToken case final value?) 'accessToken': value,
      if (instance.tenantAdmin case final value?) 'tenantAdmin': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.refreshToken case final value?) 'refreshToken': value,
    };
