// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dpp_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DppInfoModel _$DppInfoModelFromJson(Map<String, dynamic> json) => DppInfoModel(
  dppId: json['dppId'] as String?,
  modules:
      (json['modules'] as List<dynamic>?)
          ?.map((e) => ModuleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  eunomiaID: json['eunomiaID'] as String?,
  dppAccessKey: json['dppAccessKey'] as String?,
  eunomiaAccessKey: json['eunomiaAccessKey'] as String?,
  eunomiaUploadResult: json['eunomiaUploadResult'] as String?,
  eunomiaGetModelVersion: json['eunomiaGetModelVersion'] as String?,
  eunomiaGetModelDownloadURL: json['eunomiaGetModelDownloadURL'] as String?,
);

Map<String, dynamic> _$DppInfoModelToJson(DppInfoModel instance) =>
    <String, dynamic>{
      'dppId': instance.dppId,
      'modules': instance.modules,
      'eunomiaID': instance.eunomiaID,
      'dppAccessKey': instance.dppAccessKey,
      'eunomiaAccessKey': instance.eunomiaAccessKey,
      'eunomiaUploadResult': instance.eunomiaUploadResult,
      'eunomiaGetModelVersion': instance.eunomiaGetModelVersion,
      'eunomiaGetModelDownloadURL': instance.eunomiaGetModelDownloadURL,
    };

ModuleModel _$ModuleModelFromJson(Map<String, dynamic> json) => ModuleModel(
  tenants:
      (json['tenants'] as List<dynamic>?)?.map((e) => e as String).toList(),
  serialNo: json['serialNo'] as String?,
  moduleUrl: json['moduleUrl'] as String?,
  moduleType: json['moduleType'] as String?,
);

Map<String, dynamic> _$ModuleModelToJson(ModuleModel instance) =>
    <String, dynamic>{
      'tenants': instance.tenants,
      'serialNo': instance.serialNo,
      'moduleUrl': instance.moduleUrl,
      'moduleType': instance.moduleType,
    };
