// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResultModel _$LoginResultModelFromJson(
  Map<String, dynamic> json,
) => LoginResultModel(
  tenants:
      (json['tenants'] as List<dynamic>?)
          ?.map((e) => SharedTenantModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  mapToken:
      json['mapToken'] == null
          ? null
          : MapTokenModel.fromJson(json['mapToken'] as Map<String, dynamic>),
);

Map<String, dynamic> _$LoginResultModelToJson(LoginResultModel instance) =>
    <String, dynamic>{
      'tenants': instance.tenants,
      'mapToken': instance.mapToken,
    };
