// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'password_policy_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PasswordPolicyModel _$PasswordPolicyModelFromJson(Map<String, dynamic> json) =>
    PasswordPolicyModel(
      policyId: (json['policyId'] as num?)?.toInt(),
      tenantId: json['tenantId'] as String?,
      expireTime: (json['expireTime'] as num?)?.toInt(),
      mustContain: json['mustContain'] as String?,
      enableFlg: json['enableFlg'] as String?,
      length: (json['length'] as num?)?.toInt(),
      repeatForbidCount: (json['repeatForbidCount'] as num?)?.toInt(),
      lockEnableFlg: json['lockEnableFlg'] as String?,
      lockTimes: (json['lockTimes'] as num?)?.toInt(),
      lockPeriod: (json['lockPeriod'] as num?)?.toInt(),
      strengthBase: (json['strengthBase'] as num?)?.toDouble(),
      strengthLength: (json['strengthLength'] as num?)?.toInt(),
      strength: json['strength'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      tenantName: json['tenantName'] as String?,
    );

Map<String, dynamic> _$PasswordPolicyModelToJson(
  PasswordPolicyModel instance,
) => <String, dynamic>{
  'policyId': instance.policyId,
  'tenantId': instance.tenantId,
  'expireTime': instance.expireTime,
  'mustContain': instance.mustContain,
  'enableFlg': instance.enableFlg,
  'length': instance.length,
  'repeatForbidCount': instance.repeatForbidCount,
  'lockEnableFlg': instance.lockEnableFlg,
  'lockTimes': instance.lockTimes,
  'lockPeriod': instance.lockPeriod,
  'strengthBase': instance.strengthBase,
  'strengthLength': instance.strengthLength,
  'strength': instance.strength,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'tenantName': instance.tenantName,
};
