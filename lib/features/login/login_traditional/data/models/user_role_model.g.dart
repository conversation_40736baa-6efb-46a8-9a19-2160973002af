// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_role_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserRoleModel _$UserRoleModelFromJson(Map<String, dynamic> json) =>
    UserRoleModel(
      userName: json['userName'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      roleId: (json['roleId'] as num?)?.toInt() ?? 1,
      tenantId: json['tenantId'] as String?,
      mainFlg: json['mainFlg'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      roleName: json['roleName'] as String?,
      mfaFlg: json['mfaFlg'] as String?,
    );

Map<String, dynamic> _$UserRoleModelToJson(UserRoleModel instance) =>
    <String, dynamic>{
      'userName': instance.userName,
      'userId': instance.userId,
      'roleId': instance.roleId,
      'tenantId': instance.tenantId,
      'mainFlg': instance.mainFlg,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'roleName': instance.roleName,
      'mfaFlg': instance.mfaFlg,
    };
