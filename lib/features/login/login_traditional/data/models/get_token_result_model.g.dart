// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_token_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetTokenResultModel _$GetTokenResultModelFromJson(
  Map<String, dynamic> json,
) => GetTokenResultModel(
    policy:
        json['policy'] == null
            ? null
            : PasswordPolicyModel.fromJson(
              json['policy'] as Map<String, dynamic>,
            ),
    user:
        json['user'] == null
            ? null
            : SharedUserModel.fromJson(json['user'] as Map<String, dynamic>),
    mapToken:
        json['mapToken'] == null
            ? null
            : MapTokenModel.fromJson(json['mapToken'] as Map<String, dynamic>),
    membership: (json['membership'] as num?)?.toInt(),
    printEnableFlg: json['printEnableFlg'] as String?,
    tenantEnableTwoStep: json['tenantEnableTwoStep'] as bool?,
    barcodeExtraction: json['barcodeExtraction'] as String?,
    hasAssetforce: json['hasAssetforce'] as bool?,
    datadogOpenType: (json['datadogOpenType'] as num?)?.toInt(),
    systemUrl: json['systemUrl'] as String?,
    defaultSystem: json['defaultSystem'] as String?,
    userRoleList:
        (json['userRoleList'] as List<dynamic>?)
            ?.map((e) => UserRoleModel.fromJson(e as Map<String, dynamic>))
            .toList(),
    hostInfo: json['hostInfo'] as String?,
    tenantUseStatesList:
        (json['tenantUseStatesList'] as List<dynamic>?)
            ?.map(
              (e) => SharedTenantUseStatusModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList(),
    dppInfo:
        json['dppInfo'] == null
            ? null
            : DppInfoModel.fromJson(json['dppInfo'] as Map<String, dynamic>),
    zoneDomainKey: json['zoneDomainKey'] as String?,
    signInResult: json['signInResult'] as String?,
    code: (json['code'] as num).toInt(),
    msg: json['msg'] as String,
  )
  ..tenant =
      json['tenant'] == null
          ? null
          : SharedTenantModel.fromJson(json['tenant'] as Map<String, dynamic>);

Map<String, dynamic> _$GetTokenResultModelToJson(
  GetTokenResultModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  if (instance.policy?.toJson() case final value?) 'policy': value,
  if (instance.user?.toJson() case final value?) 'user': value,
  if (instance.mapToken?.toJson() case final value?) 'mapToken': value,
  if (instance.membership case final value?) 'membership': value,
  if (instance.printEnableFlg case final value?) 'printEnableFlg': value,
  if (instance.tenantEnableTwoStep case final value?)
    'tenantEnableTwoStep': value,
  if (instance.barcodeExtraction case final value?) 'barcodeExtraction': value,
  if (instance.hasAssetforce case final value?) 'hasAssetforce': value,
  if (instance.datadogOpenType case final value?) 'datadogOpenType': value,
  if (instance.systemUrl case final value?) 'systemUrl': value,
  if (instance.defaultSystem case final value?) 'defaultSystem': value,
  if (instance.userRoleList?.map((e) => e.toJson()).toList() case final value?)
    'userRoleList': value,
  if (instance.hostInfo case final value?) 'hostInfo': value,
  if (instance.tenantUseStatesList?.map((e) => e.toJson()).toList()
      case final value?)
    'tenantUseStatesList': value,
  if (instance.dppInfo?.toJson() case final value?) 'dppInfo': value,
  if (instance.zoneDomainKey case final value?) 'zoneDomainKey': value,
  if (instance.tenant?.toJson() case final value?) 'tenant': value,
  if (instance.signInResult case final value?) 'signInResult': value,
};
