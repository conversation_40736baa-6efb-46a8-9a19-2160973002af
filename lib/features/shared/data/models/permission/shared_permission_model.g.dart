// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_permission_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedPermissionResponse _$SharedPermissionResponseFromJson(
  Map<String, dynamic> json,
) => SharedPermissionResponse(
  permissionList:
      (json['permissionList'] as List<dynamic>?)
          ?.map(
            (e) => SharedPermissionModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  msg: json['msg'] as String,
  code: (json['code'] as num).toInt(),
);

Map<String, dynamic> _$SharedPermissionResponseToJson(
  SharedPermissionResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'permissionList': instance.permissionList,
};

SharedPermissionModel _$SharedPermissionModelFromJson(
  Map<String, dynamic> json,
) => SharedPermissionModel(
  roleId: (json['roleId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  type: json['type'] as String?,
  functionId: json['functionId'] as String?,
  pageComponent: json['pageComponent'] as String?,
  pageId: json['pageId'] as String?,
  resourceId: (json['resourceId'] as num?)?.toInt(),
  resource: json['resource'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
);

Map<String, dynamic> _$SharedPermissionModelToJson(
  SharedPermissionModel instance,
) => <String, dynamic>{
  'roleId': instance.roleId,
  'tenantId': instance.tenantId,
  'type': instance.type,
  'functionId': instance.functionId,
  'pageComponent': instance.pageComponent,
  'pageId': instance.pageId,
  'resourceId': instance.resourceId,
  'resource': instance.resource,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
