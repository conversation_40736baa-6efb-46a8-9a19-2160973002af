// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_layout_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetLayoutSettingResponse _$AssetLayoutSettingResponseFromJson(
  Map<String, dynamic> json,
) => AssetLayoutSettingResponse(
  layoutSettingList:
      (json['layoutSettingList'] as List<dynamic>)
          .map((e) => SharedLayoutSetting.fromJson(e as Map<String, dynamic>))
          .toList(),
  appurtenanceDeletedItemNames:
      (json['appurtenanceDeletedItemNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  commonJS: json['commonJS'] as String?,
  js: json['js'] as String?,
  appurJsOnSave: json['appurJsOnSave'] as String?,
);

Map<String, dynamic> _$AssetLayoutSettingResponseToJson(
  AssetLayoutSettingResponse instance,
) => <String, dynamic>{
  'layoutSettingList': instance.layoutSettingList,
  'appurtenanceDeletedItemNames': instance.appurtenanceDeletedItemNames,
  'commonJS': instance.commonJS,
  'js': instance.js,
  'appurJsOnSave': instance.appurJsOnSave,
};
