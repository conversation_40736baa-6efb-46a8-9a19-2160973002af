// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_relation_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetRelationListResponse _$AssetRelationListResponseFromJson(
  Map<String, dynamic> json,
) =>
    AssetRelationListResponse(
        (json['assetRelationList'] as List<dynamic>?)
            ?.map(
              (e) =>
                  e == null
                      ? null
                      : AssetRelationItem.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      )
      ..msg = json['msg'] as String?
      ..code = (json['code'] as num?)?.toInt();

Map<String, dynamic> _$AssetRelationListResponseToJson(
  AssetRelationListResponse instance,
) => <String, dynamic>{
  'msg': instance.msg,
  'code': instance.code,
  'assetRelationList': instance.assetRelationList,
};

AssetRelationItem _$AssetRelationItemFromJson(Map<String, dynamic> json) =>
    AssetRelationItem(
      json['tenantId'] as String?,
      (json['assetId'] as num?)?.toInt(),
      (json['assetTypeId'] as num?)?.toInt(),
      (json['relationAssetId'] as num?)?.toInt(),
      json['assetText'] as String?,
      json['relationType'] as String?,
      (json['createdById'] as num?)?.toInt(),
      json['createdDate'] as String?,
      (json['modifiedById'] as num?)?.toInt(),
      json['modifiedDate'] as String?,
      json['assetTypeName'] as String?,
      json['state'] as String?,
    );

Map<String, dynamic> _$AssetRelationItemToJson(AssetRelationItem instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'assetId': instance.assetId,
      'assetTypeId': instance.assetTypeId,
      'relationAssetId': instance.relationAssetId,
      'assetText': instance.assetText,
      'relationType': instance.relationType,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'assetTypeName': instance.assetTypeName,
      'state': instance.state,
    };
