// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_item_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetItemResponse _$AssetItemResponseFromJson(
  Map<String, dynamic> json,
) => AssetItemResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
  assetItemList:
      (json['assetItemList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : AssetItemListModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
);

Map<String, dynamic> _$AssetItemResponseToJson(AssetItemResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'assetItemList': instance.assetItemList,
    };

AssetItemListModel _$AssetItemListModelFromJson(Map<String, dynamic> json) =>
    AssetItemListModel(
        itemId: (json['itemId'] as num?)?.toInt(),
        itemIdStr: json['itemIdStr'] as String?,
        subItemId: (json['subItemId'] as num?)?.toInt(),
        subItemName: json['subItemName'] as String?,
        subItemType: json['subItemType'] as String?,
        displayItemName: json['displayItemName'] as String?,
        tenantId: json['tenantId'] as String?,
        assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
        itemName: json['itemName'] as String?,
        itemDisplayName: json['itemDisplayName'] as String?,
        itemType: json['itemType'] as String?,
        option: json['option'] as String?,
        defaultData: json['defaultData'],
        inputFlg: json['inputFlg'] as String?,
        uneditableFlg: json['uneditableFlg'] as String?,
        layoutSetFlg: json['layoutSetFlg'] as String?,
        sectionName: json['sectionName'] as String?,
        sectionType: json['sectionType'] as String?,
        sectionSort: (json['sectionSort'] as num?)?.toInt(),
        positionX: (json['positionX'] as num?)?.toInt(),
        positionY: (json['positionY'] as num?)?.toInt(),
        width: (json['width'] as num?)?.toInt(),
        height: (json['height'] as num?)?.toInt(),
        sysSetFlg: json['sysSetFlg'] as String?,
        createdById: json['createdById'] as String?,
        createdDate: json['createdDate'] as String?,
        modifiedById: json['modifiedById'] as String?,
        modifiedDate: json['modifiedDate'] as String?,
        mobileFlg: json['mobileFlg'] as String?,
        replaceName: json['replaceName'] as String?,
        itemIds: json['itemIds'] as String?,
        sectionPrivateGroupsPermissionCheckLog:
            json['sectionPrivateGroupsPermissionCheckLog'] as String?,
        sectionPrivateEditGroupsPermissionCheckLog:
            json['sectionPrivateEditGroupsPermissionCheckLog'] as String?,
        sectionPrivateGroupsPermissionCheckId:
            json['sectionPrivateGroupsPermissionCheckId'] as String?,
        sectionPrivateEditGroupsPermissionCheckId:
            json['sectionPrivateEditGroupsPermissionCheckId'] as String?,
        sectionPrivateGroupsName: json['sectionPrivateGroupsName'] as String?,
        sectionPrivateEditGroupsName:
            json['sectionPrivateEditGroupsName'] as String?,
        appurtenancesInformationTypeNames:
            json['appurtenancesInformationTypeNames'] as String?,
        masterTypeNames: json['masterTypeNames'] as String?,
        dbtype: json['dbtype'] as String?,
        commonItem: json['commonItem'] as bool?,
        isShowMessage: json['isShowMessage'] as bool?,
        showMessage: json['showMessage'] as String?,
      )
      ..editable = json['editable'] as bool?
      ..isValid = json['isValid'] as bool?
      ..isShowMessageTS = json['isShowMessageTS']
      ..itemMsgIsShow = json['itemMsgIsShow']
      ..undisplayItemNameList = json['undisplayItemNameList']
      ..masterId = json['masterId'] as String?
      ..unit = json['unit'] as String?
      ..percentage = json['percentage'] as String?
      ..optionObject =
          json['optionObject'] == null
              ? null
              : OptionObjModel.fromJson(
                json['optionObject'] as Map<String, dynamic>,
              )
      ..isEditPermissions = json['isEditPermissions'] as String?
      ..itemValue = json['itemValue']
      ..method = json['method'] as String?
      ..valueForShow = json['valueForShow']
      ..dataCounts = (json['dataCounts'] as num?)?.toInt()
      ..displayList = json['displayList'] as List<dynamic>?;

Map<String, dynamic> _$AssetItemListModelToJson(AssetItemListModel instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'subItemId': instance.subItemId,
      'itemIdStr': instance.itemIdStr,
      'subItemName': instance.subItemName,
      'subItemType': instance.subItemType,
      'displayItemName': instance.displayItemName,
      'tenantId': instance.tenantId,
      'assetTypeId': instance.assetTypeId,
      'itemName': instance.itemName,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'option': instance.option,
      'defaultData': instance.defaultData,
      'inputFlg': instance.inputFlg,
      'uneditableFlg': instance.uneditableFlg,
      'layoutSetFlg': instance.layoutSetFlg,
      'sectionName': instance.sectionName,
      'sectionType': instance.sectionType,
      'sectionSort': instance.sectionSort,
      'positionX': instance.positionX,
      'positionY': instance.positionY,
      'width': instance.width,
      'height': instance.height,
      'sysSetFlg': instance.sysSetFlg,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'mobileFlg': instance.mobileFlg,
      'replaceName': instance.replaceName,
      'itemIds': instance.itemIds,
      'sectionPrivateGroupsPermissionCheckLog':
          instance.sectionPrivateGroupsPermissionCheckLog,
      'sectionPrivateEditGroupsPermissionCheckLog':
          instance.sectionPrivateEditGroupsPermissionCheckLog,
      'sectionPrivateGroupsPermissionCheckId':
          instance.sectionPrivateGroupsPermissionCheckId,
      'sectionPrivateEditGroupsPermissionCheckId':
          instance.sectionPrivateEditGroupsPermissionCheckId,
      'sectionPrivateGroupsName': instance.sectionPrivateGroupsName,
      'sectionPrivateEditGroupsName': instance.sectionPrivateEditGroupsName,
      'appurtenancesInformationTypeNames':
          instance.appurtenancesInformationTypeNames,
      'masterTypeNames': instance.masterTypeNames,
      'dbtype': instance.dbtype,
      'commonItem': instance.commonItem,
      'isShowMessage': instance.isShowMessage,
      'showMessage': instance.showMessage,
      'editable': instance.editable,
      'isValid': instance.isValid,
      'isShowMessageTS': instance.isShowMessageTS,
      'itemMsgIsShow': instance.itemMsgIsShow,
      'undisplayItemNameList': instance.undisplayItemNameList,
      'masterId': instance.masterId,
      'unit': instance.unit,
      'percentage': instance.percentage,
      'optionObject': instance.optionObject?.toJson(),
      'isEditPermissions': instance.isEditPermissions,
      'itemValue': instance.itemValue,
      'method': instance.method,
      'valueForShow': instance.valueForShow,
      'dataCounts': instance.dataCounts,
      'displayList': instance.displayList,
    };
