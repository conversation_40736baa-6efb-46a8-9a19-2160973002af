// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'layout_setting_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LayoutSettingResponse _$LayoutSettingResponseFromJson(
  Map<String, dynamic> json,
) => LayoutSettingResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
  layoutSettingList:
      (json['layoutSettingList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : SharedLayoutSetting.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
);

Map<String, dynamic> _$LayoutSettingResponseToJson(
  LayoutSettingResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'layoutSettingList': instance.layoutSettingList,
};
