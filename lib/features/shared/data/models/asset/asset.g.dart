// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Asset _$AssetFromJson(Map<String, dynamic> json) => Asset(
  assetId: (json['assetId'] as num?)?.toInt(),
  externalCode: json['externalCode'] as String?,
  tenantId: json['tenantId'] as String?,
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  assetTypeName: json['assetTypeName'] as String?,
  groupIds: json['groupIds'] as String?,
  assetText: json['assetText'] as String?,
  layoutNo: json['layoutNo'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  state: json['state'] as String?,
  assetName: json['assetName'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  barcode: json['barcode'] as String?,
  assetLocation: json['assetLocation'] as String?,
  relationFlg: json['relationFlg'] as bool?,
  relationAssetDataList:
      (json['relationAssetDataList'] as List<dynamic>?)
          ?.map((e) => AssetRelation.fromJson(e as Map<String, dynamic>))
          .toList(),
  relationAssetIdList: json['relationAssetIdList'] as String?,
  dependentRelationAssetIdList:
      (json['dependentRelationAssetIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  message: json['message'] as String?,
  jurisdiction: (json['jurisdiction'] as num?)?.toInt(),
  insertAssetRelationColumnData:
      (json['insertAssetRelationColumnData'] as List<dynamic>?)
          ?.map((e) => AssetRelationColumn.fromJson(e as Map<String, dynamic>))
          .toList(),
  relationNotUpdateFlag: json['relationNotUpdateFlag'] as bool?,
  rfid: json['rfid'] as String?,
  copyAppurtenancesInformationTypeIds:
      json['copyAppurtenancesInformationTypeIds'] as String?,
  assetReservationStatusList:
      (json['assetReservationStatusList'] as List<dynamic>?)
          ?.map(
            (e) => AssetReservationStatus.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  assetItemList:
      (json['assetItemList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
  willChangedAmount: (json['willChangedAmount'] as num?)?.toInt(),
  interactionOperation: json['interactionOperation'] as String?,
  assetTextObj: json['assetTextObj'] as Map<String, dynamic>?,
  mobielDisplayList: json['mobielDisplayList'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AssetToJson(Asset instance) => <String, dynamic>{
  'assetId': instance.assetId,
  'externalCode': instance.externalCode,
  'tenantId': instance.tenantId,
  'assetTypeId': instance.assetTypeId,
  'assetTypeName': instance.assetTypeName,
  'groupIds': instance.groupIds,
  'assetText': instance.assetText,
  'assetTextObj': instance.assetTextObj,
  'layoutNo': instance.layoutNo,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'state': instance.state,
  'assetName': instance.assetName,
  'workflowId': instance.workflowId,
  'barcode': instance.barcode,
  'assetLocation': instance.assetLocation,
  'relationFlg': instance.relationFlg,
  'relationAssetDataList': instance.relationAssetDataList,
  'relationAssetIdList': instance.relationAssetIdList,
  'dependentRelationAssetIdList': instance.dependentRelationAssetIdList,
  'message': instance.message,
  'jurisdiction': instance.jurisdiction,
  'insertAssetRelationColumnData': instance.insertAssetRelationColumnData,
  'relationNotUpdateFlag': instance.relationNotUpdateFlag,
  'rfid': instance.rfid,
  'copyAppurtenancesInformationTypeIds':
      instance.copyAppurtenancesInformationTypeIds,
  'assetReservationStatusList': instance.assetReservationStatusList,
  'assetItemList': instance.assetItemList,
  'willChangedAmount': instance.willChangedAmount,
  'interactionOperation': instance.interactionOperation,
  'mobielDisplayList': instance.mobielDisplayList,
};
