// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_tenant_use_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedTenantUseStatusModel _$SharedTenantUseStatusModelFromJson(
  Map<String, dynamic> json,
) => SharedTenantUseStatusModel(
  tenantId: json['tenantId'] as String?,
  tenantCreatedDate: json['tenantCreatedDate'] as String?,
  contractPeriodStartDate: json['contractPeriodStartDate'] as String?,
  contractPeriodEndDate: json['contractPeriodEndDate'] as String?,
  termsAgreeDate: json['termsAgreeDate'] as String?,
  termsAgreeVersion: json['termsAgreeVersion'] as String?,
  plan: json['plan'] as String?,
  planDetail: json['planDetail'] as String?,
  assetNumberMax: json['assetNumberMax'] as String?,
  capacityMax: json['capacityMax'] as String?,
  dayAssetNumberMax: json['dayAssetNumberMax'] as String?,
  dayCapacityMax: json['dayCapacityMax'] as String?,
  monthAssetNumberMax: json['monthAssetNumberMax'] as String?,
  monthCapacityMax: json['monthCapacityMax'] as String?,
  billingDate: json['billingDate'] as String?,
  fieldText: json['fieldText'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
);

Map<String, dynamic> _$SharedTenantUseStatusModelToJson(
  SharedTenantUseStatusModel instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'tenantCreatedDate': instance.tenantCreatedDate,
  'contractPeriodStartDate': instance.contractPeriodStartDate,
  'contractPeriodEndDate': instance.contractPeriodEndDate,
  'termsAgreeDate': instance.termsAgreeDate,
  'termsAgreeVersion': instance.termsAgreeVersion,
  'plan': instance.plan,
  'planDetail': instance.planDetail,
  'assetNumberMax': instance.assetNumberMax,
  'capacityMax': instance.capacityMax,
  'dayAssetNumberMax': instance.dayAssetNumberMax,
  'dayCapacityMax': instance.dayCapacityMax,
  'monthAssetNumberMax': instance.monthAssetNumberMax,
  'monthCapacityMax': instance.monthCapacityMax,
  'billingDate': instance.billingDate,
  'fieldText': instance.fieldText,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
