// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_tenant_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedTenantModel _$SharedTenantModelFromJson(Map<String, dynamic> json) =>
    SharedTenantModel(
      tenantId: json['tenantId'] as String?,
      tenantName: json['tenantName'] as String?,
      managerMail: json['managerMail'] as String?,
      membership: (json['membership'] as num?)?.toInt(),
      organzationName: json['organzationName'] as String?,
      delStatus: json['delStatus'] as String?,
      delUser: json['delUser'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      disableFlg: json['disableFlg'] as String?,
      lockedFlg: json['lockedFlg'] as String?,
      databaseId: (json['databaseId'] as num?)?.toInt(),
      uuid: json['uuid'] as String?,
      enableTwoStep: json['enableTwoStep'] as bool?,
      ipSetting: json['ipSetting'] as String?,
      defaultDashboardId: (json['defaultDashboardId'] as num?)?.toInt(),
      accessKeyKbn: json['accessKeyKbn'] as String?,
      defaultSystem: json['defaultSystem'] as String?,
      printEnableFlg: json['printEnableFlg'] as String?,
      reportEnableFlg: json['reportEnableFlg'] as String?,
      actionScriptEnableFlg: json['actionScriptEnableFlg'] as String?,
      workbookEnableFlg: json['workbookEnableFlg'] as String?,
      useSystems: json['useSystems'] as String?,
      plan: json['plan'] as String?,
      planName: json['planName'] as String?,
      sourceTenantId: json['sourceTenantId'] as String?,
      sourceTenantName: json['sourceTenantName'] as String?,
      liveSection: json['liveSection'] as String?,
      relationAssetDependentFlg: json['relationAssetDependentFlg'] as String?,
      assetNumberMax: json['assetNumberMax'] as String?,
      capacityMax: json['capacityMax'] as String?,
      dayCapacityMax: json['dayCapacityMax'] as String?,
      maintenanceNoticeFlg: json['maintenanceNoticeFlg'] as String?,
      mfaMailEnableFlg: json['mfaMailEnableFlg'] as String?,
      mfaSmsEnableFlg: json['mfaSmsEnableFlg'] as String?,
      mfaType: json['mfaType'] as String?,
      mfaGroupIds: json['mfaGroupIds'] as String?,
      managerLastName: json['managerLastName'] as String?,
      managerFirstName: json['managerFirstName'] as String?,
      workbookLicenseInUse: (json['workbookLicenseInUse'] as num?)?.toInt(),
      workbookLicenseMax: json['workbookLicenseMax'] as String?,
      materialisticViewFlg: json['materialisticViewFlg'] as String?,
      openSearchFlg: json['openSearchFlg'] as String?,
      openSearchFlgSetDate: json['openSearchFlgSetDate'] as String?,
      zoneId: json['zoneId'] as String?,
      zoneName: json['zoneName'] as String?,
      enableSso: json['enableSso'] as bool?,
      allowEnableSso: json['allowEnableSso'] as String?,
      productKbn: json['productKbn'] as String?,
      usePurpose: json['usePurpose'] as String?,
      flLastName: json['flLastName'] as String?,
      flFirstName: json['flFirstName'] as String?,
      idpName: json['idpName'] as String?,
    );

Map<String, dynamic> _$SharedTenantModelToJson(SharedTenantModel instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'tenantName': instance.tenantName,
      'managerMail': instance.managerMail,
      'membership': instance.membership,
      'organzationName': instance.organzationName,
      'delStatus': instance.delStatus,
      'delUser': instance.delUser,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'disableFlg': instance.disableFlg,
      'lockedFlg': instance.lockedFlg,
      'databaseId': instance.databaseId,
      'uuid': instance.uuid,
      'enableTwoStep': instance.enableTwoStep,
      'ipSetting': instance.ipSetting,
      'defaultDashboardId': instance.defaultDashboardId,
      'accessKeyKbn': instance.accessKeyKbn,
      'defaultSystem': instance.defaultSystem,
      'printEnableFlg': instance.printEnableFlg,
      'reportEnableFlg': instance.reportEnableFlg,
      'actionScriptEnableFlg': instance.actionScriptEnableFlg,
      'workbookEnableFlg': instance.workbookEnableFlg,
      'useSystems': instance.useSystems,
      'plan': instance.plan,
      'planName': instance.planName,
      'sourceTenantId': instance.sourceTenantId,
      'sourceTenantName': instance.sourceTenantName,
      'liveSection': instance.liveSection,
      'relationAssetDependentFlg': instance.relationAssetDependentFlg,
      'assetNumberMax': instance.assetNumberMax,
      'capacityMax': instance.capacityMax,
      'dayCapacityMax': instance.dayCapacityMax,
      'maintenanceNoticeFlg': instance.maintenanceNoticeFlg,
      'mfaMailEnableFlg': instance.mfaMailEnableFlg,
      'mfaSmsEnableFlg': instance.mfaSmsEnableFlg,
      'mfaType': instance.mfaType,
      'mfaGroupIds': instance.mfaGroupIds,
      'managerLastName': instance.managerLastName,
      'managerFirstName': instance.managerFirstName,
      'workbookLicenseInUse': instance.workbookLicenseInUse,
      'workbookLicenseMax': instance.workbookLicenseMax,
      'materialisticViewFlg': instance.materialisticViewFlg,
      'openSearchFlg': instance.openSearchFlg,
      'openSearchFlgSetDate': instance.openSearchFlgSetDate,
      'zoneId': instance.zoneId,
      'zoneName': instance.zoneName,
      'enableSso': instance.enableSso,
      'allowEnableSso': instance.allowEnableSso,
      'productKbn': instance.productKbn,
      'usePurpose': instance.usePurpose,
      'flLastName': instance.flLastName,
      'flFirstName': instance.flFirstName,
      'idpName': instance.idpName,
    };
