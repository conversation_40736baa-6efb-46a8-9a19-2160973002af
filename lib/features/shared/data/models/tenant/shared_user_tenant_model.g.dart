// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_user_tenant_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedUserTenantModel _$SharedUserTenantModelFromJson(
  Map<String, dynamic> json,
) => SharedUserTenantModel(
  tenantList:
      (json['tenantList'] as List<dynamic>?)
          ?.map((e) => SharedTenantModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  tenantUseStatesList:
      (json['tenantUseStatesList'] as List<dynamic>?)
          ?.map(
            (e) =>
                SharedTenantUseStatusModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  userTenantList:
      (json['userTenantList'] as List<dynamic>?)
          ?.map((e) => SharedTenantModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  systemUrl: json['systemUrl'] as String?,
  liveUrl: json['liveUrl'] as String?,
  afiotUrl: json['afiotUrl'] as String?,
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
);

Map<String, dynamic> _$SharedUserTenantModelToJson(
  SharedUserTenantModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'tenantList': instance.tenantList,
  'tenantUseStatesList': instance.tenantUseStatesList,
  'userTenantList': instance.userTenantList,
  'systemUrl': instance.systemUrl,
  'liveUrl': instance.liveUrl,
  'afiotUrl': instance.afiotUrl,
};
