// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tenant_info_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TenantInfoResponse _$TenantInfoResponseFromJson(Map<String, dynamic> json) =>
    TenantInfoResponse(
      json['msg'] as String,
      json['hasAssetLocation'] as bool?,
      (json['assetScanListSetting'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : TenantInfoResponseAssetScanListSetting.fromJson(
                      e as Map<String, dynamic>,
                    ),
          )
          .toList(),
      (json['code'] as num).toInt(),
      json['barcodeExtraction'] as String?,
      json['planName'] as String?,
      json['tenant'] == null
          ? null
          : TenantInfoResponseTenant.fromJson(
            json['tenant'] as Map<String, dynamic>,
          ),
      json['policy'] == null
          ? null
          : TenantInfoResponsePolicy.fromJson(
            json['policy'] as Map<String, dynamic>,
          ),
    );

Map<String, dynamic> _$TenantInfoResponseToJson(TenantInfoResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'hasAssetLocation': instance.hasAssetLocation,
      'assetScanListSetting': instance.assetScanListSetting,
      'barcodeExtraction': instance.barcodeExtraction,
      'planName': instance.planName,
      'tenant': instance.tenant,
      'policy': instance.policy,
    };

TenantInfoResponseAssetScanListSetting
_$TenantInfoResponseAssetScanListSettingFromJson(Map<String, dynamic> json) =>
    TenantInfoResponseAssetScanListSetting(
      json['tenantId'] as String?,
      (json['scanLevelId'] as num?)?.toInt(),
      (json['scanItemId'] as num?)?.toInt(),
      json['itemName'],
      json['itemDisplayName'],
      json['itemType'],
      json['itemOption'],
      json['createdById'],
      json['createdDate'],
      json['modifiedById'],
      json['modifiedDate'] as String?,
    );

Map<String, dynamic> _$TenantInfoResponseAssetScanListSettingToJson(
  TenantInfoResponseAssetScanListSetting instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'scanLevelId': instance.scanLevelId,
  'scanItemId': instance.scanItemId,
  'itemName': instance.itemName,
  'itemDisplayName': instance.itemDisplayName,
  'itemType': instance.itemType,
  'itemOption': instance.itemOption,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};

TenantInfoResponseTenant _$TenantInfoResponseTenantFromJson(
  Map<String, dynamic> json,
) => TenantInfoResponseTenant(
  json['tenantId'] as String?,
  json['tenantName'] as String?,
  json['managerMail'] as String?,
  (json['membership'] as num?)?.toInt(),
  json['organzationName'],
  json['delStatus'],
  json['delUser'],
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['disableFlg'],
  json['lockedFlg'],
  json['databaseId'],
  json['uuid'] as String?,
  json['enableTwoStep'] as bool?,
  json['ipSetting'] as String?,
  json['defaultDashboardId'],
  json['accessKeyKbn'] as String?,
  json['defaultSystem'] as String?,
  json['printEnableFlg'] as String?,
  json['reportEnableFlg'] as String?,
  json['actionScriptEnableFlg'] as String?,
  json['workbookEnableFlg'] as String?,
  json['useSystems'] as String?,
  json['plan'] as String?,
  json['planName'],
  json['sourceTenantId'],
  json['sourceTenantName'],
  json['liveSection'] as String?,
  json['relationAssetDependentFlg'] as String?,
  json['assetNumberMax'],
  json['capacityMax'],
  json['dayCapacityMax'],
  json['maintenanceNoticeFlg'],
  json['mfaMailEnableFlg'] as String?,
  json['mfaSmsEnableFlg'] as String?,
  json['mfaType'],
  json['mfaGroupIds'],
  json['managerLastName'],
  json['managerFirstName'],
  json['workbookLicenseInUse'],
  json['workbookLicenseMax'],
  json['materialisticViewFlg'] as String?,
  json['openSearchFlg'],
  json['openSearchFlgSetDate'],
  json['zoneId'],
  json['zoneName'],
  json['enableSso'] as bool?,
  json['allowEnableSso'],
  json['productKbn'],
  json['usePurpose'],
  json['flLastName'],
  json['flFirstName'],
  json['idpName'],
  (json['openSearchValidDelayDays'] as num?)?.toInt(),
  json['openSearch'] as bool?,
);

Map<String, dynamic> _$TenantInfoResponseTenantToJson(
  TenantInfoResponseTenant instance,
) => <String, dynamic>{
  'tenantId': instance.tenantId,
  'tenantName': instance.tenantName,
  'managerMail': instance.managerMail,
  'membership': instance.membership,
  'organzationName': instance.organzationName,
  'delStatus': instance.delStatus,
  'delUser': instance.delUser,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'disableFlg': instance.disableFlg,
  'lockedFlg': instance.lockedFlg,
  'databaseId': instance.databaseId,
  'uuid': instance.uuid,
  'enableTwoStep': instance.enableTwoStep,
  'ipSetting': instance.ipSetting,
  'defaultDashboardId': instance.defaultDashboardId,
  'accessKeyKbn': instance.accessKeyKbn,
  'defaultSystem': instance.defaultSystem,
  'printEnableFlg': instance.printEnableFlg,
  'reportEnableFlg': instance.reportEnableFlg,
  'actionScriptEnableFlg': instance.actionScriptEnableFlg,
  'workbookEnableFlg': instance.workbookEnableFlg,
  'useSystems': instance.useSystems,
  'plan': instance.plan,
  'planName': instance.planName,
  'sourceTenantId': instance.sourceTenantId,
  'sourceTenantName': instance.sourceTenantName,
  'liveSection': instance.liveSection,
  'relationAssetDependentFlg': instance.relationAssetDependentFlg,
  'assetNumberMax': instance.assetNumberMax,
  'capacityMax': instance.capacityMax,
  'dayCapacityMax': instance.dayCapacityMax,
  'maintenanceNoticeFlg': instance.maintenanceNoticeFlg,
  'mfaMailEnableFlg': instance.mfaMailEnableFlg,
  'mfaSmsEnableFlg': instance.mfaSmsEnableFlg,
  'mfaType': instance.mfaType,
  'mfaGroupIds': instance.mfaGroupIds,
  'managerLastName': instance.managerLastName,
  'managerFirstName': instance.managerFirstName,
  'workbookLicenseInUse': instance.workbookLicenseInUse,
  'workbookLicenseMax': instance.workbookLicenseMax,
  'materialisticViewFlg': instance.materialisticViewFlg,
  'openSearchFlg': instance.openSearchFlg,
  'openSearchFlgSetDate': instance.openSearchFlgSetDate,
  'zoneId': instance.zoneId,
  'zoneName': instance.zoneName,
  'enableSso': instance.enableSso,
  'allowEnableSso': instance.allowEnableSso,
  'productKbn': instance.productKbn,
  'usePurpose': instance.usePurpose,
  'flLastName': instance.flLastName,
  'flFirstName': instance.flFirstName,
  'idpName': instance.idpName,
  'openSearchValidDelayDays': instance.openSearchValidDelayDays,
  'openSearch': instance.openSearch,
};

TenantInfoResponsePolicy _$TenantInfoResponsePolicyFromJson(
  Map<String, dynamic> json,
) => TenantInfoResponsePolicy(
  (json['policyId'] as num?)?.toInt(),
  json['tenantId'] as String?,
  json['expireTime'],
  json['mustContain'] as String?,
  json['enableFlg'] as String?,
  (json['length'] as num?)?.toInt(),
  json['repeatForbidCount'],
  json['lockEnableFlg'] as String?,
  (json['lockTimes'] as num?)?.toInt(),
  json['lockPeriod'],
  (json['strengthBase'] as num?)?.toDouble(),
  (json['strengthLength'] as num?)?.toInt(),
  json['strength'] as String?,
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['tenantName'],
);

Map<String, dynamic> _$TenantInfoResponsePolicyToJson(
  TenantInfoResponsePolicy instance,
) => <String, dynamic>{
  'policyId': instance.policyId,
  'tenantId': instance.tenantId,
  'expireTime': instance.expireTime,
  'mustContain': instance.mustContain,
  'enableFlg': instance.enableFlg,
  'length': instance.length,
  'repeatForbidCount': instance.repeatForbidCount,
  'lockEnableFlg': instance.lockEnableFlg,
  'lockTimes': instance.lockTimes,
  'lockPeriod': instance.lockPeriod,
  'strengthBase': instance.strengthBase,
  'strengthLength': instance.strengthLength,
  'strength': instance.strength,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'tenantName': instance.tenantName,
};
