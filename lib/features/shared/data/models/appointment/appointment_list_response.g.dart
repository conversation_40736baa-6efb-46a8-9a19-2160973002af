// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentListResponse _$AppointmentListResponseFromJson(
  Map<String, dynamic> json,
) => AppointmentListResponse(
  (json['code'] as num).toInt(),
  json['msg'] as String,
  (json['reservationItemCommonList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null
                ? null
                : ReservationItemCommon.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
  (json['extraReservationItemCommonList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null
                ? null
                : ReservationItemCommon.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
  (json['eventTypeList'] as List<dynamic>?)
      ?.map(
        (e) => e == null ? null : EventType.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
  (json['appointmentList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null ? null : Appointment.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
);

Map<String, dynamic> _$AppointmentListResponseToJson(
  AppointmentListResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'reservationItemCommonList': instance.reservationItemCommonList,
  'extraReservationItemCommonList': instance.extraReservationItemCommonList,
  'eventTypeList': instance.eventTypeList,
  'appointmentList': instance.appointmentList,
};

Appointment _$AppointmentFromJson(Map<String, dynamic> json) =>
    Appointment(
        reservationNo: (json['reservationNo'] as num?)?.toInt(),
        assetId: (json['assetId'] as num?)?.toInt(),
        assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
        reservationName: json['reservationName'] as String?,
        tenantId: json['tenantId'] as String?,
        start: json['start'] as String?,
        end: json['end'] as String?,
        eventTypeId: (json['eventTypeId'] as num?)?.toInt(),
        eventTypeName: json['eventTypeName'] as String?,
        color: json['color'] as String?,
        duplicateFlg: json['duplicateFlg'] as String?,
        reservationPeriod: (json['reservationPeriod'] as num?)?.toInt(),
        autoDeletePeriod: (json['autoDeletePeriod'] as num?)?.toInt(),
        reservationText: json['reservationText'] as String?,
        assetText: json['assetText'] as String?,
        createdById: json['createdById'] as String?,
        createdByName: json['createdByName'] as String?,
        createdDate: json['createdDate'] as String?,
        modifiedById: json['modifiedById'] as String?,
        modifiedDate: json['modifiedDate'] as String?,
        extraCommonText: json['extraCommonText'] as String?,
        unitDay: json['unitDay'] as String?,
        alertSetting: json['alertSetting'] as String?,
        alertData: json['alertData'],
        extraCommonTextObj: json['extraCommonTextObj'],
        reservationTextObj: json['reservationTextObj'],
        isShowMessage: json['isShowMessage'] as bool?,
        showMessage: json['showMessage'] as String?,
        inputRequired: json['inputRequired'] as bool?,
        inputLimitMaxDate: json['inputLimitMaxDate'] as String?,
        inputLimitMinDate: json['inputLimitMinDate'] as String?,
      )
      ..headReservationName = json['headReservationName'] as String?
      ..headEventTypeName = json['headEventTypeName'] as String?
      ..headStart = json['headStart']
      ..headEnd = json['headEnd'];

Map<String, dynamic> _$AppointmentToJson(Appointment instance) =>
    <String, dynamic>{
      'reservationNo': instance.reservationNo,
      'assetId': instance.assetId,
      'assetTypeId': instance.assetTypeId,
      'reservationName': instance.reservationName,
      'tenantId': instance.tenantId,
      'start': instance.start,
      'end': instance.end,
      'eventTypeId': instance.eventTypeId,
      'eventTypeName': instance.eventTypeName,
      'color': instance.color,
      'duplicateFlg': instance.duplicateFlg,
      'reservationPeriod': instance.reservationPeriod,
      'autoDeletePeriod': instance.autoDeletePeriod,
      'reservationText': instance.reservationText,
      'assetText': instance.assetText,
      'createdById': instance.createdById,
      'createdByName': instance.createdByName,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'extraCommonText': instance.extraCommonText,
      'unitDay': instance.unitDay,
      'alertSetting': instance.alertSetting,
      'alertData': instance.alertData,
      'extraCommonTextObj': instance.extraCommonTextObj,
      'reservationTextObj': instance.reservationTextObj,
      'isShowMessage': instance.isShowMessage,
      'showMessage': instance.showMessage,
      'inputRequired': instance.inputRequired,
      'inputLimitMaxDate': instance.inputLimitMaxDate,
      'inputLimitMinDate': instance.inputLimitMinDate,
      'headReservationName': instance.headReservationName,
      'headEventTypeName': instance.headEventTypeName,
      'headStart': instance.headStart,
      'headEnd': instance.headEnd,
    };

ReservationItemCommon _$ReservationItemCommonFromJson(
  Map<String, dynamic> json,
) => ReservationItemCommon(
  (json['itemId'] as num?)?.toInt(),
  json['tenantId'] as String?,
  json['itemLabel'] as String?,
  json['itemDisplayName'] as String?,
  json['itemType'] as String?,
  json['itemVal'] as String?,
  json['masterDisplayItemDisplayName'] as String?,
  json['defaultData'],
  json['option'] as String?,
  json['isRequired'] as String?,
  (json['itemSort'] as num?)?.toInt(),
  json['sysSetFlg'] as String?,
  json['mobileFlg'] as String?,
  (json['createdById'] as num?)?.toInt(),
  json['createdDate'] as String?,
  (json['modifiedById'] as num?)?.toInt(),
  json['modifiedDate'] as String?,
  json['itemValObj'],
);

Map<String, dynamic> _$ReservationItemCommonToJson(
  ReservationItemCommon instance,
) => <String, dynamic>{
  'itemId': instance.itemId,
  'tenantId': instance.tenantId,
  'itemLabel': instance.itemLabel,
  'itemDisplayName': instance.itemDisplayName,
  'itemType': instance.itemType,
  'itemVal': instance.itemVal,
  'masterDisplayItemDisplayName': instance.masterDisplayItemDisplayName,
  'defaultData': instance.defaultData,
  'option': instance.option,
  'isRequired': instance.isRequired,
  'itemSort': instance.itemSort,
  'sysSetFlg': instance.sysSetFlg,
  'mobileFlg': instance.mobileFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'itemValObj': instance.itemValObj,
};

EventType _$EventTypeFromJson(Map<String, dynamic> json) => EventType(
  key: json['key'] as String?,
  value: json['value'] as String?,
  label: json['label'] as String?,
  duplicateFlg: json['duplicateFlg'] as String?,
  reservationPeriod: (json['reservationPeriod'] as num?)?.toInt(),
  autoDeletePeriodFlg: json['autoDeletePeriodFlg'] as String?,
  autoDeletePeriod: (json['autoDeletePeriod'] as num?)?.toInt(),
  itemList:
      (json['itemList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : EventTypeItem.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  layoutSettingList:
      (json['layoutSettingList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : EventTypeLayoutSetting.fromJson(
                      e as Map<String, dynamic>,
                    ),
          )
          .toList(),
);

Map<String, dynamic> _$EventTypeToJson(EventType instance) => <String, dynamic>{
  'key': instance.key,
  'value': instance.value,
  'label': instance.label,
  'duplicateFlg': instance.duplicateFlg,
  'reservationPeriod': instance.reservationPeriod,
  'autoDeletePeriodFlg': instance.autoDeletePeriodFlg,
  'autoDeletePeriod': instance.autoDeletePeriod,
  'itemList': instance.itemList,
  'layoutSettingList': instance.layoutSettingList,
};

EventTypeItem _$EventTypeItemFromJson(Map<String, dynamic> json) =>
    EventTypeItem(
      (json['tenantId'] as num?)?.toInt(),
      (json['itemId'] as num?)?.toInt(),
      (json['eventTypeId'] as num?)?.toInt(),
      json['eventTypeName'] as String?,
      json['itemLabel'] as String?,
      json['itemDisplayName'] as String?,
      json['itemType'] as String?,
      json['itemVal'] as String?,
      json['masterDisplayItemDisplayName'] as String?,
      json['defaultData'],
      json['isRequired'] as String?,
      json['itemSort'] as String?,
      json['sysSetFlg'] as String?,
      json['eventItemIndex'] as String?,
      (json['positionX'] as num?)?.toInt(),
      (json['positionY'] as num?)?.toInt(),
      (json['width'] as num?)?.toInt(),
      (json['height'] as num?)?.toInt(),
      json['createdById'] as String?,
      json['createdDate'] as String?,
      json['modifiedById'] as String?,
      json['modifiedDate'] as String?,
      json['replaceName'] as String?,
      json['isCommon'] as String?,
      json['mobileFlg'] as String?,
      json['dbtype'] as String?,
      json['option'] as String?,
    )..itemValObj = json['itemValObj'];

Map<String, dynamic> _$EventTypeItemToJson(EventTypeItem instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'itemId': instance.itemId,
      'eventTypeId': instance.eventTypeId,
      'eventTypeName': instance.eventTypeName,
      'itemLabel': instance.itemLabel,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'itemVal': instance.itemVal,
      'masterDisplayItemDisplayName': instance.masterDisplayItemDisplayName,
      'defaultData': instance.defaultData,
      'option': instance.option,
      'isRequired': instance.isRequired,
      'itemSort': instance.itemSort,
      'sysSetFlg': instance.sysSetFlg,
      'eventItemIndex': instance.eventItemIndex,
      'positionX': instance.positionX,
      'positionY': instance.positionY,
      'width': instance.width,
      'height': instance.height,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'replaceName': instance.replaceName,
      'isCommon': instance.isCommon,
      'mobileFlg': instance.mobileFlg,
      'dbtype': instance.dbtype,
      'itemValObj': instance.itemValObj,
    };

EventTypeLayoutSetting _$EventTypeLayoutSettingFromJson(
  Map<String, dynamic> json,
) => EventTypeLayoutSetting(
  (json['itemId'] as num?)?.toInt(),
  (json['subItemId'] as num?)?.toInt(),
  (json['tenantId'] as num?)?.toInt(),
  (json['classification'] as num?)?.toInt(),
  (json['typeId'] as num?)?.toInt(),
  json['itemName'] as String?,
  json['itemDisplayName'] as String?,
  json['subItemName'] as String?,
  json['subItemDisplayName'] as String?,
  json['displayItemName'] as String?,
  json['itemType'] as String?,
  json['option'] as String?,
  json['defaultData'] as String?,
  json['inputFlg'] as String?,
  json['uneditableFlg'] as String?,
  json['mobileFlg'] as String?,
  json['sectionName'] as String?,
  json['sectionSort'] as String?,
  (json['positionX'] as num?)?.toInt(),
  (json['positionY'] as num?)?.toInt(),
  (json['width'] as num?)?.toInt(),
  (json['height'] as num?)?.toInt(),
  json['sysSetFlg'] as String?,
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['replaceName'] as String?,
  json['columnFlg'] as String?,
  json['logContext'] as String?,
  json['dbtype'] as String?,
);

Map<String, dynamic> _$EventTypeLayoutSettingToJson(
  EventTypeLayoutSetting instance,
) => <String, dynamic>{
  'itemId': instance.itemId,
  'subItemId': instance.subItemId,
  'tenantId': instance.tenantId,
  'classification': instance.classification,
  'typeId': instance.typeId,
  'itemName': instance.itemName,
  'itemDisplayName': instance.itemDisplayName,
  'subItemName': instance.subItemName,
  'subItemDisplayName': instance.subItemDisplayName,
  'displayItemName': instance.displayItemName,
  'itemType': instance.itemType,
  'option': instance.option,
  'defaultData': instance.defaultData,
  'inputFlg': instance.inputFlg,
  'uneditableFlg': instance.uneditableFlg,
  'mobileFlg': instance.mobileFlg,
  'sectionName': instance.sectionName,
  'sectionSort': instance.sectionSort,
  'positionX': instance.positionX,
  'positionY': instance.positionY,
  'width': instance.width,
  'height': instance.height,
  'sysSetFlg': instance.sysSetFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'replaceName': instance.replaceName,
  'columnFlg': instance.columnFlg,
  'logContext': instance.logContext,
  'dbtype': instance.dbtype,
};
