// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_arinfo.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedArInfo _$SharedArInfoFromJson(Map<String, dynamic> json) => SharedArInfo(
  quantity: (json['quantity'] as num?)?.toInt(),
  assetName: json['assetName'] as String?,
  count: (json['count'] as num?)?.toInt(),
  isScanAgin: json['isScanAgin'] as bool?,
  arColor: json['arColor'] as String?,
  location: json['location'] as String?,
  scanItems:
      (json['scanItems'] as List<dynamic>?)
          ?.map((e) => SharedItemType.fromJson(e as Map<String, dynamic>))
          .toList(),
  assetId: json['assetId'] as String?,
  barCode: json['barCode'] as String?,
  homeImageUrl: json['homeImageUrl'] as String?,
  homeImageUrlForShow: json['homeImageUrlForShow'] as String?,
  assetTypeId: json['assetTypeId'] as String?,
);

Map<String, dynamic> _$SharedArInfoToJson(SharedArInfo instance) =>
    <String, dynamic>{
      'quantity': instance.quantity,
      'assetName': instance.assetName,
      'count': instance.count,
      'isScanAgin': instance.isScanAgin,
      'arColor': instance.arColor,
      'location': instance.location,
      'scanItems': instance.scanItems,
      'assetId': instance.assetId,
      'barCode': instance.barCode,
      'homeImageUrl': instance.homeImageUrl,
      'homeImageUrlForShow': instance.homeImageUrlForShow,
      'assetTypeId': instance.assetTypeId,
    };
