// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_layout_setting_0_5.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedLayoutSetting05Response _$SharedLayoutSetting05ResponseFromJson(
  Map<String, dynamic> json,
) => SharedLayoutSetting05Response(
  layoutSettingList:
      (json['layoutSettingList'] as List<dynamic>?)
          ?.map(
            (e) => SharedLayoutSetting05.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
);

Map<String, dynamic> _$SharedLayoutSetting05ResponseToJson(
  SharedLayoutSetting05Response instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'layoutSettingList': instance.layoutSettingList,
};

SharedLayoutSetting05 _$SharedLayoutSetting05FromJson(
  Map<String, dynamic> json,
) => SharedLayoutSetting05(
  itemId: (json['itemId'] as num?)?.toInt(),
  subItemId: (json['subItemId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  classification: (json['classification'] as num?)?.toInt(),
  typeId: (json['typeId'] as num?)?.toInt(),
  itemName: json['itemName'] as String?,
  itemDisplayName: json['itemDisplayName'] as String?,
  subItemName: json['subItemName'] as String?,
  subItemDisplayName: json['subItemDisplayName'] as String?,
  displayItemName: json['displayItemName'] as String?,
  itemType: json['itemType'] as String?,
  option: json['option'] as String?,
  defaultData: json['defaultData'] as String?,
  inputFlg: json['inputFlg'] as String?,
  uneditableFlg: json['uneditableFlg'] as String?,
  mobileFlg: json['mobileFlg'] as String?,
  sectionName: json['sectionName'] as String?,
  sectionSort: (json['sectionSort'] as num?)?.toDouble(),
  positionX: (json['positionX'] as num?)?.toInt(),
  positionY: (json['positionY'] as num?)?.toInt(),
  width: (json['width'] as num?)?.toInt(),
  height: (json['height'] as num?)?.toInt(),
  sysSetFlg: json['sysSetFlg'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  dbType: json['dbtype'] as String?,
  replaceName: json['replaceName'] as String?,
  columnFlg: json['columnFlg'] as String?,
  logContext: json['logContext'] as String?,
);

Map<String, dynamic> _$SharedLayoutSetting05ToJson(
  SharedLayoutSetting05 instance,
) => <String, dynamic>{
  'itemId': instance.itemId,
  'subItemId': instance.subItemId,
  'tenantId': instance.tenantId,
  'classification': instance.classification,
  'typeId': instance.typeId,
  'itemName': instance.itemName,
  'itemDisplayName': instance.itemDisplayName,
  'subItemName': instance.subItemName,
  'subItemDisplayName': instance.subItemDisplayName,
  'displayItemName': instance.displayItemName,
  'itemType': instance.itemType,
  'option': instance.option,
  'defaultData': instance.defaultData,
  'inputFlg': instance.inputFlg,
  'uneditableFlg': instance.uneditableFlg,
  'mobileFlg': instance.mobileFlg,
  'sectionName': instance.sectionName,
  'sectionSort': instance.sectionSort,
  'positionX': instance.positionX,
  'positionY': instance.positionY,
  'width': instance.width,
  'height': instance.height,
  'sysSetFlg': instance.sysSetFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'dbtype': instance.dbType,
  'replaceName': instance.replaceName,
  'columnFlg': instance.columnFlg,
  'logContext': instance.logContext,
};
