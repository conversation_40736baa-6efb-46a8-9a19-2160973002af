// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_s3_upload_url_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedS3UploadUrlResponse _$SharedS3UploadUrlResponseFromJson(
  Map<String, dynamic> json,
) => SharedS3UploadUrlResponse(
  data:
      json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
);

Map<String, dynamic> _$SharedS3UploadUrlResponseToJson(
  SharedS3UploadUrlResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'data': instance.data,
};

Data _$DataFromJson(Map<String, dynamic> json) => Data(
  getUrl: json['getUrl'] as String?,
  path: json['path'] as String?,
  url: json['url'] as String?,
);

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
  'getUrl': instance.getUrl,
  'path': instance.path,
  'url': instance.url,
};
