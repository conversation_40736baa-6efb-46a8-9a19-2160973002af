// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_layout_setting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedLayoutSetting _$SharedLayoutSettingFromJson(Map<String, dynamic> json) =>
    SharedLayoutSetting(
        subItemType: json['subItemType'] as String?,
        assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
        itemIds:
            (json['itemIds'] as List<dynamic>?)
                ?.map((e) => (e as num).toInt())
                .toList(),
        sectionPrivateGroupsPermissionCheckLog:
            json['sectionPrivateGroupsPermissionCheckLog'] as String?,
        sectionPrivateEditGroupsPermissionCheckLog:
            json['sectionPrivateEditGroupsPermissionCheckLog'] as String?,
        sectionPrivateGroupsPermissionCheckId:
            json['sectionPrivateGroupsPermissionCheckId'] as String?,
        sectionPrivateEditGroupsPermissionCheckId:
            json['sectionPrivateEditGroupsPermissionCheckId'] as String?,
        sectionPrivateGroupsName: json['sectionPrivateGroupsName'] as String?,
        sectionPrivateEditGroupsName:
            json['sectionPrivateEditGroupsName'] as String?,
        appurtenancesInformationTypeNames:
            json['appurtenancesInformationTypeNames'] as String?,
        masterTypeNames: json['masterTypeNames'] as String?,
        itemId: (json['itemId'] as num?)?.toInt(),
        subItemId: (json['subItemId'] as num?)?.toInt(),
        tenantId: json['tenantId'] as String?,
        classification: (json['classification'] as num?)?.toInt(),
        typeId: (json['typeId'] as num?)?.toInt(),
        itemName: json['itemName'] as String?,
        itemDisplayName: json['itemDisplayName'] as String?,
        subItemName: json['subItemName'] as String?,
        subItemDisplayName: json['subItemDisplayName'] as String?,
        displayItemName: json['displayItemName'] as String?,
        itemType: json['itemType'] as String?,
        option: json['option'] as String?,
        defaultData: json['defaultData'],
        inputFlg: json['inputFlg'] as String?,
        uneditableFlg: json['uneditableFlg'] as String?,
        mobileFlg: json['mobileFlg'] as String?,
        sectionName: json['sectionName'] as String?,
        sectionSort: (json['sectionSort'] as num?)?.toDouble(),
        positionX: (json['positionX'] as num?)?.toInt(),
        positionY: (json['positionY'] as num?)?.toInt(),
        width: (json['width'] as num?)?.toInt(),
        height: (json['height'] as num?)?.toInt(),
        sysSetFlg: json['sysSetFlg'] as String?,
        createdById: json['createdById'] as String?,
        createdDate: json['createdDate'] as String?,
        modifiedById: json['modifiedById'] as String?,
        modifiedDate: json['modifiedDate'] as String?,
        dbType: json['dbtype'] as String?,
        replaceName: json['replaceName'] as String?,
        columnFlg: json['columnFlg'] as String?,
        logContext: json['logContext'] as String?,
        optionObj:
            json['optionObj'] == null
                ? null
                : OptionObjModel.fromJson(
                  json['optionObj'] as Map<String, dynamic>,
                ),
        masterId: json['masterId'] as String?,
        valueForShow: json['valueForShow'],
      )
      ..optionMasterObj =
          json['optionMasterObj'] == null
              ? null
              : OptionObjModel.fromJson(
                json['optionMasterObj'] as Map<String, dynamic>,
              )
      ..subInfo =
          json['subInfo'] == null
              ? null
              : MasterDisplayItemModel.fromJson(
                json['subInfo'] as Map<String, dynamic>,
              );

Map<String, dynamic> _$SharedLayoutSettingToJson(
  SharedLayoutSetting instance,
) => <String, dynamic>{
  'itemId': instance.itemId,
  'subItemId': instance.subItemId,
  'tenantId': instance.tenantId,
  'classification': instance.classification,
  'typeId': instance.typeId,
  'itemName': instance.itemName,
  'itemDisplayName': instance.itemDisplayName,
  'subItemName': instance.subItemName,
  'subItemDisplayName': instance.subItemDisplayName,
  'displayItemName': instance.displayItemName,
  'itemType': instance.itemType,
  'option': instance.option,
  'defaultData': instance.defaultData,
  'inputFlg': instance.inputFlg,
  'uneditableFlg': instance.uneditableFlg,
  'mobileFlg': instance.mobileFlg,
  'sectionName': instance.sectionName,
  'sectionSort': instance.sectionSort,
  'positionX': instance.positionX,
  'positionY': instance.positionY,
  'width': instance.width,
  'height': instance.height,
  'sysSetFlg': instance.sysSetFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'dbtype': instance.dbType,
  'replaceName': instance.replaceName,
  'columnFlg': instance.columnFlg,
  'logContext': instance.logContext,
  'subItemType': instance.subItemType,
  'assetTypeId': instance.assetTypeId,
  'itemIds': instance.itemIds,
  'sectionPrivateGroupsPermissionCheckLog':
      instance.sectionPrivateGroupsPermissionCheckLog,
  'sectionPrivateEditGroupsPermissionCheckLog':
      instance.sectionPrivateEditGroupsPermissionCheckLog,
  'sectionPrivateGroupsPermissionCheckId':
      instance.sectionPrivateGroupsPermissionCheckId,
  'sectionPrivateEditGroupsPermissionCheckId':
      instance.sectionPrivateEditGroupsPermissionCheckId,
  'sectionPrivateGroupsName': instance.sectionPrivateGroupsName,
  'sectionPrivateEditGroupsName': instance.sectionPrivateEditGroupsName,
  'appurtenancesInformationTypeNames':
      instance.appurtenancesInformationTypeNames,
  'masterTypeNames': instance.masterTypeNames,
  'optionObj': instance.optionObj,
  'optionMasterObj': instance.optionMasterObj,
  'subInfo': instance.subInfo,
  'masterId': instance.masterId,
  'valueForShow': instance.valueForShow,
};
