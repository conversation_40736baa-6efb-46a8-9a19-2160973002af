// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_asset_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedAssetItem _$SharedAssetItemFromJson(Map<String, dynamic> json) =>
    SharedAssetItem(
      itemId: (json['itemId'] as num?)?.toInt(),
      itemDisplayName: json['itemDisplayName'] as String?,
      itemType: json['itemType'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$SharedAssetItemToJson(SharedAssetItem instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'itemDisplayName': instance.itemDisplayName,
      'itemType': instance.itemType,
      'value': instance.value,
    };
