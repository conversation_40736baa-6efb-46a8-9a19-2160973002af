// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_role_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserRoleResponse _$UserRoleResponseFromJson(Map<String, dynamic> json) =>
    UserRoleResponse(
      json['msg'],
      json['code'],
      (json['data'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : UserRoleResponseData.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
    );

Map<String, dynamic> _$UserRoleResponseToJson(UserRoleResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

UserRoleResponseData _$UserRoleResponseDataFromJson(
  Map<String, dynamic> json,
) => UserRoleResponseData(
  json['userName'] as String?,
  (json['userId'] as num?)?.toInt(),
  (json['roleId'] as num?)?.toInt(),
  json['tenantId'] as String?,
  json['mainFlg'] as String?,
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['roleName'] as String?,
  json['mfaFlg'] as String?,
);

Map<String, dynamic> _$UserRoleResponseDataToJson(
  UserRoleResponseData instance,
) => <String, dynamic>{
  'userName': instance.userName,
  'userId': instance.userId,
  'roleId': instance.roleId,
  'tenantId': instance.tenantId,
  'mainFlg': instance.mainFlg,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'roleName': instance.roleName,
  'mfaFlg': instance.mfaFlg,
};
