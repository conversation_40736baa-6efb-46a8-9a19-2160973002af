// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedUserModel _$SharedUserModelFromJson(Map<String, dynamic> json) =>
    SharedUserModel(
      userId: (json['userId'] as num?)?.toInt(),
      tenantId: json['tenantId'] as String?,
      userName: json['userName'] as String?,
      lastName: json['lastName'] as String?,
      firstName: json['firstName'] as String?,
      lastNameKana: json['lastNameKana'] as String?,
      firstNameKana: json['firstNameKana'] as String?,
      nationCode: json['nationCode'] as String?,
      tel: json['tel'] as String?,
      recoverableLimit: json['recoverableLimit'] as String?,
      delFlg: json['delFlg'] as String?,
      firstLoginTime: json['firstLoginTime'] as String?,
      lastLoginTime: json['lastLoginTime'] as String?,
      disableFlg: json['disableFlg'] as String?,
      urlPassParameters: json['urlPassParameters'] as String?,
      urlPassRecoverableLimit: json['urlPassRecoverableLimit'] as String?,
      userStatus: json['userStatus'] as String?,
      biUserId: (json['biUserId'] as num?)?.toInt(),
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      manageFlg: json['manageFlg'] as String?,
      scanType: json['scanType'] as String?,
      userText: json['userText'] as String?,
      location: json['location'] as String?,
      state: json['state'] as String?,
      userGroupIds: json['userGroupIds'] as String?,
      userGroupNames: json['userGroupNames'] as String?,
      mainGroupId: json['mainGroupId'] as String?,
      managerKbn: json['managerKbn'] as String?,
      lockedFlg: json['lockedFlg'] as String?,
      pwRecoverableLimit: json['pwRecoverableLimit'] as String?,
      userRoleStr: json['userRoleStr'] as String?,
      expireTime: (json['expireTime'] as num?)?.toInt(),
      enableSso: json['enableSso'] as bool? ?? false,
      nameId: json['nameId'] as String?,
    );

Map<String, dynamic> _$SharedUserModelToJson(SharedUserModel instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'tenantId': instance.tenantId,
      'userName': instance.userName,
      'lastName': instance.lastName,
      'firstName': instance.firstName,
      'lastNameKana': instance.lastNameKana,
      'firstNameKana': instance.firstNameKana,
      'nationCode': instance.nationCode,
      'tel': instance.tel,
      'recoverableLimit': instance.recoverableLimit,
      'delFlg': instance.delFlg,
      'firstLoginTime': instance.firstLoginTime,
      'lastLoginTime': instance.lastLoginTime,
      'disableFlg': instance.disableFlg,
      'urlPassParameters': instance.urlPassParameters,
      'urlPassRecoverableLimit': instance.urlPassRecoverableLimit,
      'userStatus': instance.userStatus,
      'biUserId': instance.biUserId,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'manageFlg': instance.manageFlg,
      'scanType': instance.scanType,
      'userText': instance.userText,
      'location': instance.location,
      'state': instance.state,
      'userGroupIds': instance.userGroupIds,
      'userGroupNames': instance.userGroupNames,
      'mainGroupId': instance.mainGroupId,
      'managerKbn': instance.managerKbn,
      'lockedFlg': instance.lockedFlg,
      'pwRecoverableLimit': instance.pwRecoverableLimit,
      'userRoleStr': instance.userRoleStr,
      'expireTime': instance.expireTime,
      'enableSso': instance.enableSso,
      'nameId': instance.nameId,
    };
