// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_role_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedRoleModel _$SharedRoleModelFromJson(Map<String, dynamic> json) =>
    SharedRoleModel(
      roleId: (json['roleId'] as num?)?.toInt(),
      tenantId: json['tenantId'] as String?,
      roleName: json['roleName'] as String,
      systemFlg: json['systemFlg'] as String?,
      groupText: json['groupText'] as String?,
      createdById: json['createdById'] as String?,
      createdByName: json['createdByName'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedByName: json['modifiedByName'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      displayModifiedDate: json['displayModifiedDate'] as String?,
    );

Map<String, dynamic> _$SharedRoleModelToJson(SharedRoleModel instance) =>
    <String, dynamic>{
      'roleId': instance.roleId,
      'tenantId': instance.tenantId,
      'roleName': instance.roleName,
      'systemFlg': instance.systemFlg,
      'groupText': instance.groupText,
      'createdById': instance.createdById,
      'createdByName': instance.createdByName,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedByName': instance.modifiedByName,
      'modifiedDate': instance.modifiedDate,
      'displayModifiedDate': instance.displayModifiedDate,
    };
