// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PermissionListResponse _$PermissionListResponseFromJson(
  Map<String, dynamic> json,
) => PermissionListResponse(
  (json['permissionList'] as List<dynamic>?)
      ?.map(
        (e) =>
            e == null
                ? null
                : PermissionListModel.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
);

Map<String, dynamic> _$PermissionListResponseToJson(
  PermissionListResponse instance,
) => <String, dynamic>{'permissionList': instance.permissionList};

PermissionListModel _$PermissionListModelFromJson(Map<String, dynamic> json) =>
    PermissionListModel(
      (json['roleId'] as num?)?.toInt(),
      (json['tenantId'] as num?)?.toInt(),
      json['type'] as String?,
      json['functionId'] as String?,
      json['pageComponent'] as String?,
      json['pageId'] as String?,
      (json['resourceId'] as num?)?.toInt(),
      json['resource'] as String?,
      (json['createdById'] as num?)?.toInt(),
      json['createdDate'] as String?,
      (json['modifiedById'] as num?)?.toInt(),
      json['modifiedDate'] as String?,
    );

Map<String, dynamic> _$PermissionListModelToJson(
  PermissionListModel instance,
) => <String, dynamic>{
  'roleId': instance.roleId,
  'tenantId': instance.tenantId,
  'type': instance.type,
  'functionId': instance.functionId,
  'pageComponent': instance.pageComponent,
  'pageId': instance.pageId,
  'resourceId': instance.resourceId,
  'resource': instance.resource,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
};
