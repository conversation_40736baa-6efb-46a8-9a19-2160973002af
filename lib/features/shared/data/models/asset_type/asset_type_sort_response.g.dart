// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_type_sort_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetTypeResponse _$AssetTypeResponseFromJson(
  Map<String, dynamic> json,
) => AssetTypeResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
  commonJS: json['commonJS'] as String?,
  assetTypeList:
      (json['assetTypeList'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : AssetTypeListModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  js: json['js'] as String?,
  jsQRlogic: json['jsQRlogic'] as String?,
  jsSaveAsset: json['jsSaveAsset'] as String?,
  rememberedAssetType: json['rememberedAssetType'] as String?,
);

Map<String, dynamic> _$AssetTypeResponseToJson(AssetTypeResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'commonJS': instance.commonJS,
      'assetTypeList': instance.assetTypeList,
      'js': instance.js,
      'jsQRlogic': instance.jsQRlogic,
      'jsSaveAsset': instance.jsSaveAsset,
      'rememberedAssetType': instance.rememberedAssetType,
    };

AssetTypeListModel _$AssetTypeListModelFromJson(Map<String, dynamic> json) =>
    AssetTypeListModel(
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      tenantId: json['tenantId'] as String?,
      assetTypeName: json['assetTypeName'] as String?,
      itemFlg: json['itemFlg'] as String?,
      layoutFlg: json['layoutFlg'] as String?,
      layoutTempSaveFlg: json['layoutTempSaveFlg'] as String?,
      quantityFlg: json['quantityFlg'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedByName: json['modifiedByName'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      displayModifiedDate: json['displayModifiedDate'] as String?,
      groupIds: json['groupIds'] as String?,
      alertCount: json['alertCount'] as String?,
      displayFlg: json['displayFlg'] as String?,
      groupPermissionCheckLog: json['groupPermissionCheckLog'] as String?,
    );

Map<String, dynamic> _$AssetTypeListModelToJson(AssetTypeListModel instance) =>
    <String, dynamic>{
      'assetTypeId': instance.assetTypeId,
      'tenantId': instance.tenantId,
      'assetTypeName': instance.assetTypeName,
      'itemFlg': instance.itemFlg,
      'layoutFlg': instance.layoutFlg,
      'layoutTempSaveFlg': instance.layoutTempSaveFlg,
      'quantityFlg': instance.quantityFlg,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedByName': instance.modifiedByName,
      'modifiedDate': instance.modifiedDate,
      'displayModifiedDate': instance.displayModifiedDate,
      'groupIds': instance.groupIds,
      'alertCount': instance.alertCount,
      'displayFlg': instance.displayFlg,
      'groupPermissionCheckLog': instance.groupPermissionCheckLog,
    };
