// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_condition.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchConditionModel _$SearchConditionModelFromJson(
  Map<String, dynamic> json,
) => SearchConditionModel(
  searchId: (json['searchId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  searchName: json['searchName'] as String?,
  screenId: json['screenId'] as String?,
  searchModel: json['searchModel'] as String?,
  logicValue: json['logicValue'] as String?,
  authorityType: json['authorityType'] as String?,
  groupIds: json['groupIds'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  groupPermissionCheckLog: json['groupPermissionCheckLog'] as String?,
);

Map<String, dynamic> _$SearchConditionModelToJson(
  SearchConditionModel instance,
) => <String, dynamic>{
  'searchId': instance.searchId,
  'tenantId': instance.tenantId,
  'assetTypeId': instance.assetTypeId,
  'searchName': instance.searchName,
  'screenId': instance.screenId,
  'searchModel': instance.searchModel,
  'logicValue': instance.logicValue,
  'authorityType': instance.authorityType,
  'groupIds': instance.groupIds,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'groupPermissionCheckLog': instance.groupPermissionCheckLog,
};
