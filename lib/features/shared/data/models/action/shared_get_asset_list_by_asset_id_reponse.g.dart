// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_get_asset_list_by_asset_id_reponse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedGetAssetListByAssetIdResponse
_$SharedGetAssetListByAssetIdResponseFromJson(Map<String, dynamic> json) =>
    SharedGetAssetListByAssetIdResponse(
      msg: json['msg'] as String,
      code: (json['code'] as num).toInt(),
      assets:
          (json['assets'] as List<dynamic>?)
              ?.map(
                (e) => SharedActionAsset.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    );

Map<String, dynamic> _$SharedGetAssetListByAssetIdResponseToJson(
  SharedGetAssetListByAssetIdResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'assets': instance.assets?.map((e) => e.toJson()).toList(),
};

SharedActionAsset _$SharedActionAssetFromJson(Map<String, dynamic> json) =>
    SharedActionAsset(
      assetId: (json['assetId'] as num?)?.toInt(),
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      assetTypeName: json['assetTypeName'] as String?,
      assetItemList:
          (json['assetItemList'] as List<dynamic>?)
              ?.map((e) => SharedAssetItem.fromJson(e as Map<String, dynamic>))
              .toList(),
      assetSettingDetailList:
          (json['assetSettingDetailList'] as List<dynamic>?)
              ?.map((e) => SharedAssetItem.fromJson(e as Map<String, dynamic>))
              .toList(),
      assetName: json['assetName'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      homeImageUrl: json['homeImageUrl'] as String?,
      location: json['location'] as String?,
      identityCode: json['identityCode'] as String?,
    );

Map<String, dynamic> _$SharedActionAssetToJson(SharedActionAsset instance) =>
    <String, dynamic>{
      'assetId': instance.assetId,
      'assetTypeId': instance.assetTypeId,
      'assetTypeName': instance.assetTypeName,
      'assetItemList': instance.assetItemList?.map((e) => e.toJson()).toList(),
      'assetSettingDetailList':
          instance.assetSettingDetailList?.map((e) => e.toJson()).toList(),
      'assetName': instance.assetName,
      'quantity': instance.quantity,
      'homeImageUrl': instance.homeImageUrl,
      'location': instance.location,
      'identityCode': instance.identityCode,
    };
