// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_asset_action_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedAssetActionResponse _$SharedAssetActionResponseFromJson(
  Map<String, dynamic> json,
) => SharedAssetActionResponse(
  msg: json['msg'] as String,
  code: (json['code'] as num).toInt(),
  moreThenLimit: json['moreThenLimit'] as bool?,
  assetActionList:
      (json['assetActionList'] as List<dynamic>?)
          ?.map((e) => SharedAssetAction.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$SharedAssetActionResponseToJson(
  SharedAssetActionResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'moreThenLimit': instance.moreThenLimit,
  'assetActionList': instance.assetActionList,
};

SharedAssetAction _$SharedAssetActionFromJson(Map<String, dynamic> json) =>
    SharedAssetAction(
      assetActionId: (json['assetActionId'] as num?)?.toInt(),
      assetActionName: json['assetActionName'] as String?,
      tenantId: json['tenantId'] as String?,
      assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
      assetTypeGroupIds: json['assetTypeGroupIds'] as String?,
      displayFlg: json['displayFlg'] as String?,
      assetTypeName: json['assetTypeName'] as String?,
      quantityFlg: json['quantityFlg'] as String?,
      assetActionItem: json['assetActionItem'] as String?,
      scanCondition: json['scanCondition'] as String?,
      appurtenancesInformationTypeInfo:
          json['appurtenancesInformationTypeInfo'] as String?,
      userIds: json['userIds'] as String?,
      groupIds: json['groupIds'] as String?,
      isUpdateAmount: json['isUpdateAmount'] as String?,
      amountType: json['amountType'] as String?,
      autoFetchAsset: json['autoFetchAsset'] as String?,
      assetConditionSearchName: json['assetConditionSearchName'] as String?,
      autoFetchSearchId: (json['autoFetchSearchId'] as num?)?.toInt(),
      onlineMode: json['onlineMode'] as String?,
      onlyMobileExecutable: json['onlyMobileExecutable'] as String?,
      createdById: json['createdById'] as String?,
      createdByName: json['createdByName'] as String?,
      createdDate: json['createdDate'] as String?,
      modifiedById: json['modifiedById'] as String?,
      modifiedByName: json['modifiedByName'] as String?,
      modifiedDate: json['modifiedDate'] as String?,
      actionLabel: json['actionLabel'] as String?,
      actionIcon: json['actionIcon'] as String?,
      processId: (json['processId'] as num?)?.toInt(),
      customerActionName: json['customerActionName'] as String?,
      assetList: json['assetList'] as String?,
      processType: json['processType'] as String?,
      retroactiveDate: json['retroactiveDate'] as String?,
      processInstanceId: json['processInstanceId'] as String?,
      isCreatedUser: json['isCreatedUser'] as bool?,
      nowAssetListJson: json['nowAssetListJson'] as String?,
      tmpModifiedByName: json['tmpModifiedByName'] as String?,
      tmpModifiedDate: json['tmpModifiedDate'] as String?,
      actionBindWorkflowFlag: json['actionBindWorkflowFlag'] as bool?,
      appurtenancesInformationIds:
          json['appurtenancesInformationIds'] as String?,
      isLocked: json['isLocked'] as String?,
      lockedById: json['lockedById'] as String?,
      lockedByName: json['lockedByName'] as String?,
      appurtenancesInformationTypeIds:
          json['appurtenancesInformationTypeIds'] as String?,
      assetDisplayList:
          (json['assetDisplayList'] as List<dynamic>?)
              ?.map((e) => AssetDisplay.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$SharedAssetActionToJson(
  SharedAssetAction instance,
) => <String, dynamic>{
  'assetActionId': instance.assetActionId,
  'assetActionName': instance.assetActionName,
  'tenantId': instance.tenantId,
  'assetTypeId': instance.assetTypeId,
  'assetTypeGroupIds': instance.assetTypeGroupIds,
  'displayFlg': instance.displayFlg,
  'assetTypeName': instance.assetTypeName,
  'quantityFlg': instance.quantityFlg,
  'assetActionItem': instance.assetActionItem,
  'scanCondition': instance.scanCondition,
  'appurtenancesInformationTypeInfo': instance.appurtenancesInformationTypeInfo,
  'userIds': instance.userIds,
  'groupIds': instance.groupIds,
  'isUpdateAmount': instance.isUpdateAmount,
  'amountType': instance.amountType,
  'autoFetchAsset': instance.autoFetchAsset,
  'assetConditionSearchName': instance.assetConditionSearchName,
  'autoFetchSearchId': instance.autoFetchSearchId,
  'onlineMode': instance.onlineMode,
  'onlyMobileExecutable': instance.onlyMobileExecutable,
  'createdById': instance.createdById,
  'createdByName': instance.createdByName,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedByName': instance.modifiedByName,
  'modifiedDate': instance.modifiedDate,
  'actionLabel': instance.actionLabel,
  'actionIcon': instance.actionIcon,
  'processId': instance.processId,
  'customerActionName': instance.customerActionName,
  'assetList': instance.assetList,
  'processType': instance.processType,
  'retroactiveDate': instance.retroactiveDate,
  'processInstanceId': instance.processInstanceId,
  'isCreatedUser': instance.isCreatedUser,
  'nowAssetListJson': instance.nowAssetListJson,
  'tmpModifiedByName': instance.tmpModifiedByName,
  'tmpModifiedDate': instance.tmpModifiedDate,
  'actionBindWorkflowFlag': instance.actionBindWorkflowFlag,
  'appurtenancesInformationIds': instance.appurtenancesInformationIds,
  'isLocked': instance.isLocked,
  'lockedById': instance.lockedById,
  'lockedByName': instance.lockedByName,
  'appurtenancesInformationTypeIds': instance.appurtenancesInformationTypeIds,
  'assetDisplayList':
      instance.assetDisplayList?.map((e) => e.toJson()).toList(),
};

AssetDisplay _$AssetDisplayFromJson(Map<String, dynamic> json) => AssetDisplay(
  assetId: (json['assetId'] as num?)?.toInt(),
  externalCode: json['externalCode'] as String?,
  tenantId: json['tenantId'] as String?,
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  assetTypeName: json['assetTypeName'] as String?,
  groupIds: json['groupIds'] as String?,
  assetText: json['assetText'] as String?,
  layoutNo: json['layoutNo'] as String?,
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  state: json['state'] as String?,
  assetName: json['assetName'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  barcode: json['barcode'] as String?,
  assetLocation: json['assetLocation'] as String?,
  relationFlg: json['relationFlg'] as bool?,
  relationAssetDataList: json['relationAssetDataList'] as List<dynamic>?,
  relationAssetIdList: json['relationAssetIdList'] as String?,
  dependentRelationAssetIdList: json['dependentRelationAssetIdList'] as String?,
  message: json['message'] as String?,
  jurisdiction: json['jurisdiction'] as String?,
  insertAssetRelationColumnData:
      json['insertAssetRelationColumnData'] as String?,
  relationNotUpdateFlag: json['relationNotUpdateFlag'] as bool?,
  rfid: json['rfid'] as String?,
  copyAppurtenancesInformationTypeIds:
      json['copyAppurtenancesInformationTypeIds'] as String?,
  assetReservationStatusList: json['assetReservationStatusList'] as String?,
  assetItemList:
      (json['assetItemList'] as List<dynamic>?)
          ?.map((e) => SharedAssetItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  willChangedAmount: (json['willChangedAmount'] as num?)?.toInt(),
  interactionOperation: json['interactionOperation'] as String?,
);

Map<String, dynamic> _$AssetDisplayToJson(AssetDisplay instance) =>
    <String, dynamic>{
      'assetId': instance.assetId,
      'externalCode': instance.externalCode,
      'tenantId': instance.tenantId,
      'assetTypeId': instance.assetTypeId,
      'assetTypeName': instance.assetTypeName,
      'groupIds': instance.groupIds,
      'assetText': instance.assetText,
      'layoutNo': instance.layoutNo,
      'createdById': instance.createdById,
      'createdDate': instance.createdDate,
      'modifiedById': instance.modifiedById,
      'modifiedDate': instance.modifiedDate,
      'state': instance.state,
      'assetName': instance.assetName,
      'workflowId': instance.workflowId,
      'barcode': instance.barcode,
      'assetLocation': instance.assetLocation,
      'relationFlg': instance.relationFlg,
      'relationAssetDataList': instance.relationAssetDataList,
      'relationAssetIdList': instance.relationAssetIdList,
      'dependentRelationAssetIdList': instance.dependentRelationAssetIdList,
      'message': instance.message,
      'jurisdiction': instance.jurisdiction,
      'insertAssetRelationColumnData': instance.insertAssetRelationColumnData,
      'relationNotUpdateFlag': instance.relationNotUpdateFlag,
      'rfid': instance.rfid,
      'copyAppurtenancesInformationTypeIds':
          instance.copyAppurtenancesInformationTypeIds,
      'assetReservationStatusList': instance.assetReservationStatusList,
      'assetItemList': instance.assetItemList?.map((e) => e.toJson()).toList(),
      'willChangedAmount': instance.willChangedAmount,
      'interactionOperation': instance.interactionOperation,
    };

SharedInsertActionResponse _$SharedInsertActionResponseFromJson(
  Map<String, dynamic> json,
) => SharedInsertActionResponse(
  (json['processId'] as num?)?.toInt(),
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String,
);

Map<String, dynamic> _$SharedInsertActionResponseToJson(
  SharedInsertActionResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'processId': instance.processId,
};
