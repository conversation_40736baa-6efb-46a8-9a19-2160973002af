// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_get_selected_asset_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SharedGetSelectedAssetListResponse _$SharedGetSelectedAssetListResponseFromJson(
  Map<String, dynamic> json,
) => SharedGetSelectedAssetListResponse(
  msg: json['msg'] as String,
  code: (json['code'] as num).toInt(),
  assets:
      (json['assets'] as List<dynamic>?)
          ?.map(
            (e) => SharedGetSelectedAsset.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  errorCode: json['errorCode'] as String?,
  verifyResult: json['verifyResult'] as bool?,
  asset:
      json['asset'] == null
          ? null
          : SharedGetSelectedAsset.fromJson(
            json['asset'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$SharedGetSelectedAssetListResponseToJson(
  SharedGetSelectedAssetListResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'assets': instance.assets?.map((e) => e.toJson()).toList(),
  if (instance.errorCode case final value?) 'errorCode': value,
  if (instance.verifyResult case final value?) 'verifyResult': value,
  if (instance.asset?.toJson() case final value?) 'asset': value,
};

SharedGetSelectedAsset _$SharedGetSelectedAssetFromJson(
  Map<String, dynamic> json,
) => SharedGetSelectedAsset(
  scanItems:
      (json['scanItems'] as List<dynamic>?)
          ?.map((e) => SharedItemType.fromJson(e as Map<String, dynamic>))
          .toList(),
  assetTypeId: (json['assetTypeId'] as num?)?.toInt(),
  arColor: json['arColor'] as String?,
  assetId: (json['assetId'] as num?)?.toInt(),
  barCode: json['barCode'] as String?,
  location: json['location'] as String?,
  homeImageUrl: json['homeImageUrl'] as String?,
  assetName: json['assetName'] as String?,
  count: (json['count'] as num?)?.toInt(),
  quantity: (json['quantity'] as num?)?.toInt(),
  homeImageMobileDisplayFlg: json['homeImageMobileDisplayFlg'] as bool?,
);

Map<String, dynamic> _$SharedGetSelectedAssetToJson(
  SharedGetSelectedAsset instance,
) => <String, dynamic>{
  'scanItems': instance.scanItems?.map((e) => e.toJson()).toList(),
  'assetTypeId': instance.assetTypeId,
  'arColor': instance.arColor,
  'assetId': instance.assetId,
  'barCode': instance.barCode,
  'location': instance.location,
  'homeImageUrl': instance.homeImageUrl,
  'assetName': instance.assetName,
  'count': instance.count,
  'quantity': instance.quantity,
  'homeImageMobileDisplayFlg': instance.homeImageMobileDisplayFlg,
};
