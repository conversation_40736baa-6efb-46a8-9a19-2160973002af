// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'process_definitions_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProcessDefinitionsModel _$ProcessDefinitionsModelFromJson(
  Map<String, dynamic> json,
) => ProcessDefinitionsModel(
  assetActionData: json['assetActionData'],
  assetActionList: json['assetActionList'],
  assetTypeIdWithFirstWf: (json['assetTypeIdWithFirstWf'] as num).toInt(),
  assetTypeName: json['assetTypeName'] as String,
  comment: json['comment'] as String,
  description: json['description'] as String,
  processDefinitionId: json['processDefinitionId'] as String,
  processInstanceId: json['processInstanceId'] as String,
  sequenceFlows: json['sequenceFlows'],
  taskDefinitions: json['taskDefinitions'],
  version: (json['version'] as num).toInt(),
  workflowId: (json['workflowId'] as num).toInt(),
  workflowName: json['workflowName'] as String,
  name: json['name'] as String,
  workflowType: json['workflowType'] as String,
  workflowTypeCode: json['workflowTypeCode'] as String,
  id: json['id'] as String,
  state: json['state'] as String,
  firstWorkflowAssetTypeName: json['firstWorkflowAssetTypeName'] as String,
  createdDate: json['createdDate'] as String,
  wfName: json['wfName'] as String,
);

Map<String, dynamic> _$ProcessDefinitionsModelToJson(
  ProcessDefinitionsModel instance,
) => <String, dynamic>{
  'assetActionData': instance.assetActionData,
  'assetActionList': instance.assetActionList,
  'assetTypeIdWithFirstWf': instance.assetTypeIdWithFirstWf,
  'assetTypeName': instance.assetTypeName,
  'comment': instance.comment,
  'description': instance.description,
  'processDefinitionId': instance.processDefinitionId,
  'processInstanceId': instance.processInstanceId,
  'sequenceFlows': instance.sequenceFlows,
  'taskDefinitions': instance.taskDefinitions,
  'version': instance.version,
  'workflowId': instance.workflowId,
  'workflowName': instance.workflowName,
  'name': instance.name,
  'workflowType': instance.workflowType,
  'workflowTypeCode': instance.workflowTypeCode,
  'id': instance.id,
  'state': instance.state,
  'firstWorkflowAssetTypeName': instance.firstWorkflowAssetTypeName,
  'createdDate': instance.createdDate,
  'wfName': instance.wfName,
};
