// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeListModel _$HomeListModelFromJson(Map<String, dynamic> json) =>
    HomeListModel(
      assetActionList:
          (json['assetActionList'] as List<dynamic>)
              .map((e) => SharedAssetAction.fromJson(e as Map<String, dynamic>))
              .toList(),
      workflowList:
          (json['workflowList'] as List<dynamic>)
              .map((e) => WorkflowList.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$HomeListModelToJson(HomeListModel instance) =>
    <String, dynamic>{
      'assetActionList': instance.assetActionList,
      'workflowList': instance.workflowList,
    };

WorkflowList _$WorkflowListFromJson(Map<String, dynamic> json) => WorkflowList(
  processDefinitionId: json['processDefinitionId'] as String?,
  workflowId: (json['workflowId'] as num?)?.toInt(),
  workflowName: json['workflowName'] as String?,
  version: (json['version'] as num?)?.toInt(),
  description: json['description'] as String?,
  comment: json['comment'] as String?,
  assetActionList: json['assetActionList'],
  taskDefinitions: json['taskDefinitions'],
  sequenceFlows: json['sequenceFlows'],
  assetTypeIdWithFirstWf: (json['assetTypeIdWithFirstWf'] as num?)?.toInt(),
  createdDate: json['createdDate'] as String,
  assetTypeName: json['assetTypeName'] as String?,
  workflowType: json['workflowType'] as String?,
  workflowTypeCode: json['workflowTypeCode'] as String?,
  autoFetchAsset: json['autoFetchAsset'] as String?,
  autoSetItemDefaultValue: json['autoSetItemDefaultValue'] as String?,
  timerStartUserId: json['timerStartUserId'] as String?,
);

Map<String, dynamic> _$WorkflowListToJson(WorkflowList instance) =>
    <String, dynamic>{
      'processDefinitionId': instance.processDefinitionId,
      'workflowId': instance.workflowId,
      'workflowName': instance.workflowName,
      'version': instance.version,
      'description': instance.description,
      'comment': instance.comment,
      'assetActionList': instance.assetActionList,
      'taskDefinitions': instance.taskDefinitions,
      'sequenceFlows': instance.sequenceFlows,
      'assetTypeIdWithFirstWf': instance.assetTypeIdWithFirstWf,
      'createdDate': instance.createdDate,
      'assetTypeName': instance.assetTypeName,
      'workflowType': instance.workflowType,
      'workflowTypeCode': instance.workflowTypeCode,
      'autoFetchAsset': instance.autoFetchAsset,
      'autoSetItemDefaultValue': instance.autoSetItemDefaultValue,
      'timerStartUserId': instance.timerStartUserId,
    };
