// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeModel _$HomeModelFromJson(Map<String, dynamic> json) => HomeModel(
  talkBadgeNum: (json['talkBadgeNum'] as num?)?.toInt(),
  saveTemporaryActionCount: (json['saveTemporaryActionCount'] as num?)?.toInt(),
  saveTemporaryProcessCount:
      (json['saveTemporaryProcessCount'] as num?)?.toInt(),
  unApproveProcessCount: (json['unApproveProcessCount'] as num?)?.toInt(),
  saveTemporaryActionDisplayCount:
      json['saveTemporaryActionDisplayCount'] as String?,
  saveTemporaryProcessDisplayCount:
      json['saveTemporaryProcessDisplayCount'] as String?,
  unApproveProcessDisplayCount: json['unApproveProcessDisplayCount'] as String?,
  saveTemporaryActionLatestDate:
      json['saveTemporaryActionLatestDate'] as String?,
  saveTemporaryProcessLatestDate:
      json['saveTemporaryProcessLatestDate'] as String?,
  unApproveProcessLatestDate: json['unApproveProcessLatestDate'] as String?,
  assetActionList:
      (json['assetActionList'] as List<dynamic>?)
          ?.map((e) => AssetActionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  workflowList:
      (json['workflowList'] as List<dynamic>?)
          ?.map(
            (e) => ProcessDefinitionsModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
);

Map<String, dynamic> _$HomeModelToJson(HomeModel instance) => <String, dynamic>{
  'talkBadgeNum': instance.talkBadgeNum,
  'saveTemporaryActionCount': instance.saveTemporaryActionCount,
  'saveTemporaryProcessCount': instance.saveTemporaryProcessCount,
  'unApproveProcessCount': instance.unApproveProcessCount,
  'saveTemporaryActionDisplayCount': instance.saveTemporaryActionDisplayCount,
  'saveTemporaryProcessDisplayCount': instance.saveTemporaryProcessDisplayCount,
  'unApproveProcessDisplayCount': instance.unApproveProcessDisplayCount,
  'saveTemporaryActionLatestDate': instance.saveTemporaryActionLatestDate,
  'saveTemporaryProcessLatestDate': instance.saveTemporaryProcessLatestDate,
  'unApproveProcessLatestDate': instance.unApproveProcessLatestDate,
  'assetActionList': instance.assetActionList,
  'workflowList': instance.workflowList,
};
