// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_action_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetActionModel _$AssetActionModelFromJson(Map<String, dynamic> json) =>
    AssetActionModel(
      assetTypeId: (json['assetTypeId'] as num).toInt(),
      assetId: (json['assetId'] as num).toInt(),
      assetName: json['assetName'] as String,
      barCode: json['barCode'] as String,
      createdById: json['createdById'] as String,
      createdDate: json['createdDate'] as String,
      modifiedById: (json['modifiedById'] as num).toInt(),
      modifiedDate: json['modifiedDate'] as String,
      tmpModifiedDate: json['tmpModifiedDate'] as String,
      assetTotal: (json['assetTotal'] as num).toInt(),
      tenantId: json['tenantId'] as String,
      isUpdateAmount: (json['isUpdateAmount'] as num).toInt(),
      amountType: (json['amountType'] as num).toInt(),
      assetTypeName: json['assetTypeName'] as String,
      scanCondition: json['scanCondition'],
      assetActionName: json['assetActionName'] as String,
      assetActionItem: json['assetActionItem'] as String,
      assetActionId: json['assetActionId'] as String,
      processId: json['processId'] as String,
      assetList: json['assetList'] as String,
      customerActionName: json['customerActionName'] as String,
      isCreatedUser: json['isCreatedUser'] as bool,
      createdByName: json['createdByName'] as String,
      appurtenancesInformationTypeInfo:
          json['appurtenancesInformationTypeInfo'] as String,
      appurtenancesInformationTypeId:
          (json['appurtenancesInformationTypeId'] as num).toInt(),
      appurtenancesInformationTypeName:
          json['appurtenancesInformationTypeName'] as String,
      actionLabel: json['actionLabel'] as String,
      actionIcon: json['actionIcon'] as String,
      rightItemOptionStyle: (json['rightItemOptionStyle']
              as Map<String, dynamic>?)
          ?.map((k, e) => MapEntry(k, e as String)),
      rightItemOptionBackStyle: (json['rightItemOptionBackStyle']
              as Map<String, dynamic>?)
          ?.map((k, e) => MapEntry(k, e as String)),
    );

Map<String, dynamic> _$AssetActionModelToJson(
  AssetActionModel instance,
) => <String, dynamic>{
  'assetTypeId': instance.assetTypeId,
  'assetId': instance.assetId,
  'assetName': instance.assetName,
  'barCode': instance.barCode,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'tmpModifiedDate': instance.tmpModifiedDate,
  'assetTotal': instance.assetTotal,
  'tenantId': instance.tenantId,
  'isUpdateAmount': instance.isUpdateAmount,
  'amountType': instance.amountType,
  'assetTypeName': instance.assetTypeName,
  'scanCondition': instance.scanCondition,
  'assetActionName': instance.assetActionName,
  'assetActionItem': instance.assetActionItem,
  'assetActionId': instance.assetActionId,
  'processId': instance.processId,
  'assetList': instance.assetList,
  'customerActionName': instance.customerActionName,
  'isCreatedUser': instance.isCreatedUser,
  'createdByName': instance.createdByName,
  'appurtenancesInformationTypeInfo': instance.appurtenancesInformationTypeInfo,
  'appurtenancesInformationTypeId': instance.appurtenancesInformationTypeId,
  'appurtenancesInformationTypeName': instance.appurtenancesInformationTypeName,
  'actionLabel': instance.actionLabel,
  'actionIcon': instance.actionIcon,
  'rightItemOptionStyle': instance.rightItemOptionStyle,
  'rightItemOptionBackStyle': instance.rightItemOptionBackStyle,
};
