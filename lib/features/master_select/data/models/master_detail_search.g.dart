// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'master_detail_search.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MasterDetailSearch _$MasterDetailSearchFromJson(Map<String, dynamic> json) =>
    MasterDetailSearch(
      json['msg'] as String?,
      (json['code'] as num?)?.toInt(),
      (json['masterDetail'] as List<dynamic>?)
          ?.map(
            (e) =>
                e == null
                    ? null
                    : MasterDetailSearchMasterDetail.fromJson(
                      e as Map<String, dynamic>,
                    ),
          )
          .toList(),
      json['moreThanLimit'] as bool?,
    );

Map<String, dynamic> _$MasterDetailSearchToJson(MasterDetailSearch instance) =>
    <String, dynamic>{
      'msg': instance.msg,
      'code': instance.code,
      'masterDetail': instance.masterDetail,
      'moreThanLimit': instance.moreThanLimit,
    };

MasterDetailSearchMasterDetail _$MasterDetailSearchMasterDetailFromJson(
  Map<String, dynamic> json,
) => MasterDetailSearchMasterDetail(
  (json['masterId'] as num?)?.toInt(),
  json['tenantId'] as String?,
  (json['masterTypeId'] as num?)?.toInt(),
  json['masterText'] as String?,
  (json['detailSort'] as num?)?.toInt(),
  json['createdById'] as String?,
  json['createdDate'] as String?,
  json['modifiedById'] as String?,
  json['modifiedDate'] as String?,
  json['state'],
  json['count'],
  json['hiddenColumn'],
  json['isHidden'],
  json['excelLineNum'],
);

Map<String, dynamic> _$MasterDetailSearchMasterDetailToJson(
  MasterDetailSearchMasterDetail instance,
) => <String, dynamic>{
  'masterId': instance.masterId,
  'tenantId': instance.tenantId,
  'masterTypeId': instance.masterTypeId,
  'masterText': instance.masterText,
  'detailSort': instance.detailSort,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'state': instance.state,
  'count': instance.count,
  'hiddenColumn': instance.hiddenColumn,
  'isHidden': instance.isHidden,
  'excelLineNum': instance.excelLineNum,
};
