import 'dart:convert';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/ocr/presentation/bindings/ocr_binding.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:asset_force_mobile_v2/features/ocr/presentation/controllers/car_number_ocr_controller.dart';
import 'package:asset_force_mobile_v2/features/ocr/presentation/states/car_number_ocr_state.dart';
import 'package:get_auto_router_annotation/annotations.dart';
import 'package:flutter/cupertino.dart';

/// 车牌OCR页面
///
/// 功能说明：
/// - 支持拍摄多张车牌照片进行OCR识别
/// - 可以左右滑动查看不同照片
/// - 支持删除当前显示的照片
/// - 完成后返回OCR识别结果
@GetRoutePage('ocr/car_number_editor', binding: OcrBinding)
class CarNumberOcrPage extends GetWidget<CarNumberOcrController> {
  const CarNumberOcrPage({super.key});

  // ==================== 布局常量定义 ====================

  /// 图片显示区域的水平内边距
  /// 控制图片容器距离屏幕左右边缘的距离
  static const double _imageHorizontalPadding = 95.0;

  /// 输入框比图片框扩展的尺寸（每边）
  /// 让输入框看起来比图片框稍大一些
  static const double _inputFrameExpandSize = 50.0;

  /// 删除按钮行的高度
  static const double _deleteButtonRowHeight = 50.0;

  /// 底部完成按钮的高度
  static const double _footerHeight = 70.0;

  /// 分页控制器的水平间距
  static const double _pagerControlHorizontalPadding = 40.0;

  /// 图片边框宽度
  static const double _imageBorderWidth = 3.0;

  @override
  Widget build(BuildContext context) {
    // ==================== 屏幕尺寸计算 ====================

    /// 获取屏幕媒体查询信息
    final mediaQuery = MediaQuery.of(context);

    /// 计算安全区域高度（屏幕高度 - 状态栏 - 底部安全区域）
    final safeAreaHeight = mediaQuery.size.height - mediaQuery.padding.top - mediaQuery.padding.bottom;

    /// 图片显示区域高度 = 安全区域高度的一半
    /// 这样可以确保在不同设备上都有合适的显示比例
    final imageDisplayHeight = safeAreaHeight / 2;

    return GestureDetector(
      onTap: () {
        // 点击空白区域隐藏键盘
        controller.hideKeyboard();
      },
      child: Scaffold(
        backgroundColor: AppTheme.whiteColor,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            // 删除按钮行
            _buildDeleteButtonRow(),

            // 主要内容区域 - 使用Expanded确保占用剩余空间
            Expanded(
              child: Column(
                children: [
                  // 图片显示区域 - 使用Flexible让它可以伸缩
                  Flexible(
                    flex: 1,
                    child: _buildImageArea(imageDisplayHeight),
                  ),

                  // 左右切换照片控制
                  _buildPagerControls(),

                  // 输入区域或提示文本
                  _buildInputOrHintArea(),
                ],
              ),
            ),

            // 拍照按钮
            _buildCameraButton(),
          ],
        ),
        // 底部完成区域
        bottomNavigationBar: SafeArea(child: _buildFooter()),
      ),
    );
  }

  /// 构建应用栏
  ///
  /// 功能说明：
  /// - 显示页面标题"ナンバープレート読み取り"
  /// - 提供返回按钮，点击时确认取消操作
  /// - 使用统一的主题颜色和样式
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.chevron_left, color: AppTheme.whiteColor, size: 30),
        onPressed: () => controller.onCancel(), // 确认取消操作
      ),
      title: const Text(
        'ナンバープレート読み取り',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.whiteColor,
        ),
      ),
    );
  }

  /// 构建删除按钮行
  ///
  /// 功能说明：
  /// - 只有在有图片时才显示删除按钮
  /// - 点击可以删除当前显示的图片
  /// - 使用固定高度保持布局稳定
  Widget _buildDeleteButtonRow() {
    return Obx(() {
      final hasPages = controller.state.hasPages;

      return Container(
        height: _deleteButtonRowHeight,
        child: hasPages
            ? Center(
                child: TextButton(
                  onPressed: controller.onClickDeletePage,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '表示中の画像を削除',
                        style: TextStyle(
                          color: AppTheme.darkBlueColor,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: AppTheme.darkBlueColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: AppTheme.whiteColor,
                          size: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : const SizedBox.shrink(), // 没有图片时显示空的占位符
      );
    });
  }

  /// 构建图片显示区域
  ///
  /// [imageDisplayHeight] 图片显示区域的高度
  ///
  /// 功能说明：
  /// - 根据是否有图片动态改变边框颜色
  /// - 使用固定容器尺寸避免切换图片时的布局闪烁
  /// - 优化状态访问，避免输入时图片闪烁
  Widget _buildImageArea(double imageDisplayHeight) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: _imageHorizontalPadding),
      child: Obx(() {
        // 只获取影响图片显示的关键状态，避免因result变化而重新构建
        final pageCount = controller.state.pageCount;
        final currentPageIndex = controller.state.currentPageIndex.value;
        final hasPages = pageCount > 0;

        // 获取当前页面，但不在这里访问result属性
        final currentPage =
            pageCount > 0 && currentPageIndex < pageCount ? controller.state.pages[currentPageIndex] : null;

        return Container(
          width: double.infinity,
          height: imageDisplayHeight,
          decoration: BoxDecoration(
            border: Border.all(
              color: hasPages ? AppTheme.darkBlueColor : AppTheme.ocrImageArrowColor,
              width: _imageBorderWidth,
            ),
          ),
          child: _buildImageContentOptimized(hasPages, currentPage),
        );
      }),
    );
  }

  /// 构建优化的图片内容（避免输入时重新构建）
  ///
  /// [hasPages] 是否有图片页面
  /// [currentPage] 当前页面数据
  ///
  /// 功能说明：
  /// - 接收预先计算的状态参数，避免内部访问state
  /// - 减少因result字段变化导致的重新构建
  /// - 只有影响图片显示的状态变化时才重新构建
  Widget _buildImageContentOptimized(bool hasPages, OcrPageData? currentPage) {
    // 没有任何图片页面
    if (!hasPages) {
      return _buildImagePlaceholder(isError: false);
    }

    // 有页面但当前页面数据异常
    if (currentPage == null) {
      return _buildImagePlaceholder(isError: true);
    }

    // 正常显示图片（只访问markedImage，不访问result）
    return _buildImage(currentPage.markedImage);
  }

  /// 构建图片组件
  ///
  /// [imageData] Base64编码的图片数据
  ///
  /// 功能说明：
  /// - 解析Base64图片数据并显示
  /// - 使用cover模式充满整个容器
  /// - 自动处理解析错误，降级显示错误占位符
  Widget _buildImage(String imageData) {
    try {
      // 移除data URL前缀（如果有的话），例如: "data:image/jpeg;base64,"
      final base64String = imageData.replaceFirst(RegExp(r'data:image/[^;]+;base64,'), '');
      final bytes = base64Decode(base64String);

      return Image.memory(
        bytes,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover, // 图片充满容器，可能裁剪但不变形
        errorBuilder: (context, error, stackTrace) {
          return _buildImagePlaceholder(isError: true);
        },
      );
    } catch (e) {
      // Base64解码失败，显示错误占位符
      return _buildImagePlaceholder(isError: true);
    }
  }

  /// 构建图片占位符
  ///
  /// [isError] 是否为错误状态
  ///
  /// 功能说明：
  /// - 提供统一的占位符显示
  /// - 根据错误状态显示不同的提示文本
  /// - 保持与图片相同的容器尺寸
  Widget _buildImagePlaceholder({required bool isError}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppTheme.ocrNotImagePlaceholderBackgroundColor,
      child: Center(
        child: Text(
          isError ? '画像表示エラー' : '画像がありません',
          style: const TextStyle(
            color: AppTheme.placeholderColor,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  /// 构建分页控制器
  ///
  /// 功能说明：
  /// - 显示当前页码和总页数
  /// - 提供上一页/下一页导航功能
  /// - 当到达边界时禁用相应按钮并改变颜色
  Widget _buildPagerControls() {
    return Obx(() {
      // 获取分页相关状态
      final currentPageIndex = controller.state.currentPageIndex.value;
      final pageCount = controller.state.pageCount;
      final hasPages = controller.state.hasPages;

      // 计算按钮是否可用
      final canGoPrevious = currentPageIndex > 0;
      final canGoNext = currentPageIndex < pageCount - 1;

      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 上一页按钮
          IconButton(
            onPressed: canGoPrevious ? controller.onClickPrevPage : null,
            icon: Icon(
              CupertinoIcons.chevron_left,
              size: 20,
              color: canGoPrevious ? AppTheme.darkBlueColor : AppTheme.ocrImageArrowColor,
            ),
          ),

          // 页码显示
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: _pagerControlHorizontalPadding),
            child: Text(
              hasPages ? '${currentPageIndex + 1} / $pageCount' : '0 / 0',
              style: const TextStyle(fontSize: 16),
            ),
          ),

          // 下一页按钮
          IconButton(
            onPressed: canGoNext ? controller.onClickNextPage : null,
            icon: Icon(
              CupertinoIcons.chevron_right,
              size: 20,
              color: canGoNext ? AppTheme.darkBlueColor : AppTheme.ocrImageArrowColor,
            ),
          ),
        ],
      );
    });
  }

  /// 构建输入区域或提示文本
  Widget _buildInputOrHintArea() {
    return Obx(() {
      // 确保访问可观察变量
      final hasPages = controller.state.hasPages;
      return hasPages ? _buildInputArea() : _buildHintText();
    });
  }

  /// 构建输入区域
  ///
  /// 功能说明：
  /// - 创建一个带边框的输入框
  /// - 输入框比图片区域稍大，提供更好的视觉层次
  /// - 使用padding替代margin，符合用户习惯
  Widget _buildInputArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: _imageHorizontalPadding - _inputFrameExpandSize), // 比图片区域大，提供视觉层次
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.ocrNotImagePlaceholderBackgroundColor,
          border: Border.all(
            color: AppTheme.ocrImageArrowColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          focusNode: controller.carNumberFocusNode,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
          decoration: const InputDecoration(
            // 内容边距：左边40px，右边20px（为icon留空间）
            contentPadding: EdgeInsets.only(
              left: 40, // 距离容器左边的间距
              top: 10,
              right: 20, // 距离右侧icon的间距
              bottom: 10,
            ),
            // 右侧编辑图标
            suffixIcon: Padding(
              padding: EdgeInsets.only(right: 20), // icon距离右边的间距
              child: Icon(
                CupertinoIcons.pen,
                color: AppTheme.darkBlueColor,
                size: 30,
              ),
            ),
            suffixIconConstraints: BoxConstraints(
              minWidth: 0,
              minHeight: 0,
            ),
            border: InputBorder.none, // 移除默认边框，使用外层Container的边框
          ),
          controller: controller.controllerCarNumber,
        ),
      ),
    );
  }

  /// 构建提示文本
  Widget _buildHintText() {
    return const Center(
      child: Text(
        'ナンバープレートを\n撮影してください',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: AppTheme.darkBlueColor,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  /// 构建拍照按钮
  Widget _buildCameraButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: OutlinedButton(
        onPressed: controller.onClickAddNewPhoto,
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.darkBlueColor,
          side: const BorderSide(color: AppTheme.darkBlueColor, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.only(
            left: 10,
            top: 5,
            right: 10,
            bottom: 5,
          ),
          minimumSize: Size.zero,
        ),
        child: Obx(() {
          final hasPages = controller.state.hasPages;
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (hasPages) ...[
                const Icon(Icons.add, color: AppTheme.darkBlueColor),
                const SizedBox(width: 4),
                const Text('追加撮影'),
                const SizedBox(width: 8),
                const Icon(Icons.camera_alt, color: AppTheme.darkBlueColor),
              ] else ...[
                const Text('ナンバープレート撮影'),
                const SizedBox(width: 1),
                const Icon(Icons.camera_alt, color: AppTheme.darkBlueColor),
              ],
            ],
          );
        }),
      ),
    );
  }

  /// 构建底部完成区域
  ///
  /// 功能说明：
  /// - 提供完成OCR识别的确认按钮
  /// - 只有在有图片时才能点击
  /// - 按钮位于右侧，符合用户操作习惯
  Widget _buildFooter() {
    return Container(
      height: _footerHeight,
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppTheme.ocrImageArrowColor, width: 1),
        ),
      ),
      child: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 50),
          child: SizedBox(
            width: 55,
            height: 35,
            child: Obx(() {
              final hasPages = controller.state.hasPages;
              return ElevatedButton(
                onPressed: hasPages ? () => controller.onPassBackResult() : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: hasPages ? AppTheme.darkBlueColor : AppTheme.timePickerDividerColor,
                  disabledBackgroundColor: AppTheme.timePickerDividerColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.zero,
                ),
                child: const Text(
                  '完了',
                  style: TextStyle(fontSize: 16, color: AppTheme.whiteColor),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}
