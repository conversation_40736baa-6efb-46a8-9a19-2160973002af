import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:mockito/mockito.dart';

import '../../../mocks/js_executor_mocks.dart';
import '../../../mocks/js_executor_mocks.mocks.dart';

void main() {
  // 初始化Flutter测试绑定
  TestWidgetsFlutterBinding.ensureInitialized();

  // 初始化LogUtil
  setUpAll(() {
    LogUtil.initialize(
      printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50, colors: false, printEmojis: false),
    );
  });
  group('JsExecutor Tests', () {
    late JsExecutor jsExecutor;
    late MockInAppWebViewController mockWebController;
    late JsFlutterBridgeService mockBridgeService;

    setUp(() {
      // 创建mock对象
      mockWebController = createMockWebViewController();
      mockBridgeService = createMockJsFlutterBridgeService();

      // 创建JsExecutor实例，使用测试构造函数注入mock WebViewController
      jsExecutor = JsExecutor.withController(mockBridgeService, mockWebController);

      // 标记WebView已初始化，跳过实际的WebView初始化过程
      jsExecutor.isInitWebView = true;
    });

    tearDown(() {
      jsExecutor.dispose();
    });

    group('基础功能测试', () {
      test('构造函数应该正确初始化', () {
        expect(jsExecutor, isNotNull);
        expect(jsExecutor.isInitWebView, isTrue); // 在setUp中已设置为true
        expect(jsExecutor.hasController, isFalse);
      });

      test('dispose应该清理资源', () {
        jsExecutor.dispose();
        // 验证清理操作
        verify(mockBridgeService.clearController()).called(1);
      });
    });

    group('JavaScript执行测试', () {
      setUp(() async {
        // 模拟asset文件读取
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              final String key = methodCall.arguments as String;
              if (key.contains('calculateJavascript.js')) {
                return 'console.log("Calculate JS loaded");';
              } else if (key.contains('customizedLogicJavascript.js')) {
                return TestJavaScriptCode.dateHandleFunctions;
              } else if (key.contains('initJavascript.js')) {
                return 'console.log("Init JS loaded");';
              }
            }
            return null;
          },
        );
      });

      test('应该能执行简单的JavaScript代码', () async {
        // 设置mock返回值
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => 'test result');

        final result = await jsExecutor.eval('console.log("Hello World");', JsCodeLocal.noLocal, needsResult: true);

        expect(result, equals('test result'));
      });

      test('应该能执行日期处理函数', () async {
        // 设置mock返回预期的日期结果
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/04/30');

        final result = await jsExecutor.eval(
          "dayHandle('2025/04/29 15:10', +1);",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('2025/04/30'));

        // 验证JavaScript代码被正确调用
        verify(mockWebController.evaluateJavascript(source: anyNamed('source'))).called(1);
      });

      test('应该正确处理异步函数转换', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '');

        await jsExecutor.eval('setValue("testItem", "testValue");', JsCodeLocal.customizedLogicJavascript);

        // 验证代码被执行
        verify(mockWebController.evaluateJavascript(source: anyNamed('source'))).called(1);
      });

      test('应该正确处理并发控制', () async {
        // 设置一个长时间运行的JavaScript执行
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return '';
        });

        // 同时启动两个执行
        final future1 = jsExecutor.eval('console.log("1");', JsCodeLocal.noLocal);
        final future2 = jsExecutor.eval('console.log("2");', JsCodeLocal.noLocal);

        final results = await Future.wait([future1, future2]);

        // 第二个应该被跳过（返回空字符串）
        expect(results[0], equals(''));
        expect(results[1], equals(''));

        // 只应该有一次实际的JavaScript执行
        verify(mockWebController.evaluateJavascript(source: anyNamed('source'))).called(1);
      });

      test('应该正确处理执行错误', () async {
        when(
          mockWebController.evaluateJavascript(source: anyNamed('source')),
        ).thenThrow(Exception('JavaScript execution failed'));

        final result = await jsExecutor.eval('throw new Error("test error");', JsCodeLocal.noLocal);

        expect(result, equals(''));
      });
    });

    group('日期处理函数专项测试', () {
      setUp(() async {
        // 设置asset文件mock
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              final String key = methodCall.arguments as String;
              if (key.contains('customizedLogicJavascript.js')) {
                return TestJavaScriptCode.dateHandleFunctions;
              }
            }
            return '';
          },
        );

        // WebViewController已经在主setUp中通过测试构造函数注入
      });

      test('dayHandle函数应该正确处理日期加法', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/04/30');

        final result = await jsExecutor.eval(
          "dayHandle('2025/04/29 15:10', +1);",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('2025/04/30'));
      });

      test('dayHandle函数应该正确处理日期减法', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/04/28');

        final result = await jsExecutor.eval(
          "dayHandle('2025/04/29 15:10', -1);",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('2025/04/28'));
      });

      test('monthHandle函数应该正确处理月份加法', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/05/29');

        final result = await jsExecutor.eval(
          "monthHandle('2025/04/29 15:10', +1);",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('2025/05/29'));
      });

      test('yearHandle函数应该正确处理年份加法', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2026/04/29');

        final result = await jsExecutor.eval(
          "yearHandle('2025/04/29 15:10', +1);",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('2026/04/29'));
      });

      test('dayBetween函数应该正确计算日期差', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '1');

        final result = await jsExecutor.eval(
          "dayBetween('2025/04/29', '2025/04/30');",
          JsCodeLocal.customizedLogicJavascript,
          needsResult: true,
        );

        expect(result, equals('1'));
      });
    });

    group('代码类型测试', () {
      test('noLocal类型应该不加载基础JavaScript文件', () async {
        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '');

        await jsExecutor.eval('console.log("test");', JsCodeLocal.noLocal);

        // 验证执行的代码不包含基础JavaScript文件内容
        final captured = verify(mockWebController.evaluateJavascript(source: captureAnyNamed('source'))).captured;

        expect(captured.length, equals(1));
        final executedCode = captured[0] as String;
        expect(executedCode, contains('console.log("test");'));
      });

      test('customizedLogicJavascript类型应该加载基础文件并转换异步函数', () async {
        // 设置asset文件mock
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return 'console.log("Base JS loaded");';
            }
            return '';
          },
        );

        when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '');

        await jsExecutor.eval('setValue("test", "value");', JsCodeLocal.customizedLogicJavascript);

        // 验证执行的代码包含基础JavaScript文件和await转换
        final captured = verify(mockWebController.evaluateJavascript(source: captureAnyNamed('source'))).captured;

        expect(captured.length, equals(1));
        final executedCode = captured[0] as String;
        expect(executedCode, contains('console.log("Base JS loaded");'));
        expect(executedCode, contains('await setValue("test", "value");'));
      });
    });
  });
}
