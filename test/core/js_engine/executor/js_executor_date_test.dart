import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:mockito/mockito.dart';

import '../../../mocks/js_executor_mocks.dart';
import '../../../mocks/js_executor_mocks.mocks.dart';

void main() {
  // 初始化Flutter测试绑定
  TestWidgetsFlutterBinding.ensureInitialized();

  // 初始化LogUtil
  setUpAll(() {
    LogUtil.initialize(
      printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50, colors: false, printEmojis: false),
    );
  });

  group('JsExecutor 日期处理函数测试', () {
    late JsExecutor jsExecutor;
    late MockInAppWebViewController mockWebController;
    late JsFlutterBridgeService mockBridgeService;

    setUp(() {
      // 创建mock对象
      mockWebController = createMockWebViewController();
      mockBridgeService = createMockJsFlutterBridgeService();

      // 创建JsExecutor实例，使用测试构造函数注入mock WebViewController
      jsExecutor = JsExecutor.withController(mockBridgeService, mockWebController);

      // 标记WebView已初始化，跳过实际的WebView初始化过程
      jsExecutor.isInitWebView = true;

      // 模拟asset文件读取
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('flutter/assets'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'loadString') {
            final String key = methodCall.arguments as String;
            if (key.contains('customizedLogicJavascript.js')) {
              return TestJavaScriptCode.dateHandleFunctions;
            }
          }
          return '';
        },
      );
    });

    tearDown(() {
      jsExecutor.dispose();
      // 清理asset文件mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('flutter/assets'),
        null,
      );
    });

    test('dayHandle函数应该正确处理日期加法 - 测试用例: dayHandle("2025/04/29 15:10", +1)', () async {
      // 设置mock返回预期的日期结果
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/04/30');

      final result = await jsExecutor.eval(
        'dayHandle("2025/04/29 15:10", +1);',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('2025/04/30'));

      // 验证执行的代码包含日期处理函数
      final captured = verify(
        mockWebController.evaluateJavascript(source: captureAnyNamed('source'), contentWorld: anyNamed('contentWorld')),
      ).captured;

      expect(captured.length, equals(1));
      final executedCode = captured[0] as String;
      expect(executedCode, contains('dayHandle'));
      expect(executedCode, contains('2025/04/29 15:10'));
      expect(executedCode, contains('+1'));
    });

    test('dayHandle函数应该正确处理日期减法', () async {
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/04/28');

      final result = await jsExecutor.eval(
        'dayHandle("2025/04/29 15:10", -1);',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('2025/04/28'));
    });

    test('monthHandle函数应该正确处理月份加法', () async {
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/05/29');

      final result = await jsExecutor.eval(
        'monthHandle("2025/04/29 15:10", +1);',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('2025/05/29'));
    });

    test('yearHandle函数应该正确处理年份加法', () async {
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2026/04/29');

      final result = await jsExecutor.eval(
        'yearHandle("2025/04/29 15:10", +1);',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('2026/04/29'));
    });

    test('dayBetween函数应该正确计算日期差', () async {
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '1');

      final result = await jsExecutor.eval(
        'dayBetween("2025/04/29", "2025/04/30");',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('1'));
    });

    test('应该能正确执行复杂的日期处理逻辑', () async {
      when(mockWebController.evaluateJavascript(source: anyNamed('source'))).thenAnswer((_) async => '2025/05/01');

      final result = await jsExecutor.eval(
        '''
        var startDate = "2025/04/29 15:10";
        var endDate = dayHandle(startDate, +2);
        endDate;
        ''',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      expect(result, equals('2025/05/01'));
    });

    test('验证JavaScript代码结构正确', () async {
      when(
        mockWebController.evaluateJavascript(source: anyNamed('source'), contentWorld: anyNamed('contentWorld')),
      ).thenAnswer((_) async => '2025/04/30');

      await jsExecutor.eval(
        'dayHandle("2025/04/29 15:10", +1);',
        JsCodeLocal.customizedLogicJavascript,
        needsResult: true,
      );

      // 获取执行的JavaScript代码
      final captured = verify(
        mockWebController.evaluateJavascript(source: captureAnyNamed('source'), contentWorld: anyNamed('contentWorld')),
      ).captured;

      final executedCode = captured[0] as String;

      // 由于asset文件读取失败，基础函数定义不会被包含在执行代码中
      // 但用户代码应该被包含
      expect(executedCode, contains('dayHandle'));

      // 验证代码被包装在执行函数中
      expect(executedCode, contains('(function()'));
      expect(executedCode, contains('try {'));
      expect(executedCode, contains('var result ='));
    });
  });
}
