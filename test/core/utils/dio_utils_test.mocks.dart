// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/core/utils/dio_utils_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart' as _i3;
import 'package:dio/dio.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDio_0 extends _i1.SmartFake implements _i2.Dio {
  _FakeDio_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponse_1<T> extends _i1.SmartFake implements _i2.Response<T> {
  _FakeResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DioUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockDioUtil extends _i1.Mock implements _i3.DioUtil {
  @override
  _i2.Dio get dio =>
      (super.noSuchMethod(
            Invocation.getter(#dio),
            returnValue: _FakeDio_0(this, Invocation.getter(#dio)),
            returnValueForMissingStub: _FakeDio_0(
              this,
              Invocation.getter(#dio),
            ),
          )
          as _i2.Dio);

  @override
  _i4.Future<_i2.Response<dynamic>> get(
    String? path, {
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#get, [path], {#queryParams: queryParams}),
            returnValue: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(#get, [path], {#queryParams: queryParams}),
              ),
            ),
            returnValueForMissingStub: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(#get, [path], {#queryParams: queryParams}),
              ),
            ),
          )
          as _i4.Future<_i2.Response<dynamic>>);

  @override
  _i4.Future<_i2.Response<dynamic>> post(
    String? path, {
    dynamic data,
    bool? useFormUrlEncoded = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [path],
              {#data: data, #useFormUrlEncoded: useFormUrlEncoded},
            ),
            returnValue: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #post,
                  [path],
                  {#data: data, #useFormUrlEncoded: useFormUrlEncoded},
                ),
              ),
            ),
            returnValueForMissingStub: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #post,
                  [path],
                  {#data: data, #useFormUrlEncoded: useFormUrlEncoded},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.Response<dynamic>>);

  @override
  _i4.Future<_i2.Response<dynamic>> put(String? path, {dynamic data}) =>
      (super.noSuchMethod(
            Invocation.method(#put, [path], {#data: data}),
            returnValue: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(#put, [path], {#data: data}),
              ),
            ),
            returnValueForMissingStub: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(#put, [path], {#data: data}),
              ),
            ),
          )
          as _i4.Future<_i2.Response<dynamic>>);

  @override
  _i4.Future<_i2.Response<dynamic>> delete(
    String? path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [path],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #delete,
                  [path],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
            returnValueForMissingStub: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #delete,
                  [path],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.Response<dynamic>>);

  @override
  _i4.Future<_i2.Response<dynamic>> uploadBytes(
    String? path, {
    required List<int>? bytes,
    Map<String, dynamic>? headers,
    _i2.ProgressCallback? onSendProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #uploadBytes,
              [path],
              {
                #bytes: bytes,
                #headers: headers,
                #onSendProgress: onSendProgress,
              },
            ),
            returnValue: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #uploadBytes,
                  [path],
                  {
                    #bytes: bytes,
                    #headers: headers,
                    #onSendProgress: onSendProgress,
                  },
                ),
              ),
            ),
            returnValueForMissingStub: _i4.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_1<dynamic>(
                this,
                Invocation.method(
                  #uploadBytes,
                  [path],
                  {
                    #bytes: bytes,
                    #headers: headers,
                    #onSendProgress: onSendProgress,
                  },
                ),
              ),
            ),
          )
          as _i4.Future<_i2.Response<dynamic>>);
}
