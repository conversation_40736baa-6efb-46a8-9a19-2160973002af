// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/mocks/js_executor_mocks.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i23;
import 'dart:typed_data' as _i24;
import 'dart:ui' as _i27;

import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart' as _i21;
import 'package:asset_force_mobile_v2/core/network/base_response.dart' as _i13;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i20;
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart'
    as _i37;
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart'
    as _i38;
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart'
    as _i18;
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart'
    as _i19;
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart'
    as _i17;
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_image_item_controller.dart'
    as _i33;
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart'
    as _i30;
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart'
    as _i34;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_customize_logic_usecase.dart'
    as _i36;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_history_records_count_response.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart'
    as _i31;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart'
    as _i32;
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart'
    as _i35;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_id_response.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_keyword_response.dart'
    as _i16;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_layout_setting_response.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart'
    as _i10;
import 'package:flutter_inappwebview/src/in_app_webview/android/in_app_webview_controller.dart'
    as _i2;
import 'package:flutter_inappwebview/src/in_app_webview/apple/in_app_webview_controller.dart'
    as _i3;
import 'package:flutter_inappwebview/src/in_app_webview/headless_in_app_webview.dart'
    as _i29;
import 'package:flutter_inappwebview/src/in_app_webview/in_app_webview_controller.dart'
    as _i22;
import 'package:flutter_inappwebview/src/print_job/main.dart' as _i26;
import 'package:flutter_inappwebview/src/web_message/main.dart' as _i28;
import 'package:flutter_inappwebview/src/web_storage/web_storage.dart' as _i5;
import 'package:flutter_inappwebview_platform_interface/flutter_inappwebview_platform_interface.dart'
    as _i4;
import 'package:get/get.dart' as _i6;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i39;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i25;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAndroidInAppWebViewController_0 extends _i1.SmartFake
    implements _i2.AndroidInAppWebViewController {
  _FakeAndroidInAppWebViewController_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeIOSInAppWebViewController_1 extends _i1.SmartFake
    implements _i3.IOSInAppWebViewController {
  _FakeIOSInAppWebViewController_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePlatformInAppWebViewController_2 extends _i1.SmartFake
    implements _i4.PlatformInAppWebViewController {
  _FakePlatformInAppWebViewController_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeWebStorage_3 extends _i1.SmartFake implements _i5.WebStorage {
  _FakeWebStorage_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePlatformHeadlessInAppWebView_4 extends _i1.SmartFake
    implements _i4.PlatformHeadlessInAppWebView {
  _FakePlatformHeadlessInAppWebView_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_5<T> extends _i1.SmartFake
    implements _i6.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCustomizeAssetListModelResponse_6 extends _i1.SmartFake
    implements _i7.CustomizeAssetListModelResponse {
  _FakeCustomizeAssetListModelResponse_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeAssetMobileResponse_7 extends _i1.SmartFake
    implements _i8.AssetMobileResponse {
  _FakeAssetMobileResponse_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMasterLayoutSettingResponse_8 extends _i1.SmartFake
    implements _i9.MasterLayoutSettingResponse {
  _FakeMasterLayoutSettingResponse_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetRelationListResponse_9 extends _i1.SmartFake
    implements _i10.AssetRelationListResponse {
  _FakeAssetRelationListResponse_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetByIdResponse_10 extends _i1.SmartFake
    implements _i11.AssetByIdResponse {
  _FakeAssetByIdResponse_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetItemResponse_11 extends _i1.SmartFake
    implements _i12.AssetItemResponse {
  _FakeAssetItemResponse_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBaseResponse_12 extends _i1.SmartFake implements _i13.BaseResponse {
  _FakeBaseResponse_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetLayoutSettingResponse_13 extends _i1.SmartFake
    implements _i14.AssetLayoutSettingResponse {
  _FakeAssetLayoutSettingResponse_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetHistoryRecordsCountResponse_14 extends _i1.SmartFake
    implements _i15.AssetHistoryRecordsCountResponse {
  _FakeAssetHistoryRecordsCountResponse_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeAssetByKeywordResponse_15 extends _i1.SmartFake
    implements _i16.AssetByKeywordResponse {
  _FakeAssetByKeywordResponse_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_16 extends _i1.SmartFake implements _i6.RxBool {
  _FakeRxBool_16(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxList_17<E> extends _i1.SmartFake implements _i6.RxList<E> {
  _FakeRxList_17(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePrepareAssetDataUseCase_18 extends _i1.SmartFake
    implements _i17.PrepareAssetDataUseCase {
  _FakePrepareAssetDataUseCase_18(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCalcAssetDictUseCase_19 extends _i1.SmartFake
    implements _i18.CalcAssetDictUseCase {
  _FakeCalcAssetDictUseCase_19(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCheckValidateUseCase_20 extends _i1.SmartFake
    implements _i19.CheckValidateUseCase {
  _FakeCheckValidateUseCase_20(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxString_21 extends _i1.SmartFake implements _i6.RxString {
  _FakeRxString_21(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_22 extends _i1.SmartFake
    implements _i20.NavigationService {
  _FakeNavigationService_22(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeJsExecutor_23 extends _i1.SmartFake implements _i21.JsExecutor {
  _FakeJsExecutor_23(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCalcAssetDictResult_24 extends _i1.SmartFake
    implements _i18.CalcAssetDictResult {
  _FakeCalcAssetDictResult_24(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [InAppWebViewController].
///
/// See the documentation for Mockito's code generation for more information.
class MockInAppWebViewController extends _i1.Mock
    implements _i22.InAppWebViewController {
  MockInAppWebViewController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AndroidInAppWebViewController get android =>
      (super.noSuchMethod(
            Invocation.getter(#android),
            returnValue: _FakeAndroidInAppWebViewController_0(
              this,
              Invocation.getter(#android),
            ),
          )
          as _i2.AndroidInAppWebViewController);

  @override
  set android(_i2.AndroidInAppWebViewController? _android) =>
      super.noSuchMethod(
        Invocation.setter(#android, _android),
        returnValueForMissingStub: null,
      );

  @override
  _i3.IOSInAppWebViewController get ios =>
      (super.noSuchMethod(
            Invocation.getter(#ios),
            returnValue: _FakeIOSInAppWebViewController_1(
              this,
              Invocation.getter(#ios),
            ),
          )
          as _i3.IOSInAppWebViewController);

  @override
  set ios(_i3.IOSInAppWebViewController? _ios) => super.noSuchMethod(
    Invocation.setter(#ios, _ios),
    returnValueForMissingStub: null,
  );

  @override
  _i4.PlatformInAppWebViewController get platform =>
      (super.noSuchMethod(
            Invocation.getter(#platform),
            returnValue: _FakePlatformInAppWebViewController_2(
              this,
              Invocation.getter(#platform),
            ),
          )
          as _i4.PlatformInAppWebViewController);

  @override
  _i5.WebStorage get webStorage =>
      (super.noSuchMethod(
            Invocation.getter(#webStorage),
            returnValue: _FakeWebStorage_3(
              this,
              Invocation.getter(#webStorage),
            ),
          )
          as _i5.WebStorage);

  @override
  _i23.Future<_i4.WebUri?> getUrl() =>
      (super.noSuchMethod(
            Invocation.method(#getUrl, []),
            returnValue: _i23.Future<_i4.WebUri?>.value(),
          )
          as _i23.Future<_i4.WebUri?>);

  @override
  _i23.Future<String?> getTitle() =>
      (super.noSuchMethod(
            Invocation.method(#getTitle, []),
            returnValue: _i23.Future<String?>.value(),
          )
          as _i23.Future<String?>);

  @override
  _i23.Future<int?> getProgress() =>
      (super.noSuchMethod(
            Invocation.method(#getProgress, []),
            returnValue: _i23.Future<int?>.value(),
          )
          as _i23.Future<int?>);

  @override
  _i23.Future<String?> getHtml() =>
      (super.noSuchMethod(
            Invocation.method(#getHtml, []),
            returnValue: _i23.Future<String?>.value(),
          )
          as _i23.Future<String?>);

  @override
  _i23.Future<List<_i4.Favicon>> getFavicons() =>
      (super.noSuchMethod(
            Invocation.method(#getFavicons, []),
            returnValue: _i23.Future<List<_i4.Favicon>>.value(<_i4.Favicon>[]),
          )
          as _i23.Future<List<_i4.Favicon>>);

  @override
  _i23.Future<void> loadUrl({
    required _i4.URLRequest? urlRequest,
    Uri? iosAllowingReadAccessTo,
    _i4.WebUri? allowingReadAccessTo,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loadUrl, [], {
              #urlRequest: urlRequest,
              #iosAllowingReadAccessTo: iosAllowingReadAccessTo,
              #allowingReadAccessTo: allowingReadAccessTo,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> postUrl({
    required _i4.WebUri? url,
    required _i24.Uint8List? postData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#postUrl, [], {#url: url, #postData: postData}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> loadData({
    required String? data,
    String? mimeType = 'text/html',
    String? encoding = 'utf8',
    _i4.WebUri? baseUrl,
    Uri? androidHistoryUrl,
    _i4.WebUri? historyUrl,
    Uri? iosAllowingReadAccessTo,
    _i4.WebUri? allowingReadAccessTo,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loadData, [], {
              #data: data,
              #mimeType: mimeType,
              #encoding: encoding,
              #baseUrl: baseUrl,
              #androidHistoryUrl: androidHistoryUrl,
              #historyUrl: historyUrl,
              #iosAllowingReadAccessTo: iosAllowingReadAccessTo,
              #allowingReadAccessTo: allowingReadAccessTo,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> loadFile({required String? assetFilePath}) =>
      (super.noSuchMethod(
            Invocation.method(#loadFile, [], {#assetFilePath: assetFilePath}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> goBack() =>
      (super.noSuchMethod(
            Invocation.method(#goBack, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> canGoBack() =>
      (super.noSuchMethod(
            Invocation.method(#canGoBack, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> goForward() =>
      (super.noSuchMethod(
            Invocation.method(#goForward, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> canGoForward() =>
      (super.noSuchMethod(
            Invocation.method(#canGoForward, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> goBackOrForward({required int? steps}) =>
      (super.noSuchMethod(
            Invocation.method(#goBackOrForward, [], {#steps: steps}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> canGoBackOrForward({required int? steps}) =>
      (super.noSuchMethod(
            Invocation.method(#canGoBackOrForward, [], {#steps: steps}),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> goTo({required _i4.WebHistoryItem? historyItem}) =>
      (super.noSuchMethod(
            Invocation.method(#goTo, [], {#historyItem: historyItem}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> isLoading() =>
      (super.noSuchMethod(
            Invocation.method(#isLoading, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> stopLoading() =>
      (super.noSuchMethod(
            Invocation.method(#stopLoading, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<dynamic> evaluateJavascript({
    required String? source,
    _i4.ContentWorld? contentWorld,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#evaluateJavascript, [], {
              #source: source,
              #contentWorld: contentWorld,
            }),
            returnValue: _i23.Future<dynamic>.value(),
          )
          as _i23.Future<dynamic>);

  @override
  _i23.Future<void> injectJavascriptFileFromUrl({
    required _i4.WebUri? urlFile,
    _i4.ScriptHtmlTagAttributes? scriptHtmlTagAttributes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#injectJavascriptFileFromUrl, [], {
              #urlFile: urlFile,
              #scriptHtmlTagAttributes: scriptHtmlTagAttributes,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<dynamic> injectJavascriptFileFromAsset({
    required String? assetFilePath,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#injectJavascriptFileFromAsset, [], {
              #assetFilePath: assetFilePath,
            }),
            returnValue: _i23.Future<dynamic>.value(),
          )
          as _i23.Future<dynamic>);

  @override
  _i23.Future<void> injectCSSCode({required String? source}) =>
      (super.noSuchMethod(
            Invocation.method(#injectCSSCode, [], {#source: source}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> injectCSSFileFromUrl({
    required _i4.WebUri? urlFile,
    _i4.CSSLinkHtmlTagAttributes? cssLinkHtmlTagAttributes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#injectCSSFileFromUrl, [], {
              #urlFile: urlFile,
              #cssLinkHtmlTagAttributes: cssLinkHtmlTagAttributes,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> injectCSSFileFromAsset({required String? assetFilePath}) =>
      (super.noSuchMethod(
            Invocation.method(#injectCSSFileFromAsset, [], {
              #assetFilePath: assetFilePath,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void addJavaScriptHandler({
    required String? handlerName,
    required _i4.JavaScriptHandlerCallback? callback,
  }) => super.noSuchMethod(
    Invocation.method(#addJavaScriptHandler, [], {
      #handlerName: handlerName,
      #callback: callback,
    }),
    returnValueForMissingStub: null,
  );

  @override
  _i4.JavaScriptHandlerCallback? removeJavaScriptHandler({
    required String? handlerName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeJavaScriptHandler, [], {
              #handlerName: handlerName,
            }),
          )
          as _i4.JavaScriptHandlerCallback?);

  @override
  bool hasJavaScriptHandler({required String? handlerName}) =>
      (super.noSuchMethod(
            Invocation.method(#hasJavaScriptHandler, [], {
              #handlerName: handlerName,
            }),
            returnValue: false,
          )
          as bool);

  @override
  _i23.Future<_i24.Uint8List?> takeScreenshot({
    _i4.ScreenshotConfiguration? screenshotConfiguration,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#takeScreenshot, [], {
              #screenshotConfiguration: screenshotConfiguration,
            }),
            returnValue: _i23.Future<_i24.Uint8List?>.value(),
          )
          as _i23.Future<_i24.Uint8List?>);

  @override
  _i23.Future<void> setOptions({
    required _i4.InAppWebViewGroupOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setOptions, [], {#options: options}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.InAppWebViewGroupOptions?> getOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getOptions, []),
            returnValue: _i23.Future<_i4.InAppWebViewGroupOptions?>.value(),
          )
          as _i23.Future<_i4.InAppWebViewGroupOptions?>);

  @override
  _i23.Future<void> setSettings({
    required _i4.InAppWebViewSettings? settings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setSettings, [], {#settings: settings}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.InAppWebViewSettings?> getSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getSettings, []),
            returnValue: _i23.Future<_i4.InAppWebViewSettings?>.value(),
          )
          as _i23.Future<_i4.InAppWebViewSettings?>);

  @override
  _i23.Future<_i4.WebHistory?> getCopyBackForwardList() =>
      (super.noSuchMethod(
            Invocation.method(#getCopyBackForwardList, []),
            returnValue: _i23.Future<_i4.WebHistory?>.value(),
          )
          as _i23.Future<_i4.WebHistory?>);

  @override
  _i23.Future<void> clearCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearCache, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> findAllAsync({required String? find}) =>
      (super.noSuchMethod(
            Invocation.method(#findAllAsync, [], {#find: find}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> findNext({required bool? forward}) =>
      (super.noSuchMethod(
            Invocation.method(#findNext, [], {#forward: forward}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> clearMatches() =>
      (super.noSuchMethod(
            Invocation.method(#clearMatches, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<String> getTRexRunnerHtml() =>
      (super.noSuchMethod(
            Invocation.method(#getTRexRunnerHtml, []),
            returnValue: _i23.Future<String>.value(
              _i25.dummyValue<String>(
                this,
                Invocation.method(#getTRexRunnerHtml, []),
              ),
            ),
          )
          as _i23.Future<String>);

  @override
  _i23.Future<String> getTRexRunnerCss() =>
      (super.noSuchMethod(
            Invocation.method(#getTRexRunnerCss, []),
            returnValue: _i23.Future<String>.value(
              _i25.dummyValue<String>(
                this,
                Invocation.method(#getTRexRunnerCss, []),
              ),
            ),
          )
          as _i23.Future<String>);

  @override
  _i23.Future<void> scrollTo({
    required int? x,
    required int? y,
    bool? animated = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scrollTo, [], {
              #x: x,
              #y: y,
              #animated: animated,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> scrollBy({
    required int? x,
    required int? y,
    bool? animated = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scrollBy, [], {
              #x: x,
              #y: y,
              #animated: animated,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> pauseTimers() =>
      (super.noSuchMethod(
            Invocation.method(#pauseTimers, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> resumeTimers() =>
      (super.noSuchMethod(
            Invocation.method(#resumeTimers, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i26.PrintJobController?> printCurrentPage({
    _i4.PrintJobSettings? settings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#printCurrentPage, [], {#settings: settings}),
            returnValue: _i23.Future<_i26.PrintJobController?>.value(),
          )
          as _i23.Future<_i26.PrintJobController?>);

  @override
  _i23.Future<int?> getContentHeight() =>
      (super.noSuchMethod(
            Invocation.method(#getContentHeight, []),
            returnValue: _i23.Future<int?>.value(),
          )
          as _i23.Future<int?>);

  @override
  _i23.Future<int?> getContentWidth() =>
      (super.noSuchMethod(
            Invocation.method(#getContentWidth, []),
            returnValue: _i23.Future<int?>.value(),
          )
          as _i23.Future<int?>);

  @override
  _i23.Future<void> zoomBy({
    required double? zoomFactor,
    bool? iosAnimated,
    bool? animated = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#zoomBy, [], {
              #zoomFactor: zoomFactor,
              #iosAnimated: iosAnimated,
              #animated: animated,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.WebUri?> getOriginalUrl() =>
      (super.noSuchMethod(
            Invocation.method(#getOriginalUrl, []),
            returnValue: _i23.Future<_i4.WebUri?>.value(),
          )
          as _i23.Future<_i4.WebUri?>);

  @override
  _i23.Future<double?> getZoomScale() =>
      (super.noSuchMethod(
            Invocation.method(#getZoomScale, []),
            returnValue: _i23.Future<double?>.value(),
          )
          as _i23.Future<double?>);

  @override
  _i23.Future<double?> getScale() =>
      (super.noSuchMethod(
            Invocation.method(#getScale, []),
            returnValue: _i23.Future<double?>.value(),
          )
          as _i23.Future<double?>);

  @override
  _i23.Future<String?> getSelectedText() =>
      (super.noSuchMethod(
            Invocation.method(#getSelectedText, []),
            returnValue: _i23.Future<String?>.value(),
          )
          as _i23.Future<String?>);

  @override
  _i23.Future<_i4.InAppWebViewHitTestResult?> getHitTestResult() =>
      (super.noSuchMethod(
            Invocation.method(#getHitTestResult, []),
            returnValue: _i23.Future<_i4.InAppWebViewHitTestResult?>.value(),
          )
          as _i23.Future<_i4.InAppWebViewHitTestResult?>);

  @override
  _i23.Future<void> clearFocus() =>
      (super.noSuchMethod(
            Invocation.method(#clearFocus, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setContextMenu(_i4.ContextMenu? contextMenu) =>
      (super.noSuchMethod(
            Invocation.method(#setContextMenu, [contextMenu]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.RequestFocusNodeHrefResult?> requestFocusNodeHref() =>
      (super.noSuchMethod(
            Invocation.method(#requestFocusNodeHref, []),
            returnValue: _i23.Future<_i4.RequestFocusNodeHrefResult?>.value(),
          )
          as _i23.Future<_i4.RequestFocusNodeHrefResult?>);

  @override
  _i23.Future<_i4.RequestImageRefResult?> requestImageRef() =>
      (super.noSuchMethod(
            Invocation.method(#requestImageRef, []),
            returnValue: _i23.Future<_i4.RequestImageRefResult?>.value(),
          )
          as _i23.Future<_i4.RequestImageRefResult?>);

  @override
  _i23.Future<List<_i4.MetaTag>> getMetaTags() =>
      (super.noSuchMethod(
            Invocation.method(#getMetaTags, []),
            returnValue: _i23.Future<List<_i4.MetaTag>>.value(<_i4.MetaTag>[]),
          )
          as _i23.Future<List<_i4.MetaTag>>);

  @override
  _i23.Future<_i27.Color?> getMetaThemeColor() =>
      (super.noSuchMethod(
            Invocation.method(#getMetaThemeColor, []),
            returnValue: _i23.Future<_i27.Color?>.value(),
          )
          as _i23.Future<_i27.Color?>);

  @override
  _i23.Future<int?> getScrollX() =>
      (super.noSuchMethod(
            Invocation.method(#getScrollX, []),
            returnValue: _i23.Future<int?>.value(),
          )
          as _i23.Future<int?>);

  @override
  _i23.Future<int?> getScrollY() =>
      (super.noSuchMethod(
            Invocation.method(#getScrollY, []),
            returnValue: _i23.Future<int?>.value(),
          )
          as _i23.Future<int?>);

  @override
  _i23.Future<_i4.SslCertificate?> getCertificate() =>
      (super.noSuchMethod(
            Invocation.method(#getCertificate, []),
            returnValue: _i23.Future<_i4.SslCertificate?>.value(),
          )
          as _i23.Future<_i4.SslCertificate?>);

  @override
  _i23.Future<void> addUserScript({required _i4.UserScript? userScript}) =>
      (super.noSuchMethod(
            Invocation.method(#addUserScript, [], {#userScript: userScript}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> addUserScripts({
    required List<_i4.UserScript>? userScripts,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#addUserScripts, [], {#userScripts: userScripts}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> removeUserScript({required _i4.UserScript? userScript}) =>
      (super.noSuchMethod(
            Invocation.method(#removeUserScript, [], {#userScript: userScript}),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> removeUserScriptsByGroupName({
    required String? groupName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeUserScriptsByGroupName, [], {
              #groupName: groupName,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> removeUserScripts({
    required List<_i4.UserScript>? userScripts,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeUserScripts, [], {
              #userScripts: userScripts,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> removeAllUserScripts() =>
      (super.noSuchMethod(
            Invocation.method(#removeAllUserScripts, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  bool hasUserScript({required _i4.UserScript? userScript}) =>
      (super.noSuchMethod(
            Invocation.method(#hasUserScript, [], {#userScript: userScript}),
            returnValue: false,
          )
          as bool);

  @override
  _i23.Future<_i4.CallAsyncJavaScriptResult?> callAsyncJavaScript({
    required String? functionBody,
    Map<String, dynamic>? arguments = const {},
    _i4.ContentWorld? contentWorld,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#callAsyncJavaScript, [], {
              #functionBody: functionBody,
              #arguments: arguments,
              #contentWorld: contentWorld,
            }),
            returnValue: _i23.Future<_i4.CallAsyncJavaScriptResult?>.value(),
          )
          as _i23.Future<_i4.CallAsyncJavaScriptResult?>);

  @override
  _i23.Future<String?> saveWebArchive({
    required String? filePath,
    bool? autoname = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveWebArchive, [], {
              #filePath: filePath,
              #autoname: autoname,
            }),
            returnValue: _i23.Future<String?>.value(),
          )
          as _i23.Future<String?>);

  @override
  _i23.Future<bool> isSecureContext() =>
      (super.noSuchMethod(
            Invocation.method(#isSecureContext, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<_i28.WebMessageChannel?> createWebMessageChannel() =>
      (super.noSuchMethod(
            Invocation.method(#createWebMessageChannel, []),
            returnValue: _i23.Future<_i28.WebMessageChannel?>.value(),
          )
          as _i23.Future<_i28.WebMessageChannel?>);

  @override
  _i23.Future<void> postWebMessage({
    required _i4.WebMessage? message,
    _i4.WebUri? targetOrigin,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#postWebMessage, [], {
              #message: message,
              #targetOrigin: targetOrigin,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> addWebMessageListener(
    _i28.WebMessageListener? webMessageListener,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addWebMessageListener, [webMessageListener]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  bool hasWebMessageListener(_i28.WebMessageListener? webMessageListener) =>
      (super.noSuchMethod(
            Invocation.method(#hasWebMessageListener, [webMessageListener]),
            returnValue: false,
          )
          as bool);

  @override
  _i23.Future<bool> canScrollVertically() =>
      (super.noSuchMethod(
            Invocation.method(#canScrollVertically, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<bool> canScrollHorizontally() =>
      (super.noSuchMethod(
            Invocation.method(#canScrollHorizontally, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<bool> startSafeBrowsing() =>
      (super.noSuchMethod(
            Invocation.method(#startSafeBrowsing, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> clearSslPreferences() =>
      (super.noSuchMethod(
            Invocation.method(#clearSslPreferences, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> pause() =>
      (super.noSuchMethod(
            Invocation.method(#pause, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> resume() =>
      (super.noSuchMethod(
            Invocation.method(#resume, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<bool> pageDown({required bool? bottom}) =>
      (super.noSuchMethod(
            Invocation.method(#pageDown, [], {#bottom: bottom}),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<bool> pageUp({required bool? top}) =>
      (super.noSuchMethod(
            Invocation.method(#pageUp, [], {#top: top}),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<bool> zoomIn() =>
      (super.noSuchMethod(
            Invocation.method(#zoomIn, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<bool> zoomOut() =>
      (super.noSuchMethod(
            Invocation.method(#zoomOut, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> clearHistory() =>
      (super.noSuchMethod(
            Invocation.method(#clearHistory, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> reloadFromOrigin() =>
      (super.noSuchMethod(
            Invocation.method(#reloadFromOrigin, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i24.Uint8List?> createPdf({
    _i4.IOSWKPDFConfiguration? iosWKPdfConfiguration,
    _i4.PDFConfiguration? pdfConfiguration,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createPdf, [], {
              #iosWKPdfConfiguration: iosWKPdfConfiguration,
              #pdfConfiguration: pdfConfiguration,
            }),
            returnValue: _i23.Future<_i24.Uint8List?>.value(),
          )
          as _i23.Future<_i24.Uint8List?>);

  @override
  _i23.Future<_i24.Uint8List?> createWebArchiveData() =>
      (super.noSuchMethod(
            Invocation.method(#createWebArchiveData, []),
            returnValue: _i23.Future<_i24.Uint8List?>.value(),
          )
          as _i23.Future<_i24.Uint8List?>);

  @override
  _i23.Future<bool> hasOnlySecureContent() =>
      (super.noSuchMethod(
            Invocation.method(#hasOnlySecureContent, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> pauseAllMediaPlayback() =>
      (super.noSuchMethod(
            Invocation.method(#pauseAllMediaPlayback, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setAllMediaPlaybackSuspended({required bool? suspended}) =>
      (super.noSuchMethod(
            Invocation.method(#setAllMediaPlaybackSuspended, [], {
              #suspended: suspended,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> closeAllMediaPresentations() =>
      (super.noSuchMethod(
            Invocation.method(#closeAllMediaPresentations, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.MediaPlaybackState?> requestMediaPlaybackState() =>
      (super.noSuchMethod(
            Invocation.method(#requestMediaPlaybackState, []),
            returnValue: _i23.Future<_i4.MediaPlaybackState?>.value(),
          )
          as _i23.Future<_i4.MediaPlaybackState?>);

  @override
  _i23.Future<bool> isInFullscreen() =>
      (super.noSuchMethod(
            Invocation.method(#isInFullscreen, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<void> clearFormData() =>
      (super.noSuchMethod(
            Invocation.method(#clearFormData, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.MediaCaptureState?> getCameraCaptureState() =>
      (super.noSuchMethod(
            Invocation.method(#getCameraCaptureState, []),
            returnValue: _i23.Future<_i4.MediaCaptureState?>.value(),
          )
          as _i23.Future<_i4.MediaCaptureState?>);

  @override
  _i23.Future<void> setCameraCaptureState({
    required _i4.MediaCaptureState? state,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setCameraCaptureState, [], {#state: state}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i4.MediaCaptureState?> getMicrophoneCaptureState() =>
      (super.noSuchMethod(
            Invocation.method(#getMicrophoneCaptureState, []),
            returnValue: _i23.Future<_i4.MediaCaptureState?>.value(),
          )
          as _i23.Future<_i4.MediaCaptureState?>);

  @override
  _i23.Future<void> setMicrophoneCaptureState({
    required _i4.MediaCaptureState? state,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setMicrophoneCaptureState, [], {#state: state}),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> loadSimulatedRequest({
    required _i4.URLRequest? urlRequest,
    required _i24.Uint8List? data,
    _i4.URLResponse? urlResponse,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loadSimulatedRequest, [], {
              #urlRequest: urlRequest,
              #data: data,
              #urlResponse: urlResponse,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> openDevTools() =>
      (super.noSuchMethod(
            Invocation.method(#openDevTools, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<dynamic> callDevToolsProtocolMethod({
    required String? methodName,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#callDevToolsProtocolMethod, [], {
              #methodName: methodName,
              #parameters: parameters,
            }),
            returnValue: _i23.Future<dynamic>.value(),
          )
          as _i23.Future<dynamic>);

  @override
  _i23.Future<void> addDevToolsProtocolEventListener({
    required String? eventName,
    required dynamic Function(dynamic)? callback,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#addDevToolsProtocolEventListener, [], {
              #eventName: eventName,
              #callback: callback,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> removeDevToolsProtocolEventListener({
    required String? eventName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeDevToolsProtocolEventListener, [], {
              #eventName: eventName,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<String?> getIFrameId() =>
      (super.noSuchMethod(
            Invocation.method(#getIFrameId, []),
            returnValue: _i23.Future<String?>.value(),
          )
          as _i23.Future<String?>);

  @override
  void dispose({bool? isKeepAlive = false}) => super.noSuchMethod(
    Invocation.method(#dispose, [], {#isKeepAlive: isKeepAlive}),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [HeadlessInAppWebView].
///
/// See the documentation for Mockito's code generation for more information.
class MockHeadlessInAppWebView extends _i1.Mock
    implements _i29.HeadlessInAppWebView {
  MockHeadlessInAppWebView() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.PlatformHeadlessInAppWebView get platform =>
      (super.noSuchMethod(
            Invocation.getter(#platform),
            returnValue: _FakePlatformHeadlessInAppWebView_4(
              this,
              Invocation.getter(#platform),
            ),
          )
          as _i4.PlatformHeadlessInAppWebView);

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i25.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  _i23.Future<void> run() =>
      (super.noSuchMethod(
            Invocation.method(#run, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  bool isRunning() =>
      (super.noSuchMethod(Invocation.method(#isRunning, []), returnValue: false)
          as bool);

  @override
  _i23.Future<void> setSize(_i27.Size? size) =>
      (super.noSuchMethod(
            Invocation.method(#setSize, [size]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i27.Size?> getSize() =>
      (super.noSuchMethod(
            Invocation.method(#getSize, []),
            returnValue: _i23.Future<_i27.Size?>.value(),
          )
          as _i23.Future<_i27.Size?>);

  @override
  _i23.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);
}

/// A class which mocks [JsFlutterBridgeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockJsFlutterBridgeService extends _i1.Mock
    implements _i21.JsFlutterBridgeService {
  MockJsFlutterBridgeService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasController =>
      (super.noSuchMethod(Invocation.getter(#hasController), returnValue: false)
          as bool);

  @override
  bool get hasBatchUpdateManager =>
      (super.noSuchMethod(
            Invocation.getter(#hasBatchUpdateManager),
            returnValue: false,
          )
          as bool);

  @override
  void setController(_i30.AfCustomizeViewController? controller) =>
      super.noSuchMethod(
        Invocation.method(#setController, [controller]),
        returnValueForMissingStub: null,
      );

  @override
  void refreshItemNameCache() => super.noSuchMethod(
    Invocation.method(#refreshItemNameCache, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearController() => super.noSuchMethod(
    Invocation.method(#clearController, []),
    returnValueForMissingStub: null,
  );

  @override
  void setBatchUpdateManager(_i21.BatchUpdateManager? manager) =>
      super.noSuchMethod(
        Invocation.method(#setBatchUpdateManager, [manager]),
        returnValueForMissingStub: null,
      );

  @override
  void setCodeExecutionSuccessCallback(void Function()? callback) =>
      super.noSuchMethod(
        Invocation.method(#setCodeExecutionSuccessCallback, [callback]),
        returnValueForMissingStub: null,
      );

  @override
  void clearBatchUpdateManager() => super.noSuchMethod(
    Invocation.method(#clearBatchUpdateManager, []),
    returnValueForMissingStub: null,
  );

  @override
  void setClonedDataMode(bool? enabled, dynamic clonedData) =>
      super.noSuchMethod(
        Invocation.method(#setClonedDataMode, [enabled, clonedData]),
        returnValueForMissingStub: null,
      );

  @override
  _i23.Future<dynamic> callFunction(
    String? functionName,
    List<dynamic>? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#callFunction, [functionName, params]),
            returnValue: _i23.Future<dynamic>.value(),
          )
          as _i23.Future<dynamic>);

  @override
  List<String> getSupportedFunctions() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportedFunctions, []),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  bool isFunctionSupported(String? functionName) =>
      (super.noSuchMethod(
            Invocation.method(#isFunctionSupported, [functionName]),
            returnValue: false,
          )
          as bool);
}

/// A class which mocks [JsBridgeFunctionRegistry].
///
/// See the documentation for Mockito's code generation for more information.
class MockJsBridgeFunctionRegistry extends _i1.Mock
    implements _i21.JsBridgeFunctionRegistry {
  MockJsBridgeFunctionRegistry() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  _i6.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  bool isFunctionSupported(String? functionName) =>
      (super.noSuchMethod(
            Invocation.method(#isFunctionSupported, [functionName]),
            returnValue: false,
          )
          as bool);

  @override
  List<String> getSupportedFunctions() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportedFunctions, []),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [JsBridgeDataManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockJsBridgeDataManager extends _i1.Mock
    implements _i21.JsBridgeDataManager {
  MockJsBridgeDataManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasController =>
      (super.noSuchMethod(Invocation.getter(#hasController), returnValue: false)
          as bool);

  @override
  _i6.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  _i6.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  void setController(_i30.AfCustomizeViewController? controller) =>
      super.noSuchMethod(
        Invocation.method(#setController, [controller]),
        returnValueForMissingStub: null,
      );

  @override
  void refreshItemNameCache() => super.noSuchMethod(
    Invocation.method(#refreshItemNameCache, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearController() => super.noSuchMethod(
    Invocation.method(#clearController, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<Map<String, dynamic>> findItem(String? itemName) =>
      (super.noSuchMethod(
            Invocation.method(#findItem, [itemName]),
            returnValue: _i23.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i23.Future<Map<String, dynamic>>);

  @override
  _i23.Future<Map<String, dynamic>> findAndUpdateItem(
    String? itemName,
    dynamic itemValue,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#findAndUpdateItem, [itemName, itemValue]),
            returnValue: _i23.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i23.Future<Map<String, dynamic>>);

  @override
  dynamic getValueFromClonedData(
    String? itemName,
    dynamic clonedItemDataDict,
  ) => super.noSuchMethod(
    Invocation.method(#getValueFromClonedData, [itemName, clonedItemDataDict]),
  );

  @override
  void setValueInClonedData(
    String? itemName,
    dynamic value,
    dynamic clonedItemDataDict,
  ) => super.noSuchMethod(
    Invocation.method(#setValueInClonedData, [
      itemName,
      value,
      clonedItemDataDict,
    ]),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<void> syncClonedDataChanges(dynamic clonedData) =>
      (super.noSuchMethod(
            Invocation.method(#syncClonedDataChanges, [clonedData]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [JsBridgeStateManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockJsBridgeStateManager extends _i1.Mock
    implements _i21.JsBridgeStateManager {
  MockJsBridgeStateManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasBatchUpdateManager =>
      (super.noSuchMethod(
            Invocation.getter(#hasBatchUpdateManager),
            returnValue: false,
          )
          as bool);

  @override
  bool get isClonedDataMode =>
      (super.noSuchMethod(
            Invocation.getter(#isClonedDataMode),
            returnValue: false,
          )
          as bool);

  @override
  _i6.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  _i6.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  void setBatchUpdateManager(_i21.BatchUpdateManager? manager) =>
      super.noSuchMethod(
        Invocation.method(#setBatchUpdateManager, [manager]),
        returnValueForMissingStub: null,
      );

  @override
  void clearBatchUpdateManager() => super.noSuchMethod(
    Invocation.method(#clearBatchUpdateManager, []),
    returnValueForMissingStub: null,
  );

  @override
  void setCodeExecutionSuccessCallback(void Function()? callback) =>
      super.noSuchMethod(
        Invocation.method(#setCodeExecutionSuccessCallback, [callback]),
        returnValueForMissingStub: null,
      );

  @override
  void setClonedDataMode(bool? enabled, dynamic clonedData) =>
      super.noSuchMethod(
        Invocation.method(#setClonedDataMode, [enabled, clonedData]),
        returnValueForMissingStub: null,
      );

  @override
  _i23.Future<void> handleCodeExecutionSuccess() =>
      (super.noSuchMethod(
            Invocation.method(#handleCodeExecutionSuccess, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void handleCodeExecutionStart() => super.noSuchMethod(
    Invocation.method(#handleCodeExecutionStart, []),
    returnValueForMissingStub: null,
  );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [JsBridgeUiManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockJsBridgeUiManager extends _i1.Mock implements _i21.JsBridgeUiManager {
  MockJsBridgeUiManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  _i6.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i23.Future<dynamic> showAlert(List<dynamic>? params) =>
      (super.noSuchMethod(
            Invocation.method(#showAlert, [params]),
            returnValue: _i23.Future<dynamic>.value(),
          )
          as _i23.Future<dynamic>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AssetRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetRepository extends _i1.Mock implements _i31.AssetRepository {
  MockAssetRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i23.Future<_i7.CustomizeAssetListModelResponse> getCustomizeSearchForMobile({
    required _i32.CustomizeSearchForMobileRequestQuery? searchParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomizeSearchForMobile, [], {
              #searchParams: searchParams,
            }),
            returnValue: _i23.Future<_i7.CustomizeAssetListModelResponse>.value(
              _FakeCustomizeAssetListModelResponse_6(
                this,
                Invocation.method(#getCustomizeSearchForMobile, [], {
                  #searchParams: searchParams,
                }),
              ),
            ),
          )
          as _i23.Future<_i7.CustomizeAssetListModelResponse>);

  @override
  _i23.Future<_i8.AssetMobileResponse> getAssetMobileSettings({
    required int? assetTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetMobileSettings, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i23.Future<_i8.AssetMobileResponse>.value(
              _FakeAssetMobileResponse_7(
                this,
                Invocation.method(#getAssetMobileSettings, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i23.Future<_i8.AssetMobileResponse>);

  @override
  _i23.Future<_i9.MasterLayoutSettingResponse> getAllMasterLayoutSetting({
    required int? typeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllMasterLayoutSetting, [], {
              #typeId: typeId,
            }),
            returnValue: _i23.Future<_i9.MasterLayoutSettingResponse>.value(
              _FakeMasterLayoutSettingResponse_8(
                this,
                Invocation.method(#getAllMasterLayoutSetting, [], {
                  #typeId: typeId,
                }),
              ),
            ),
          )
          as _i23.Future<_i9.MasterLayoutSettingResponse>);

  @override
  _i23.Future<_i10.AssetRelationListResponse> getAssetRelationList({
    required int? assetId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetRelationList, [], {#assetId: assetId}),
            returnValue: _i23.Future<_i10.AssetRelationListResponse>.value(
              _FakeAssetRelationListResponse_9(
                this,
                Invocation.method(#getAssetRelationList, [], {
                  #assetId: assetId,
                }),
              ),
            ),
          )
          as _i23.Future<_i10.AssetRelationListResponse>);

  @override
  _i23.Future<_i11.AssetByIdResponse> getAssetById({required int? assetId}) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetById, [], {#assetId: assetId}),
            returnValue: _i23.Future<_i11.AssetByIdResponse>.value(
              _FakeAssetByIdResponse_10(
                this,
                Invocation.method(#getAssetById, [], {#assetId: assetId}),
              ),
            ),
          )
          as _i23.Future<_i11.AssetByIdResponse>);

  @override
  _i23.Future<bool> getAssetsPrintAble({required String? assetTypeIdsStr}) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetsPrintAble, [], {
              #assetTypeIdsStr: assetTypeIdsStr,
            }),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  _i23.Future<_i12.AssetItemResponse> getAssetItemType({
    required String? assetTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetItemType, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i23.Future<_i12.AssetItemResponse>.value(
              _FakeAssetItemResponse_11(
                this,
                Invocation.method(#getAssetItemType, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i23.Future<_i12.AssetItemResponse>);

  @override
  _i23.Future<_i13.BaseResponse> saveAsset({
    required String? assetId,
    required String? assetTypeId,
    required String? assetText,
    String? modifiedDate,
    String? barcode,
    String? relationAssetIdList,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveAsset, [], {
              #assetId: assetId,
              #assetTypeId: assetTypeId,
              #assetText: assetText,
              #modifiedDate: modifiedDate,
              #barcode: barcode,
              #relationAssetIdList: relationAssetIdList,
            }),
            returnValue: _i23.Future<_i13.BaseResponse>.value(
              _FakeBaseResponse_12(
                this,
                Invocation.method(#saveAsset, [], {
                  #assetId: assetId,
                  #assetTypeId: assetTypeId,
                  #assetText: assetText,
                  #modifiedDate: modifiedDate,
                  #barcode: barcode,
                  #relationAssetIdList: relationAssetIdList,
                }),
              ),
            ),
          )
          as _i23.Future<_i13.BaseResponse>);

  @override
  _i23.Future<_i14.AssetLayoutSettingResponse> getLayoutSetting({
    String? typeId = '0',
    String? classification = '5',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getLayoutSetting, [], {
              #typeId: typeId,
              #classification: classification,
            }),
            returnValue: _i23.Future<_i14.AssetLayoutSettingResponse>.value(
              _FakeAssetLayoutSettingResponse_13(
                this,
                Invocation.method(#getLayoutSetting, [], {
                  #typeId: typeId,
                  #classification: classification,
                }),
              ),
            ),
          )
          as _i23.Future<_i14.AssetLayoutSettingResponse>);

  @override
  _i23.Future<_i15.AssetHistoryRecordsCountResponse>
  getAssetHistoryRecordsCount({
    required int? assetId,
    required String? appurtenancesInformationTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetHistoryRecordsCount, [], {
              #assetId: assetId,
              #appurtenancesInformationTypeId: appurtenancesInformationTypeId,
            }),
            returnValue:
                _i23.Future<_i15.AssetHistoryRecordsCountResponse>.value(
                  _FakeAssetHistoryRecordsCountResponse_14(
                    this,
                    Invocation.method(#getAssetHistoryRecordsCount, [], {
                      #assetId: assetId,
                      #appurtenancesInformationTypeId:
                          appurtenancesInformationTypeId,
                    }),
                  ),
                ),
          )
          as _i23.Future<_i15.AssetHistoryRecordsCountResponse>);

  @override
  _i23.Future<_i16.AssetByKeywordResponse> findAssetByKeyword({
    required int? assetTypeid,
    required String? keyword,
    required int? currentPage,
    required int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#findAssetByKeyword, [], {
              #assetTypeid: assetTypeid,
              #keyword: keyword,
              #currentPage: currentPage,
              #pageSize: pageSize,
            }),
            returnValue: _i23.Future<_i16.AssetByKeywordResponse>.value(
              _FakeAssetByKeywordResponse_15(
                this,
                Invocation.method(#findAssetByKeyword, [], {
                  #assetTypeid: assetTypeid,
                  #keyword: keyword,
                  #currentPage: currentPage,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i23.Future<_i16.AssetByKeywordResponse>);
}

/// A class which mocks [AfCustomizeViewController].
///
/// See the documentation for Mockito's code generation for more information.
class MockAfCustomizeViewController extends _i1.Mock
    implements _i30.AfCustomizeViewController {
  MockAfCustomizeViewController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.RxBool get isUnitDay =>
      (super.noSuchMethod(
            Invocation.getter(#isUnitDay),
            returnValue: _FakeRxBool_16(this, Invocation.getter(#isUnitDay)),
          )
          as _i6.RxBool);

  @override
  _i6.RxList<_i33.AfCustomizeImageItemController> get imageControllers =>
      (super.noSuchMethod(
            Invocation.getter(#imageControllers),
            returnValue: _FakeRxList_17<_i33.AfCustomizeImageItemController>(
              this,
              Invocation.getter(#imageControllers),
            ),
          )
          as _i6.RxList<_i33.AfCustomizeImageItemController>);

  @override
  _i17.PrepareAssetDataUseCase get prepareAssetDataUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#prepareAssetDataUseCase),
            returnValue: _FakePrepareAssetDataUseCase_18(
              this,
              Invocation.getter(#prepareAssetDataUseCase),
            ),
          )
          as _i17.PrepareAssetDataUseCase);

  @override
  _i18.CalcAssetDictUseCase get calcAssetDictUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#calcAssetDictUseCase),
            returnValue: _FakeCalcAssetDictUseCase_19(
              this,
              Invocation.getter(#calcAssetDictUseCase),
            ),
          )
          as _i18.CalcAssetDictUseCase);

  @override
  _i19.CheckValidateUseCase get checkValidateUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#checkValidateUseCase),
            returnValue: _FakeCheckValidateUseCase_20(
              this,
              Invocation.getter(#checkValidateUseCase),
            ),
          )
          as _i19.CheckValidateUseCase);

  @override
  _i6.RxBool get previewFlg =>
      (super.noSuchMethod(
            Invocation.getter(#previewFlg),
            returnValue: _FakeRxBool_16(this, Invocation.getter(#previewFlg)),
          )
          as _i6.RxBool);

  @override
  _i6.RxString get liveTalkUrl =>
      (super.noSuchMethod(
            Invocation.getter(#liveTalkUrl),
            returnValue: _FakeRxString_21(
              this,
              Invocation.getter(#liveTalkUrl),
            ),
          )
          as _i6.RxString);

  @override
  _i34.AfCustomizeViewScene get scene =>
      (super.noSuchMethod(
            Invocation.getter(#scene),
            returnValue: _i34.AfCustomizeViewScene.assetDetail,
          )
          as _i34.AfCustomizeViewScene);

  @override
  set scene(_i34.AfCustomizeViewScene? _scene) => super.noSuchMethod(
    Invocation.setter(#scene, _scene),
    returnValueForMissingStub: null,
  );

  @override
  bool get isShowMasterDefaultValue =>
      (super.noSuchMethod(
            Invocation.getter(#isShowMasterDefaultValue),
            returnValue: false,
          )
          as bool);

  @override
  set isShowMasterDefaultValue(bool? _isShowMasterDefaultValue) =>
      super.noSuchMethod(
        Invocation.setter(#isShowMasterDefaultValue, _isShowMasterDefaultValue),
        returnValueForMissingStub: null,
      );

  @override
  set calcAssetDictResult(_i18.CalcAssetDictResult? _calcAssetDictResult) =>
      super.noSuchMethod(
        Invocation.setter(#calcAssetDictResult, _calcAssetDictResult),
        returnValueForMissingStub: null,
      );

  @override
  set needNavigationBar(bool? _needNavigationBar) => super.noSuchMethod(
    Invocation.setter(#needNavigationBar, _needNavigationBar),
    returnValueForMissingStub: null,
  );

  @override
  bool get needEnterValidate =>
      (super.noSuchMethod(
            Invocation.getter(#needEnterValidate),
            returnValue: false,
          )
          as bool);

  @override
  set needEnterValidate(bool? _needEnterValidate) => super.noSuchMethod(
    Invocation.setter(#needEnterValidate, _needEnterValidate),
    returnValueForMissingStub: null,
  );

  @override
  set scheduleEventList(_i35.AppointmentListResponse? _scheduleEventList) =>
      super.noSuchMethod(
        Invocation.setter(#scheduleEventList, _scheduleEventList),
        returnValueForMissingStub: null,
      );

  @override
  set appointment(_i35.Appointment? _appointment) => super.noSuchMethod(
    Invocation.setter(#appointment, _appointment),
    returnValueForMissingStub: null,
  );

  @override
  _i6.RxList<_i35.EventTypeItem> get eventTypeItems =>
      (super.noSuchMethod(
            Invocation.getter(#eventTypeItems),
            returnValue: _FakeRxList_17<_i35.EventTypeItem>(
              this,
              Invocation.getter(#eventTypeItems),
            ),
          )
          as _i6.RxList<_i35.EventTypeItem>);

  @override
  _i20.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_22(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i20.NavigationService);

  @override
  _i21.JsExecutor get jsExecutor =>
      (super.noSuchMethod(
            Invocation.getter(#jsExecutor),
            returnValue: _FakeJsExecutor_23(
              this,
              Invocation.getter(#jsExecutor),
            ),
          )
          as _i21.JsExecutor);

  @override
  set customizeLogicJs(_i36.GetCustomizeLogicResult? _customizeLogicJs) =>
      super.noSuchMethod(
        Invocation.setter(#customizeLogicJs, _customizeLogicJs),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, List<_i37.RxAssetItemWrapper>> get assetDict =>
      (super.noSuchMethod(
            Invocation.getter(#assetDict),
            returnValue: <String, List<_i37.RxAssetItemWrapper>>{},
          )
          as Map<String, List<_i37.RxAssetItemWrapper>>);

  @override
  set assetDict(Map<String, List<_i37.RxAssetItemWrapper>>? dict) =>
      super.noSuchMethod(
        Invocation.setter(#assetDict, dict),
        returnValueForMissingStub: null,
      );

  @override
  _i6.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  _i6.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i6.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(Invocation.getter(#listeners), returnValue: 0)
          as int);

  @override
  bool updateHomeImage({
    required _i37.RxAssetItemWrapper? item,
    bool? isSetHomeImage = false,
    int? index,
    List<_i38.RxImageModel>? imageModels,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateHomeImage, [], {
              #item: item,
              #isSetHomeImage: isSetHomeImage,
              #index: index,
              #imageModels: imageModels,
            }),
            returnValue: false,
          )
          as bool);

  @override
  void resetImagesHomeStatus() => super.noSuchMethod(
    Invocation.method(#resetImagesHomeStatus, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<void> updateAssetDict(Map<String, List<dynamic>>? newDict) =>
      (super.noSuchMethod(
            Invocation.method(#updateAssetDict, [newDict]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<List<_i19.CheckValidateResult>> validateAllItems() =>
      (super.noSuchMethod(
            Invocation.method(#validateAllItems, []),
            returnValue: _i23.Future<List<_i19.CheckValidateResult>>.value(
              <_i19.CheckValidateResult>[],
            ),
          )
          as _i23.Future<List<_i19.CheckValidateResult>>);

  @override
  void updateScene(_i34.AfCustomizeViewScene? scene) => super.noSuchMethod(
    Invocation.method(#updateScene, [scene]),
    returnValueForMissingStub: null,
  );

  @override
  void updateMaster(bool? showDefaultValue) => super.noSuchMethod(
    Invocation.method(#updateMaster, [showDefaultValue]),
    returnValueForMissingStub: null,
  );

  @override
  void updateCalculateItems(String? itemName) => super.noSuchMethod(
    Invocation.method(#updateCalculateItems, [itemName]),
    returnValueForMissingStub: null,
  );

  @override
  void recalculateItems(String? itemName) => super.noSuchMethod(
    Invocation.method(#recalculateItems, [itemName]),
    returnValueForMissingStub: null,
  );

  @override
  void registerImageController(
    _i33.AfCustomizeImageItemController? controller,
  ) => super.noSuchMethod(
    Invocation.method(#registerImageController, [controller]),
    returnValueForMissingStub: null,
  );

  @override
  void unregisterImageController(
    _i33.AfCustomizeImageItemController? controller,
  ) => super.noSuchMethod(
    Invocation.method(#unregisterImageController, [controller]),
    returnValueForMissingStub: null,
  );

  @override
  List<_i33.AfCustomizeImageItemController> getImageControllers() =>
      (super.noSuchMethod(
            Invocation.method(#getImageControllers, []),
            returnValue: <_i33.AfCustomizeImageItemController>[],
          )
          as List<_i33.AfCustomizeImageItemController>);

  @override
  _i37.RxAssetItemWrapper? findEntryByItemId(String? itemId) =>
      (super.noSuchMethod(Invocation.method(#findEntryByItemId, [itemId]))
          as _i37.RxAssetItemWrapper?);

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<_i18.CalcAssetDictResult> calcAssetDict() =>
      (super.noSuchMethod(
            Invocation.method(#calcAssetDict, []),
            returnValue: _i23.Future<_i18.CalcAssetDictResult>.value(
              _FakeCalcAssetDictResult_24(
                this,
                Invocation.method(#calcAssetDict, []),
              ),
            ),
          )
          as _i23.Future<_i18.CalcAssetDictResult>);

  @override
  _i23.Future<bool> validateAndShowErrors() =>
      (super.noSuchMethod(
            Invocation.method(#validateAndShowErrors, []),
            returnValue: _i23.Future<bool>.value(false),
          )
          as _i23.Future<bool>);

  @override
  String getAssetTextJson() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetTextJson, []),
            returnValue: _i25.dummyValue<String>(
              this,
              Invocation.method(#getAssetTextJson, []),
            ),
          )
          as String);

  @override
  void setNeedNavigationBar(bool? needNavigationBar) => super.noSuchMethod(
    Invocation.method(#setNeedNavigationBar, [needNavigationBar]),
    returnValueForMissingStub: null,
  );

  @override
  void setNeedEnterValidate(bool? needEnterValidate) => super.noSuchMethod(
    Invocation.method(#setNeedEnterValidate, [needEnterValidate]),
    returnValueForMissingStub: null,
  );

  @override
  void onItemChanged(_i37.RxAssetItemWrapper? entry) => super.noSuchMethod(
    Invocation.method(#onItemChanged, [entry]),
    returnValueForMissingStub: null,
  );

  @override
  void setScene(_i34.AfCustomizeViewScene? scene) => super.noSuchMethod(
    Invocation.method(#setScene, [scene]),
    returnValueForMissingStub: null,
  );

  @override
  void setUnitDay(bool? isUnitDay) => super.noSuchMethod(
    Invocation.method(#setUnitDay, [isUnitDay]),
    returnValueForMissingStub: null,
  );

  @override
  void updateScheduleEventList(
    _i35.AppointmentListResponse? scheduleEventList,
  ) => super.noSuchMethod(
    Invocation.method(#updateScheduleEventList, [scheduleEventList]),
    returnValueForMissingStub: null,
  );

  @override
  void setAppointment(_i35.Appointment? appointmentListItem) =>
      super.noSuchMethod(
        Invocation.method(#setAppointment, [appointmentListItem]),
        returnValueForMissingStub: null,
      );

  @override
  void updateEventTypeItems(List<_i35.EventTypeItem?>? eventTypeItems) =>
      super.noSuchMethod(
        Invocation.method(#updateEventTypeItems, [eventTypeItems]),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> buildAssetTextMap() =>
      (super.noSuchMethod(
            Invocation.method(#buildAssetTextMap, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void setCustomizeLogicJs(_i36.GetCustomizeLogicResult? customizeLogicJs) =>
      super.noSuchMethod(
        Invocation.method(#setCustomizeLogicJs, [customizeLogicJs]),
        returnValueForMissingStub: null,
      );

  @override
  _i23.Future<void> executeCustomizeLogicJs() =>
      (super.noSuchMethod(
            Invocation.method(#executeCustomizeLogicJs, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> updateJsExecutorData() =>
      (super.noSuchMethod(
            Invocation.method(#updateJsExecutorData, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i39.Disposer addListener(_i39.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
          )
          as _i39.Disposer);

  @override
  void removeListener(_i27.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i27.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i39.Disposer addListenerId(Object? key, _i39.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
          )
          as _i39.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [BatchUpdateManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockBatchUpdateManager extends _i1.Mock
    implements _i21.BatchUpdateManager {
  MockBatchUpdateManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void beginBatch() => super.noSuchMethod(
    Invocation.method(#beginBatch, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<void> commitBatch() =>
      (super.noSuchMethod(
            Invocation.method(#commitBatch, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> rollbackBatch() =>
      (super.noSuchMethod(
            Invocation.method(#rollbackBatch, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void setValue(String? itemName, dynamic value, {int? priority = 0}) =>
      super.noSuchMethod(
        Invocation.method(#setValue, [itemName, value], {#priority: priority}),
        returnValueForMissingStub: null,
      );

  @override
  dynamic getValue(String? itemName) =>
      super.noSuchMethod(Invocation.method(#getValue, [itemName]));

  @override
  bool isModified(String? itemName) =>
      (super.noSuchMethod(
            Invocation.method(#isModified, [itemName]),
            returnValue: false,
          )
          as bool);

  @override
  Set<String> getModifiedItems() =>
      (super.noSuchMethod(
            Invocation.method(#getModifiedItems, []),
            returnValue: <String>{},
          )
          as Set<String>);

  @override
  void addBatchCompleteCallback(_i21.VoidCallback? callback) =>
      super.noSuchMethod(
        Invocation.method(#addBatchCompleteCallback, [callback]),
        returnValueForMissingStub: null,
      );

  @override
  void removeBatchCompleteCallback(_i21.VoidCallback? callback) =>
      super.noSuchMethod(
        Invocation.method(#removeBatchCompleteCallback, [callback]),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [CowDataManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockCowDataManager extends _i1.Mock implements _i21.CowDataManager {
  MockCowDataManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get modificationCount =>
      (super.noSuchMethod(Invocation.getter(#modificationCount), returnValue: 0)
          as int);

  @override
  void beginTransaction() => super.noSuchMethod(
    Invocation.method(#beginTransaction, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<void> commitTransaction() =>
      (super.noSuchMethod(
            Invocation.method(#commitTransaction, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void rollbackTransaction() => super.noSuchMethod(
    Invocation.method(#rollbackTransaction, []),
    returnValueForMissingStub: null,
  );

  @override
  dynamic getValue(String? itemName) =>
      super.noSuchMethod(Invocation.method(#getValue, [itemName]));

  @override
  void setValue(String? itemName, dynamic value) => super.noSuchMethod(
    Invocation.method(#setValue, [itemName, value]),
    returnValueForMissingStub: null,
  );

  @override
  bool isModified(String? itemName) =>
      (super.noSuchMethod(
            Invocation.method(#isModified, [itemName]),
            returnValue: false,
          )
          as bool);

  @override
  Set<String> getModifiedItems() =>
      (super.noSuchMethod(
            Invocation.method(#getModifiedItems, []),
            returnValue: <String>{},
          )
          as Set<String>);

  @override
  void clearModifications() => super.noSuchMethod(
    Invocation.method(#clearModifications, []),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getDebugInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getDebugInfo, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
