import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_flutter_bridge_service.dart';
import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 生成Mock类的注解
@GenerateMocks([
  InAppWebViewController,
  HeadlessInAppWebView,
  JsFlutterBridgeService,
  JsBridgeFunctionRegistry,
  JsBridgeDataManager,
  JsBridgeStateManager,
  JsBridgeUiManager,
  AssetRepository,
  AfCustomizeViewController,
  BatchUpdateManager,
  CowDataManager,
])
import 'js_executor_mocks.mocks.dart';

/// 创建测试用的JsFlutterBridgeService
JsFlutterBridgeService createMockJsFlutterBridgeService() {
  final mockFunctionRegistry = MockJsBridgeFunctionRegistry();
  final mockDataManager = MockJsBridgeDataManager();
  final mockStateManager = MockJsBridgeStateManager();
  final mockUiManager = MockJsBridgeUiManager();
  final mockAssetRepository = MockAssetRepository();

  // 设置默认行为
  when(mockFunctionRegistry.isFunctionSupported(any)).thenReturn(true);
  when(mockDataManager.hasController).thenReturn(false);
  when(mockStateManager.hasBatchUpdateManager).thenReturn(false);

  return JsFlutterBridgeService(
    functionRegistry: mockFunctionRegistry,
    dataManager: mockDataManager,
    stateManager: mockStateManager,
    uiManager: mockUiManager,
    assetRepository: mockAssetRepository,
  );
}

/// 创建测试用的WebViewController Mock
MockInAppWebViewController createMockWebViewController() {
  final mockController = MockInAppWebViewController();
  
  // 设置默认行为
  when(mockController.addJavaScriptHandler(
    handlerName: anyNamed('handlerName'),
    callback: anyNamed('callback'),
  )).thenAnswer((_) async {});

  when(mockController.loadData(
    data: anyNamed('data'),
    mimeType: anyNamed('mimeType'),
    encoding: anyNamed('encoding'),
  )).thenAnswer((_) async {});

  // 默认JavaScript执行返回空字符串
  when(mockController.evaluateJavascript(source: anyNamed('source')))
      .thenAnswer((_) async => '');

  return mockController;
}

/// 测试用的JavaScript代码片段
class TestJavaScriptCode {
  /// 日期处理函数的JavaScript代码
  static const String dateHandleFunctions = '''
function getFormatDateReturnDate(date) {
  const formattedDateTimeStr = getFormatDate(date);
  return new Date(formattedDateTimeStr);
}

function getFormatDate(date) {
  const dateObject = validateDateInput(date);
  if (dateObject === undefined || dateObject === null) {
    return date;
  }
  return formatAndReturn(dateObject, 'yyyy/MM/dd');
}

function validateDateInput(date) {
  let dateObject;
  if (typeof date === 'string') {
    dateObject = parseDateString(date);
  } else {
    dateObject = date;
  }
  if (isValid(dateObject)) {
    return dateObject;
  }
  return undefined;
}

function isValid(date) {
  return date instanceof Date && !isNaN(date.getTime());
}

function parseDateString(dateString) {
  // 处理 / 和 空格 格式问题
  if (dateString.includes('/')) {
    dateString = dateString.replace(/\\\\/g, '-');
  }
  if (dateString.includes(' ')) {
    dateString = dateString.replace(' ', 'T');
  }

  const parsedDate = new Date(dateString);

  if (!isNaN(parsedDate.getTime())) {
    return parsedDate;
  }
  return undefined;
}

function formatAndReturn(date, formatStr) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return formatStr
    .replace('yyyy', year)
    .replace('MM', month)
    .replace('dd', day)
    .replace('HH', hours)
    .replace('mm', minutes);
}

function dayHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setDate(date.getDate() + quantity);
  return formatAndReturn(date, 'yyyy/MM/dd');
}

function monthHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  let day = date.getDate();
  date.setMonth(date.getMonth() + quantity);
  let day2 = date.getDate();
  if (day > day2) {
    date = new Date(date.getFullYear(), date.getMonth(), 0);
  }
  return formatAndReturn(date, 'yyyy/MM/dd');
}

function yearHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setFullYear(date.getFullYear() + quantity);
  return formatAndReturn(date, 'yyyy/MM/dd');
}

function now() {
  return new Date();
}

function dayBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }

  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }

  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (24 * 3600 * 1000));
  return days;
}

function monthBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (30 * 24 * 3600 * 1000));
  return days;
}

function yearBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (365 * 24 * 3600 * 1000));
  return days;
}
''';

  /// 测试用例：dayHandle('2025/04/29 15:10', +1)
  static const String dayHandleTestCase = '''
\$dateHandleFunctions

// 执行测试用例
dayHandle('2025/04/29 15:10', +1);
''';
}
