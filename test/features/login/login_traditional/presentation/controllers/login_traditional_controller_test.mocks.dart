// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_traditional/presentation/controllers/login_traditional_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i12;
import 'dart:ui' as _i23;

import 'package:asset_force_mobile_v2/core/deeplink/i_deeplink_service.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i3;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart'
    as _i22;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i20;
import 'package:asset_force_mobile_v2/core/storage/i_storage_utils.dart' as _i7;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i21;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i16;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/usecases/login_traditional_usecase.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart'
    as _i17;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart'
    as _i18;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart'
    as _i19;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i24;
import 'package:asset_force_mobile_v2/features/skill_plugin/i_sso_scan_plugin.dart'
    as _i5;
import 'package:flutter/services.dart' as _i25;
import 'package:local_auth/local_auth.dart' as _i14;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i15;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginTraditionalRepository_0 extends _i1.SmartFake
    implements _i2.LoginTraditionalRepository {
  _FakeLoginTraditionalRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_1 extends _i1.SmartFake implements _i3.IEnvHelper {
  _FakeIEnvHelper_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginResultModel_2 extends _i1.SmartFake
    implements _i4.LoginResultModel {
  _FakeLoginResultModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeISsoScanPlugin_3 extends _i1.SmartFake
    implements _i5.ISsoScanPlugin {
  _FakeISsoScanPlugin_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIDeeplinkService_4 extends _i1.SmartFake
    implements _i6.IDeeplinkService {
  _FakeIDeeplinkService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_5 extends _i1.SmartFake implements _i7.IStorageUtils {
  _FakeIStorageUtils_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTenantRepository_6 extends _i1.SmartFake
    implements _i8.TenantRepository {
  _FakeTenantRepository_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserRepository_7 extends _i1.SmartFake
    implements _i9.UserRepository {
  _FakeUserRepository_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetTokenResultModel_8 extends _i1.SmartFake
    implements _i10.GetTokenResultModel {
  _FakeGetTokenResultModel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginTraditionalUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginTraditionalUseCase extends _i1.Mock
    implements _i11.LoginTraditionalUseCase {
  @override
  _i2.LoginTraditionalRepository get loginRepository =>
      (super.noSuchMethod(
            Invocation.getter(#loginRepository),
            returnValue: _FakeLoginTraditionalRepository_0(
              this,
              Invocation.getter(#loginRepository),
            ),
            returnValueForMissingStub: _FakeLoginTraditionalRepository_0(
              this,
              Invocation.getter(#loginRepository),
            ),
          )
          as _i2.LoginTraditionalRepository);

  @override
  _i3.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_1(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_1(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i3.IEnvHelper);

  @override
  _i12.Future<_i4.LoginResultModel> call(_i11.LoginParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i12.Future<_i4.LoginResultModel>.value(
              _FakeLoginResultModel_2(this, Invocation.method(#call, [params])),
            ),
            returnValueForMissingStub: _i12.Future<_i4.LoginResultModel>.value(
              _FakeLoginResultModel_2(this, Invocation.method(#call, [params])),
            ),
          )
          as _i12.Future<_i4.LoginResultModel>);

  @override
  _i12.Future<void> versionCheck() =>
      (super.noSuchMethod(
            Invocation.method(#versionCheck, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);
}

/// A class which mocks [BiometricsSwitchUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchUtil extends _i1.Mock
    implements _i13.BiometricsSwitchUtil {
  @override
  _i12.Future<String?> isAccountWorkingProperly({required String? password}) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountWorkingProperly, [], {
              #password: password,
            }),
            returnValue: _i12.Future<String?>.value(),
            returnValueForMissingStub: _i12.Future<String?>.value(),
          )
          as _i12.Future<String?>);

  @override
  _i12.Future<void> closeOrOpenBiometrics({required bool? isOpen}) =>
      (super.noSuchMethod(
            Invocation.method(#closeOrOpenBiometrics, [], {#isOpen: isOpen}),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  bool getUserBiometricsOpenSwitch() =>
      (super.noSuchMethod(
            Invocation.method(#getUserBiometricsOpenSwitch, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i12.Future<bool> isBiometricsAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricsAvailable, []),
            returnValue: _i12.Future<bool>.value(false),
            returnValueForMissingStub: _i12.Future<bool>.value(false),
          )
          as _i12.Future<bool>);

  @override
  _i12.Future<List<_i14.BiometricType>> getAvailableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableBiometrics, []),
            returnValue: _i12.Future<List<_i14.BiometricType>>.value(
              <_i14.BiometricType>[],
            ),
            returnValueForMissingStub:
                _i12.Future<List<_i14.BiometricType>>.value(
                  <_i14.BiometricType>[],
                ),
          )
          as _i12.Future<List<_i14.BiometricType>>);

  @override
  _i12.Future<void> authenticateBiometricsSetting() =>
      (super.noSuchMethod(
            Invocation.method(#authenticateBiometricsSetting, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> setBiometricSecretPassword({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricSecretPassword, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<String> getBiometricSecretPassword() =>
      (super.noSuchMethod(
            Invocation.method(#getBiometricSecretPassword, []),
            returnValue: _i12.Future<String>.value(
              _i15.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
            returnValueForMissingStub: _i12.Future<String>.value(
              _i15.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
          )
          as _i12.Future<String>);
}

/// A class which mocks [LoginSsoUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoUseCase extends _i1.Mock implements _i16.LoginSsoUseCase {
  @override
  _i5.ISsoScanPlugin get ssoScanPlugin =>
      (super.noSuchMethod(
            Invocation.getter(#ssoScanPlugin),
            returnValue: _FakeISsoScanPlugin_3(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
            returnValueForMissingStub: _FakeISsoScanPlugin_3(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
          )
          as _i5.ISsoScanPlugin);

  @override
  _i6.IDeeplinkService get deeplinkService =>
      (super.noSuchMethod(
            Invocation.getter(#deeplinkService),
            returnValue: _FakeIDeeplinkService_4(
              this,
              Invocation.getter(#deeplinkService),
            ),
            returnValueForMissingStub: _FakeIDeeplinkService_4(
              this,
              Invocation.getter(#deeplinkService),
            ),
          )
          as _i6.IDeeplinkService);

  @override
  _i7.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_5(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_5(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i7.IStorageUtils);

  @override
  _i3.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_1(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_1(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i3.IEnvHelper);

  @override
  _i12.Future<dynamic> call(String? pragma) =>
      (super.noSuchMethod(
            Invocation.method(#call, [pragma]),
            returnValue: _i12.Future<dynamic>.value(),
            returnValueForMissingStub: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);

  @override
  _i12.Future<String?> openScanBarcode() =>
      (super.noSuchMethod(
            Invocation.method(#openScanBarcode, []),
            returnValue: _i12.Future<String?>.value(),
            returnValueForMissingStub: _i12.Future<String?>.value(),
          )
          as _i12.Future<String?>);

  @override
  List<_i17.TenantSsoModel> fromJsonList({required String? jsonString}) =>
      (super.noSuchMethod(
            Invocation.method(#fromJsonList, [], {#jsonString: jsonString}),
            returnValue: <_i17.TenantSsoModel>[],
            returnValueForMissingStub: <_i17.TenantSsoModel>[],
          )
          as List<_i17.TenantSsoModel>);

  @override
  String processTenants({required List<_i17.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processTenants, [], {#tenants: tenants}),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
          )
          as String);

  @override
  String processOrderTenants({required List<_i17.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
          )
          as String);
}

/// A class which mocks [IEnvHelper].
///
/// See the documentation for Mockito's code generation for more information.
class MockIEnvHelper extends _i1.Mock implements _i3.IEnvHelper {
  @override
  set environment(String? value) => super.noSuchMethod(
    Invocation.setter(#environment, value),
    returnValueForMissingStub: null,
  );

  @override
  bool isAndroid() =>
      (super.noSuchMethod(
            Invocation.method(#isAndroid, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isIOS() =>
      (super.noSuchMethod(
            Invocation.method(#isIOS, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isWindows() =>
      (super.noSuchMethod(
            Invocation.method(#isWindows, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isLinux() =>
      (super.noSuchMethod(
            Invocation.method(#isLinux, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isMacOS() =>
      (super.noSuchMethod(
            Invocation.method(#isMacOS, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  String getEnvironment() =>
      (super.noSuchMethod(
            Invocation.method(#getEnvironment, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getEnvironment, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getEnvironment, []),
            ),
          )
          as String);

  @override
  bool isProd() =>
      (super.noSuchMethod(
            Invocation.method(#isProd, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isDev() =>
      (super.noSuchMethod(
            Invocation.method(#isDev, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isQa() =>
      (super.noSuchMethod(
            Invocation.method(#isQa, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool isStg() =>
      (super.noSuchMethod(
            Invocation.method(#isStg, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  String getAppVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppVersion, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppVersion, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppVersion, []),
            ),
          )
          as String);

  @override
  String getApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiHost, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getApiHost, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getApiHost, []),
            ),
          )
          as String);

  @override
  String getAuthApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getAuthApiHost, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAuthApiHost, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAuthApiHost, []),
            ),
          )
          as String);

  @override
  String getApiGatewayHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiGatewayHost, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getApiGatewayHost, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getApiGatewayHost, []),
            ),
          )
          as String);

  @override
  String getSocketApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getSocketApiHost, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getSocketApiHost, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getSocketApiHost, []),
            ),
          )
          as String);

  @override
  String getReportApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getReportApiHost, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getReportApiHost, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getReportApiHost, []),
            ),
          )
          as String);

  @override
  String getAppNativeName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeName, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeName, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeName, []),
            ),
          )
          as String);

  @override
  String getAppNativePackageName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativePackageName, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativePackageName, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativePackageName, []),
            ),
          )
          as String);

  @override
  String getAppNativeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeVersion, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeVersion, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeVersion, []),
            ),
          )
          as String);

  @override
  String getAppNativeBuildNumber() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeBuildNumber, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeBuildNumber, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeBuildNumber, []),
            ),
          )
          as String);

  @override
  _i12.Future<void> updateAuthHost() =>
      (super.noSuchMethod(
            Invocation.method(#updateAuthHost, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> updateServerEnv() =>
      (super.noSuchMethod(
            Invocation.method(#updateServerEnv, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> setHost(String? zoneId) =>
      (super.noSuchMethod(
            Invocation.method(#setHost, [zoneId]),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i7.IStorageUtils {
  @override
  _i12.Future<void> set<T>(String? key, T? value) =>
      (super.noSuchMethod(
            Invocation.method(#set, [key, value]),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  T? get<T>(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#get, [key]),
            returnValueForMissingStub: null,
          )
          as T?);

  @override
  _i12.Future<void> remove(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#remove, [key]),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> clear() =>
      (super.noSuchMethod(
            Invocation.method(#clear, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  bool contains(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#contains, [key]),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
            returnValueForMissingStub: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i12.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);
}

/// A class which mocks [TenantUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockTenantUseCase extends _i1.Mock implements _i18.TenantUseCase {
  @override
  _i8.TenantRepository get tenantRepository =>
      (super.noSuchMethod(
            Invocation.getter(#tenantRepository),
            returnValue: _FakeTenantRepository_6(
              this,
              Invocation.getter(#tenantRepository),
            ),
            returnValueForMissingStub: _FakeTenantRepository_6(
              this,
              Invocation.getter(#tenantRepository),
            ),
          )
          as _i8.TenantRepository);

  @override
  _i9.UserRepository get userRepository =>
      (super.noSuchMethod(
            Invocation.getter(#userRepository),
            returnValue: _FakeUserRepository_7(
              this,
              Invocation.getter(#userRepository),
            ),
            returnValueForMissingStub: _FakeUserRepository_7(
              this,
              Invocation.getter(#userRepository),
            ),
          )
          as _i9.UserRepository);

  @override
  _i3.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_1(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_1(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i3.IEnvHelper);

  @override
  _i7.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_5(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_5(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i7.IStorageUtils);

  @override
  _i12.Future<_i10.GetTokenResultModel> getBasicInfo(
    _i19.SelectTenantModel? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBasicInfo, [params]),
            returnValue: _i12.Future<_i10.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_8(
                this,
                Invocation.method(#getBasicInfo, [params]),
              ),
            ),
            returnValueForMissingStub:
                _i12.Future<_i10.GetTokenResultModel>.value(
                  _FakeGetTokenResultModel_8(
                    this,
                    Invocation.method(#getBasicInfo, [params]),
                  ),
                ),
          )
          as _i12.Future<_i10.GetTokenResultModel>);

  @override
  _i12.Future<_i10.GetTokenResultModel> call(_i19.SelectTenantModel? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i12.Future<_i10.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_8(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
            returnValueForMissingStub:
                _i12.Future<_i10.GetTokenResultModel>.value(
                  _FakeGetTokenResultModel_8(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i12.Future<_i10.GetTokenResultModel>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i20.NavigationService {
  @override
  _i12.Future<dynamic> navigateTo(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<dynamic>.value(),
            returnValueForMissingStub: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);

  @override
  _i12.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<dynamic>.value(),
            returnValueForMissingStub: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);

  @override
  _i12.Future<bool> navigateUntil(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<bool>.value(false),
            returnValueForMissingStub: _i12.Future<bool>.value(false),
          )
          as _i12.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<dynamic> toAssetDetail(_i21.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i12.Future<dynamic>.value(),
            returnValueForMissingStub: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i22.DialogService {
  @override
  _i12.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i23.VoidCallback? onConfirm,
    _i23.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i24.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i23.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i25.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i24.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}
