// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_traditional/domain/usecases/login_traditional_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i6;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginResultModel_0 extends _i1.SmartFake
    implements _i2.LoginResultModel {
  _FakeLoginResultModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSystemMobileDeployManageResultModel_1 extends _i1.SmartFake
    implements _i3.SystemMobileDeployManageResultModel {
  _FakeSystemMobileDeployManageResultModel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [LoginTraditionalRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginTraditionalRepository extends _i1.Mock
    implements _i4.LoginTraditionalRepository {
  MockLoginTraditionalRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.LoginResultModel> login({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#login, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i5.Future<_i2.LoginResultModel>.value(
              _FakeLoginResultModel_0(
                this,
                Invocation.method(#login, [], {
                  #userName: userName,
                  #password: password,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.LoginResultModel>);

  @override
  _i5.Future<_i3.SystemMobileDeployManageResultModel>
  getMobileDeployManageApiHostVersion({required int? appClientType}) =>
      (super.noSuchMethod(
            Invocation.method(#getMobileDeployManageApiHostVersion, [], {
              #appClientType: appClientType,
            }),
            returnValue:
                _i5.Future<_i3.SystemMobileDeployManageResultModel>.value(
                  _FakeSystemMobileDeployManageResultModel_1(
                    this,
                    Invocation.method(
                      #getMobileDeployManageApiHostVersion,
                      [],
                      {#appClientType: appClientType},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i3.SystemMobileDeployManageResultModel>);

  @override
  _i5.Future<_i3.SystemMobileDeployManageResultModel?>
  getMobileDeployMobileVersion({
    required String? mobileVersion,
    required int? appClientType,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getMobileDeployMobileVersion, [], {
              #mobileVersion: mobileVersion,
              #appClientType: appClientType,
            }),
            returnValue:
                _i5.Future<_i3.SystemMobileDeployManageResultModel?>.value(),
          )
          as _i5.Future<_i3.SystemMobileDeployManageResultModel?>);
}

/// A class which mocks [IEnvHelper].
///
/// See the documentation for Mockito's code generation for more information.
class MockIEnvHelper extends _i1.Mock implements _i6.IEnvHelper {
  MockIEnvHelper() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set environment(String? value) => super.noSuchMethod(
    Invocation.setter(#environment, value),
    returnValueForMissingStub: null,
  );

  @override
  bool isAndroid() =>
      (super.noSuchMethod(Invocation.method(#isAndroid, []), returnValue: false)
          as bool);

  @override
  bool isIOS() =>
      (super.noSuchMethod(Invocation.method(#isIOS, []), returnValue: false)
          as bool);

  @override
  bool isWindows() =>
      (super.noSuchMethod(Invocation.method(#isWindows, []), returnValue: false)
          as bool);

  @override
  bool isLinux() =>
      (super.noSuchMethod(Invocation.method(#isLinux, []), returnValue: false)
          as bool);

  @override
  bool isMacOS() =>
      (super.noSuchMethod(Invocation.method(#isMacOS, []), returnValue: false)
          as bool);

  @override
  String getEnvironment() =>
      (super.noSuchMethod(
            Invocation.method(#getEnvironment, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getEnvironment, []),
            ),
          )
          as String);

  @override
  bool isProd() =>
      (super.noSuchMethod(Invocation.method(#isProd, []), returnValue: false)
          as bool);

  @override
  bool isDev() =>
      (super.noSuchMethod(Invocation.method(#isDev, []), returnValue: false)
          as bool);

  @override
  bool isQa() =>
      (super.noSuchMethod(Invocation.method(#isQa, []), returnValue: false)
          as bool);

  @override
  bool isStg() =>
      (super.noSuchMethod(Invocation.method(#isStg, []), returnValue: false)
          as bool);

  @override
  String getAppVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppVersion, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAppVersion, []),
            ),
          )
          as String);

  @override
  String getApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiHost, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getApiHost, []),
            ),
          )
          as String);

  @override
  String getAuthApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getAuthApiHost, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAuthApiHost, []),
            ),
          )
          as String);

  @override
  String getApiGatewayHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiGatewayHost, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getApiGatewayHost, []),
            ),
          )
          as String);

  @override
  String getSocketApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getSocketApiHost, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getSocketApiHost, []),
            ),
          )
          as String);

  @override
  String getReportApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getReportApiHost, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getReportApiHost, []),
            ),
          )
          as String);

  @override
  String getAppNativeName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeName, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeName, []),
            ),
          )
          as String);

  @override
  String getAppNativePackageName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativePackageName, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAppNativePackageName, []),
            ),
          )
          as String);

  @override
  String getAppNativeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeVersion, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeVersion, []),
            ),
          )
          as String);

  @override
  String getAppNativeBuildNumber() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeBuildNumber, []),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeBuildNumber, []),
            ),
          )
          as String);

  @override
  _i5.Future<void> updateAuthHost() =>
      (super.noSuchMethod(
            Invocation.method(#updateAuthHost, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateServerEnv() =>
      (super.noSuchMethod(
            Invocation.method(#updateServerEnv, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setHost(String? zoneId) =>
      (super.noSuchMethod(
            Invocation.method(#setHost, [zoneId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
