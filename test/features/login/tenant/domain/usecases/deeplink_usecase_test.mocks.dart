// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/tenant/domain/usecases/deeplink_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;

import 'package:asset_force_mobile_v2/core/deeplink/i_deeplink_service.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i8;
import 'package:asset_force_mobile_v2/core/storage/i_storage_utils.dart' as _i7;
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i17;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart'
    as _i18;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart'
    as _i16;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/skill_plugin/i_sso_scan_plugin.dart'
    as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i19;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetTokenResultModel_0 extends _i1.SmartFake
    implements _i2.GetTokenResultModel {
  _FakeGetTokenResultModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedMyAccountModel_1 extends _i1.SmartFake
    implements _i3.SharedMyAccountModel {
  _FakeSharedMyAccountModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedUserTenantModel_2 extends _i1.SmartFake
    implements _i4.SharedUserTenantModel {
  _FakeSharedUserTenantModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeISsoScanPlugin_3 extends _i1.SmartFake
    implements _i5.ISsoScanPlugin {
  _FakeISsoScanPlugin_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIDeeplinkService_4 extends _i1.SmartFake
    implements _i6.IDeeplinkService {
  _FakeIDeeplinkService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_5 extends _i1.SmartFake implements _i7.IStorageUtils {
  _FakeIStorageUtils_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_6 extends _i1.SmartFake implements _i8.IEnvHelper {
  _FakeIEnvHelper_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TenantRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTenantRepository extends _i1.Mock implements _i9.TenantRepository {
  MockTenantRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i10.Future<_i2.GetTokenResultModel> loginTenant({
    required String? ticket,
    required String? tenantId,
    required bool? isBiometrics,
    String? code,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginTenant, [], {
              #ticket: ticket,
              #tenantId: tenantId,
              #isBiometrics: isBiometrics,
              #code: code,
            }),
            returnValue: _i10.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#loginTenant, [], {
                  #ticket: ticket,
                  #tenantId: tenantId,
                  #isBiometrics: isBiometrics,
                  #code: code,
                }),
              ),
            ),
          )
          as _i10.Future<_i2.GetTokenResultModel>);

  @override
  _i10.Future<_i2.GetTokenResultModel> getFullUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getFullUserInfo, []),
            returnValue: _i10.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getFullUserInfo, []),
              ),
            ),
          )
          as _i10.Future<_i2.GetTokenResultModel>);

  @override
  _i10.Future<_i2.GetTokenResultModel> getVerificationCode({
    required String? ticket,
    required String? countryCode,
    required String? phone,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getVerificationCode, [], {
              #ticket: ticket,
              #countryCode: countryCode,
              #phone: phone,
            }),
            returnValue: _i10.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getVerificationCode, [], {
                  #ticket: ticket,
                  #countryCode: countryCode,
                  #phone: phone,
                }),
              ),
            ),
          )
          as _i10.Future<_i2.GetTokenResultModel>);

  @override
  _i10.Future<_i2.GetTokenResultModel> getSendMfaMailVerifyCode({
    required String? ticket,
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getSendMfaMailVerifyCode, [], {
              #ticket: ticket,
              #email: email,
            }),
            returnValue: _i10.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getSendMfaMailVerifyCode, [], {
                  #ticket: ticket,
                  #email: email,
                }),
              ),
            ),
          )
          as _i10.Future<_i2.GetTokenResultModel>);

  @override
  _i10.Future<_i2.GetTokenResultModel> getUserBindPhone({
    required String? ticket,
    required String? countryCode,
    required String? phone,
    required String? userName,
    required String? password,
    required String? code,
    required String? tenantId,
    required int? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserBindPhone, [], {
              #ticket: ticket,
              #countryCode: countryCode,
              #phone: phone,
              #userName: userName,
              #password: password,
              #code: code,
              #tenantId: tenantId,
              #userId: userId,
            }),
            returnValue: _i10.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getUserBindPhone, [], {
                  #ticket: ticket,
                  #countryCode: countryCode,
                  #phone: phone,
                  #userName: userName,
                  #password: password,
                  #code: code,
                  #tenantId: tenantId,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i10.Future<_i2.GetTokenResultModel>);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i11.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i10.Future<_i3.SharedMyAccountModel> getUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getUserInfo, []),
            returnValue: _i10.Future<_i3.SharedMyAccountModel>.value(
              _FakeSharedMyAccountModel_1(
                this,
                Invocation.method(#getUserInfo, []),
              ),
            ),
          )
          as _i10.Future<_i3.SharedMyAccountModel>);

  @override
  _i10.Future<_i4.SharedUserTenantModel> getUserTenant() =>
      (super.noSuchMethod(
            Invocation.method(#getUserTenant, []),
            returnValue: _i10.Future<_i4.SharedUserTenantModel>.value(
              _FakeSharedUserTenantModel_2(
                this,
                Invocation.method(#getUserTenant, []),
              ),
            ),
          )
          as _i10.Future<_i4.SharedUserTenantModel>);

  @override
  _i10.Future<bool> getAuthorityInfo(int? functionId) =>
      (super.noSuchMethod(
            Invocation.method(#getAuthorityInfo, [functionId]),
            returnValue: _i10.Future<bool>.value(false),
          )
          as _i10.Future<bool>);

  @override
  _i10.Future<List<_i12.UserRoleResponseData>> getUserRole() =>
      (super.noSuchMethod(
            Invocation.method(#getUserRole, []),
            returnValue: _i10.Future<List<_i12.UserRoleResponseData>>.value(
              <_i12.UserRoleResponseData>[],
            ),
          )
          as _i10.Future<List<_i12.UserRoleResponseData>>);

  @override
  _i10.Future<bool> updateUserInfo(dynamic userInfo) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserInfo, [userInfo]),
            returnValue: _i10.Future<bool>.value(false),
          )
          as _i10.Future<bool>);

  @override
  _i10.Future<List<_i13.SharedUserModel>> getUserList() =>
      (super.noSuchMethod(
            Invocation.method(#getUserList, []),
            returnValue: _i10.Future<List<_i13.SharedUserModel>>.value(
              <_i13.SharedUserModel>[],
            ),
          )
          as _i10.Future<List<_i13.SharedUserModel>>);

  @override
  _i10.Future<_i14.UnPermissionResponseEntity?> getUnPermission() =>
      (super.noSuchMethod(
            Invocation.method(#getUnPermission, []),
            returnValue: _i10.Future<_i14.UnPermissionResponseEntity?>.value(),
          )
          as _i10.Future<_i14.UnPermissionResponseEntity?>);

  @override
  _i10.Future<_i15.TenantInfoResponse?> getTenantInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getTenantInfo, []),
            returnValue: _i10.Future<_i15.TenantInfoResponse?>.value(),
          )
          as _i10.Future<_i15.TenantInfoResponse?>);

  @override
  _i10.Future<List<_i16.SharedRoleModel>> getGroupList(
    String? processDefinitionId,
    String? taskDefKey,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGroupList, [processDefinitionId, taskDefKey]),
            returnValue: _i10.Future<List<_i16.SharedRoleModel>>.value(
              <_i16.SharedRoleModel>[],
            ),
          )
          as _i10.Future<List<_i16.SharedRoleModel>>);
}

/// A class which mocks [LoginSsoUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoUseCase extends _i1.Mock implements _i17.LoginSsoUseCase {
  MockLoginSsoUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.ISsoScanPlugin get ssoScanPlugin =>
      (super.noSuchMethod(
            Invocation.getter(#ssoScanPlugin),
            returnValue: _FakeISsoScanPlugin_3(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
          )
          as _i5.ISsoScanPlugin);

  @override
  _i6.IDeeplinkService get deeplinkService =>
      (super.noSuchMethod(
            Invocation.getter(#deeplinkService),
            returnValue: _FakeIDeeplinkService_4(
              this,
              Invocation.getter(#deeplinkService),
            ),
          )
          as _i6.IDeeplinkService);

  @override
  _i7.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_5(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i7.IStorageUtils);

  @override
  _i8.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_6(this, Invocation.getter(#envHelper)),
          )
          as _i8.IEnvHelper);

  @override
  _i10.Future<dynamic> call(String? pragma) =>
      (super.noSuchMethod(
            Invocation.method(#call, [pragma]),
            returnValue: _i10.Future<dynamic>.value(),
          )
          as _i10.Future<dynamic>);

  @override
  _i10.Future<String?> openScanBarcode() =>
      (super.noSuchMethod(
            Invocation.method(#openScanBarcode, []),
            returnValue: _i10.Future<String?>.value(),
          )
          as _i10.Future<String?>);

  @override
  List<_i18.TenantSsoModel> fromJsonList({required String? jsonString}) =>
      (super.noSuchMethod(
            Invocation.method(#fromJsonList, [], {#jsonString: jsonString}),
            returnValue: <_i18.TenantSsoModel>[],
          )
          as List<_i18.TenantSsoModel>);

  @override
  String processTenants({required List<_i18.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processTenants, [], {#tenants: tenants}),
            returnValue: _i19.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
          )
          as String);

  @override
  String processOrderTenants({required List<_i18.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            returnValue: _i19.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
          )
          as String);
}
