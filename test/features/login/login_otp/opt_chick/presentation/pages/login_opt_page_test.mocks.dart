// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_otp/opt_chick/presentation/pages/login_opt_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i11;
import 'dart:ui' as _i14;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i12;
import 'package:asset_force_mobile_v2/core/services/countdown_timer_service.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i5;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/domain/usecases/opt_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/controllers/login_opt_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/states/login_opt_state.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart'
    as _i3;
import 'package:flutter/cupertino.dart' as _i7;
import 'package:get/get.dart' as _i9;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i13;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeOptUsecase_0 extends _i1.SmartFake implements _i2.OptUsecase {
  _FakeOptUsecase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTenantUseCase_1 extends _i1.SmartFake implements _i3.TenantUseCase {
  _FakeTenantUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_2 extends _i1.SmartFake
    implements _i4.NavigationService {
  _FakeNavigationService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_3 extends _i1.SmartFake implements _i5.DialogService {
  _FakeDialogService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCountdownTimerService_4 extends _i1.SmartFake
    implements _i6.CountdownTimerService {
  _FakeCountdownTimerService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTextEditingController_5 extends _i1.SmartFake
    implements _i7.TextEditingController {
  _FakeTextEditingController_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginOptState_6 extends _i1.SmartFake implements _i8.LoginOptState {
  _FakeLoginOptState_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i9.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginOptController].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginOptController extends _i1.Mock
    implements _i10.LoginOptController {
  @override
  _i2.OptUsecase get optUsecase =>
      (super.noSuchMethod(
            Invocation.getter(#optUsecase),
            returnValue: _FakeOptUsecase_0(
              this,
              Invocation.getter(#optUsecase),
            ),
            returnValueForMissingStub: _FakeOptUsecase_0(
              this,
              Invocation.getter(#optUsecase),
            ),
          )
          as _i2.OptUsecase);

  @override
  _i3.TenantUseCase get tenantUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#tenantUseCase),
            returnValue: _FakeTenantUseCase_1(
              this,
              Invocation.getter(#tenantUseCase),
            ),
            returnValueForMissingStub: _FakeTenantUseCase_1(
              this,
              Invocation.getter(#tenantUseCase),
            ),
          )
          as _i3.TenantUseCase);

  @override
  _i4.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i4.NavigationService);

  @override
  _i5.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_3(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_3(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i5.DialogService);

  @override
  _i6.CountdownTimerService get countdownTimerService =>
      (super.noSuchMethod(
            Invocation.getter(#countdownTimerService),
            returnValue: _FakeCountdownTimerService_4(
              this,
              Invocation.getter(#countdownTimerService),
            ),
            returnValueForMissingStub: _FakeCountdownTimerService_4(
              this,
              Invocation.getter(#countdownTimerService),
            ),
          )
          as _i6.CountdownTimerService);

  @override
  _i7.TextEditingController get controllerNCode =>
      (super.noSuchMethod(
            Invocation.getter(#controllerNCode),
            returnValue: _FakeTextEditingController_5(
              this,
              Invocation.getter(#controllerNCode),
            ),
            returnValueForMissingStub: _FakeTextEditingController_5(
              this,
              Invocation.getter(#controllerNCode),
            ),
          )
          as _i7.TextEditingController);

  @override
  _i8.LoginOptState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeLoginOptState_6(this, Invocation.getter(#state)),
            returnValueForMissingStub: _FakeLoginOptState_6(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i8.LoginOptState);

  @override
  _i9.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  _i9.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void initParams(dynamic param) => super.noSuchMethod(
    Invocation.method(#initParams, [param]),
    returnValueForMissingStub: null,
  );

  @override
  dynamic hiddenPhoneNumber({required String? phoneNo}) => super.noSuchMethod(
    Invocation.method(#hiddenPhoneNumber, [], {#phoneNo: phoneNo}),
    returnValueForMissingStub: null,
  );

  @override
  void goBackPage() => super.noSuchMethod(
    Invocation.method(#goBackPage, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i12.ErrorHandlingMode? mode = _i12.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListener(_i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i14.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListenerId(Object? key, _i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
