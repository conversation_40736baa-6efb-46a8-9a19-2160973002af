// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_otp/opt_chick/domain/usecases/opt_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i7;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetTokenResultModel_0 extends _i1.SmartFake
    implements _i2.GetTokenResultModel {
  _FakeGetTokenResultModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedMyAccountModel_1 extends _i1.SmartFake
    implements _i3.SharedMyAccountModel {
  _FakeSharedMyAccountModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedUserTenantModel_2 extends _i1.SmartFake
    implements _i4.SharedUserTenantModel {
  _FakeSharedUserTenantModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TenantRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTenantRepository extends _i1.Mock implements _i5.TenantRepository {
  MockTenantRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.GetTokenResultModel> loginTenant({
    required String? ticket,
    required String? tenantId,
    required bool? isBiometrics,
    String? code,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginTenant, [], {
              #ticket: ticket,
              #tenantId: tenantId,
              #isBiometrics: isBiometrics,
              #code: code,
            }),
            returnValue: _i6.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#loginTenant, [], {
                  #ticket: ticket,
                  #tenantId: tenantId,
                  #isBiometrics: isBiometrics,
                  #code: code,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GetTokenResultModel>);

  @override
  _i6.Future<_i2.GetTokenResultModel> getFullUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getFullUserInfo, []),
            returnValue: _i6.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getFullUserInfo, []),
              ),
            ),
          )
          as _i6.Future<_i2.GetTokenResultModel>);

  @override
  _i6.Future<_i2.GetTokenResultModel> getVerificationCode({
    required String? ticket,
    required String? countryCode,
    required String? phone,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getVerificationCode, [], {
              #ticket: ticket,
              #countryCode: countryCode,
              #phone: phone,
            }),
            returnValue: _i6.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getVerificationCode, [], {
                  #ticket: ticket,
                  #countryCode: countryCode,
                  #phone: phone,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GetTokenResultModel>);

  @override
  _i6.Future<_i2.GetTokenResultModel> getSendMfaMailVerifyCode({
    required String? ticket,
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getSendMfaMailVerifyCode, [], {
              #ticket: ticket,
              #email: email,
            }),
            returnValue: _i6.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getSendMfaMailVerifyCode, [], {
                  #ticket: ticket,
                  #email: email,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GetTokenResultModel>);

  @override
  _i6.Future<_i2.GetTokenResultModel> getUserBindPhone({
    required String? ticket,
    required String? countryCode,
    required String? phone,
    required String? userName,
    required String? password,
    required String? code,
    required String? tenantId,
    required int? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserBindPhone, [], {
              #ticket: ticket,
              #countryCode: countryCode,
              #phone: phone,
              #userName: userName,
              #password: password,
              #code: code,
              #tenantId: tenantId,
              #userId: userId,
            }),
            returnValue: _i6.Future<_i2.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_0(
                this,
                Invocation.method(#getUserBindPhone, [], {
                  #ticket: ticket,
                  #countryCode: countryCode,
                  #phone: phone,
                  #userName: userName,
                  #password: password,
                  #code: code,
                  #tenantId: tenantId,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GetTokenResultModel>);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i7.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i3.SharedMyAccountModel> getUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getUserInfo, []),
            returnValue: _i6.Future<_i3.SharedMyAccountModel>.value(
              _FakeSharedMyAccountModel_1(
                this,
                Invocation.method(#getUserInfo, []),
              ),
            ),
          )
          as _i6.Future<_i3.SharedMyAccountModel>);

  @override
  _i6.Future<_i4.SharedUserTenantModel> getUserTenant() =>
      (super.noSuchMethod(
            Invocation.method(#getUserTenant, []),
            returnValue: _i6.Future<_i4.SharedUserTenantModel>.value(
              _FakeSharedUserTenantModel_2(
                this,
                Invocation.method(#getUserTenant, []),
              ),
            ),
          )
          as _i6.Future<_i4.SharedUserTenantModel>);

  @override
  _i6.Future<bool> getAuthorityInfo(int? functionId) =>
      (super.noSuchMethod(
            Invocation.method(#getAuthorityInfo, [functionId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<List<_i8.UserRoleResponseData>> getUserRole() =>
      (super.noSuchMethod(
            Invocation.method(#getUserRole, []),
            returnValue: _i6.Future<List<_i8.UserRoleResponseData>>.value(
              <_i8.UserRoleResponseData>[],
            ),
          )
          as _i6.Future<List<_i8.UserRoleResponseData>>);

  @override
  _i6.Future<bool> updateUserInfo(dynamic userInfo) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserInfo, [userInfo]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<List<_i9.SharedUserModel>> getUserList() =>
      (super.noSuchMethod(
            Invocation.method(#getUserList, []),
            returnValue: _i6.Future<List<_i9.SharedUserModel>>.value(
              <_i9.SharedUserModel>[],
            ),
          )
          as _i6.Future<List<_i9.SharedUserModel>>);

  @override
  _i6.Future<_i10.UnPermissionResponseEntity?> getUnPermission() =>
      (super.noSuchMethod(
            Invocation.method(#getUnPermission, []),
            returnValue: _i6.Future<_i10.UnPermissionResponseEntity?>.value(),
          )
          as _i6.Future<_i10.UnPermissionResponseEntity?>);

  @override
  _i6.Future<_i11.TenantInfoResponse?> getTenantInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getTenantInfo, []),
            returnValue: _i6.Future<_i11.TenantInfoResponse?>.value(),
          )
          as _i6.Future<_i11.TenantInfoResponse?>);

  @override
  _i6.Future<List<_i12.SharedRoleModel>> getGroupList(
    String? processDefinitionId,
    String? taskDefKey,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGroupList, [processDefinitionId, taskDefKey]),
            returnValue: _i6.Future<List<_i12.SharedRoleModel>>.value(
              <_i12.SharedRoleModel>[],
            ),
          )
          as _i6.Future<List<_i12.SharedRoleModel>>);
}
