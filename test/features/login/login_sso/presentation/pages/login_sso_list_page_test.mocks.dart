// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_sso/presentation/pages/login_sso_list_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;
import 'dart:ui' as _i13;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i5;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i11;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i3;
import 'package:asset_force_mobile_v2/core/storage/i_storage_utils.dart' as _i6;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_list_controller.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart'
    as _i9;
import 'package:get/get.dart' as _i7;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i12;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginSsoUseCase_0 extends _i1.SmartFake
    implements _i2.LoginSsoUseCase {
  _FakeLoginSsoUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_1 extends _i1.SmartFake
    implements _i3.NavigationService {
  _FakeNavigationService_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_2 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_3 extends _i1.SmartFake implements _i5.IEnvHelper {
  _FakeIEnvHelper_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_4 extends _i1.SmartFake implements _i6.IStorageUtils {
  _FakeIStorageUtils_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxList_5<E> extends _i1.SmartFake implements _i7.RxList<E> {
  _FakeRxList_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_6 extends _i1.SmartFake implements _i7.RxBool {
  _FakeRxBool_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i7.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginSsoListController].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoListController extends _i1.Mock
    implements _i8.LoginSsoListController {
  @override
  _i2.LoginSsoUseCase get loginSsoUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#loginSsoUseCase),
            returnValue: _FakeLoginSsoUseCase_0(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
            returnValueForMissingStub: _FakeLoginSsoUseCase_0(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
          )
          as _i2.LoginSsoUseCase);

  @override
  _i3.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i3.NavigationService);

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  _i5.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_3(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_3(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i5.IEnvHelper);

  @override
  _i6.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i6.IStorageUtils);

  @override
  _i7.RxList<_i9.TenantSsoModel> get ssoItemList =>
      (super.noSuchMethod(
            Invocation.getter(#ssoItemList),
            returnValue: _FakeRxList_5<_i9.TenantSsoModel>(
              this,
              Invocation.getter(#ssoItemList),
            ),
            returnValueForMissingStub: _FakeRxList_5<_i9.TenantSsoModel>(
              this,
              Invocation.getter(#ssoItemList),
            ),
          )
          as _i7.RxList<_i9.TenantSsoModel>);

  @override
  set ssoItemList(_i7.RxList<_i9.TenantSsoModel>? _ssoItemList) =>
      super.noSuchMethod(
        Invocation.setter(#ssoItemList, _ssoItemList),
        returnValueForMissingStub: null,
      );

  @override
  _i7.RxBool get isSsoListEdit =>
      (super.noSuchMethod(
            Invocation.getter(#isSsoListEdit),
            returnValue: _FakeRxBool_6(this, Invocation.getter(#isSsoListEdit)),
            returnValueForMissingStub: _FakeRxBool_6(
              this,
              Invocation.getter(#isSsoListEdit),
            ),
          )
          as _i7.RxBool);

  @override
  set isSsoListEdit(_i7.RxBool? _isSsoListEdit) => super.noSuchMethod(
    Invocation.setter(#isSsoListEdit, _isSsoListEdit),
    returnValueForMissingStub: null,
  );

  @override
  _i7.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  _i7.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void ssoListDeleteOnClick({required _i9.TenantSsoModel? ssoItem}) =>
      super.noSuchMethod(
        Invocation.method(#ssoListDeleteOnClick, [], {#ssoItem: ssoItem}),
        returnValueForMissingStub: null,
      );

  @override
  void ssoListAddOnClick() => super.noSuchMethod(
    Invocation.method(#ssoListAddOnClick, []),
    returnValueForMissingStub: null,
  );

  @override
  void ssoListGoToWebViewOnClick({required _i9.TenantSsoModel? ssoItem}) =>
      super.noSuchMethod(
        Invocation.method(#ssoListGoToWebViewOnClick, [], {#ssoItem: ssoItem}),
        returnValueForMissingStub: null,
      );

  @override
  _i10.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i11.ErrorHandlingMode? mode = _i11.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Disposer addListener(_i12.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i12.Disposer);

  @override
  void removeListener(_i13.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i13.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Disposer addListenerId(Object? key, _i12.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i12.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
