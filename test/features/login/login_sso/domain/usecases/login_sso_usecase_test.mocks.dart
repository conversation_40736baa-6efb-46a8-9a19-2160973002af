// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_sso/domain/usecases/login_sso_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:asset_force_mobile_v2/core/deeplink/deeplink_service.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/deeplink/handler/link_handler_factory.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/skill_plugin/sso_scan_plugin.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeOpenBarcodeScantModel_0 extends _i1.SmartFake
    implements _i2.OpenBarcodeScantModel {
  _FakeOpenBarcodeScantModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInvokeBackendUrlModel_1 extends _i1.SmartFake
    implements _i2.InvokeBackendUrlModel {
  _FakeInvokeBackendUrlModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLinkHandlerFactory_2 extends _i1.SmartFake
    implements _i3.LinkHandlerFactory {
  _FakeLinkHandlerFactory_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SsoScanPlugin].
///
/// See the documentation for Mockito's code generation for more information.
class MockSsoScanPlugin extends _i1.Mock implements _i2.SsoScanPlugin {
  MockSsoScanPlugin() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get skillName =>
      (super.noSuchMethod(
            Invocation.getter(#skillName),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#skillName),
            ),
          )
          as String);

  @override
  _i5.Future<_i2.OpenBarcodeScantModel> openBarcodeScan() =>
      (super.noSuchMethod(
            Invocation.method(#openBarcodeScan, []),
            returnValue: _i5.Future<_i2.OpenBarcodeScantModel>.value(
              _FakeOpenBarcodeScantModel_0(
                this,
                Invocation.method(#openBarcodeScan, []),
              ),
            ),
          )
          as _i5.Future<_i2.OpenBarcodeScantModel>);

  @override
  _i5.Future<_i2.InvokeBackendUrlModel> toSsoSetting({
    required Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#toSsoSetting, [], {#data: data}),
            returnValue: _i5.Future<_i2.InvokeBackendUrlModel>.value(
              _FakeInvokeBackendUrlModel_1(
                this,
                Invocation.method(#toSsoSetting, [], {#data: data}),
              ),
            ),
          )
          as _i5.Future<_i2.InvokeBackendUrlModel>);

  @override
  _i5.Future<void> toClearSsoLoginCache() =>
      (super.noSuchMethod(
            Invocation.method(#toClearSsoLoginCache, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> invokeChannelMethod<T>({
    required String? method,
    bool? isSetBaseData = true,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invokeChannelMethod, [], {
              #method: method,
              #isSetBaseData: isSetBaseData,
              #data: data,
            }),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);
}

/// A class which mocks [DeeplinkService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDeeplinkService extends _i1.Mock implements _i6.DeeplinkService {
  MockDeeplinkService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.LinkHandlerFactory get linkHandlerFactory =>
      (super.noSuchMethod(
            Invocation.getter(#linkHandlerFactory),
            returnValue: _FakeLinkHandlerFactory_2(
              this,
              Invocation.getter(#linkHandlerFactory),
            ),
          )
          as _i3.LinkHandlerFactory);

  @override
  set linkHandlerFactory(_i3.LinkHandlerFactory? _linkHandlerFactory) =>
      super.noSuchMethod(
        Invocation.setter(#linkHandlerFactory, _linkHandlerFactory),
        returnValueForMissingStub: null,
      );

  @override
  void init() => super.noSuchMethod(
    Invocation.method(#init, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> processLink(Uri? uri) =>
      (super.noSuchMethod(
            Invocation.method(#processLink, [uri]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [CommonDialog].
///
/// See the documentation for Mockito's code generation for more information.
class MockCommonDialog extends _i1.Mock implements _i7.CommonDialog {
  MockCommonDialog() {
    _i1.throwOnMissingStub(this);
  }
}
