class AssetListMockData {
  /// load_data_usecase_test.dart
  static Map<String, Object> assetMobileResponse = {
    'code': 0,
    'msg': '成功',
    'assetItemList': [
      {
        'itemId': 3,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'assetName',
        'itemDisplayName': '資産名',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option': '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '1',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:13:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 2,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'identityCode',
        'itemDisplayName': '識別コード',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            '{"readonly": "1", "automatic": true, "maxlength": "30", "ruleUseFlg": "0", "resetTiming": "", "sectionPrivateGroups": "", "autoNumberingRuleList": [], "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:13:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 5,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'createdDate',
        'itemDisplayName': '登録日',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option': '{"dateType": "date", "readonly": "0", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': 'today',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 1,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:13:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 6,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'updatedDate',
        'itemDisplayName': '更新日',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option': '{"dateType": "date", "readonly": "1", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 1,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:13:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492679,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '1行テキスト666',
        'itemDisplayName': '1行テキスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 2,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:16:25',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 7,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'passedTime',
        'itemDisplayName': '経過日数',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'number',
        'option':
            '{"readonly": "0", "numberDecimalPoint": "0", "sectionPrivateGroups": "", "numberCommaDecimalPoint": "0", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 2,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:13:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 1,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'location',
        'itemDisplayName': '場所',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'list',
        'option':
            "{\"data\": [\"東京\", \"火星\", \"地球\", \"長城\", \"御屋\", \"レストラン\", \"マーケット\", \"喫茶店\", \"地下鉄\"], \"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 3,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-10-24 14:50:34',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492680,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '日付/時間666',
        'itemDisplayName': '日付/時間888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"dateTime\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 4,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:16:25',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492687,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'RFID',
        'itemDisplayName': 'RFID888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'rfid',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 5,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-19 16:00:27',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492689,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'ナンバープレート',
        'itemDisplayName': 'ナンバープレート888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 6,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-22 11:26:37',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493079,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '数字',
        'itemDisplayName': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'number',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"numberCommaDecimalPoint\": \"3\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '1234.1567',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 7,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-12-19 16:47:08',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493080,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '通貨',
        'itemDisplayName': '通貨_小数点桁数12',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'currency',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"currencyType\": \"JPY(￥)\", \"currencyDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '11234.12345',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 8,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-12-19 16:47:08',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493081,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '計算',
        'itemDisplayName': '計算_小数点桁数12',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'calculate',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`数字`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 9,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-12-19 16:47:08',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493082,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'FXH_Master_All',
        'itemDisplayName': 'FXH_Master_All',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'master',
        'option':
            "{\"masterDisplayItems\":[{\"itemDisplayName\":\"数字_小数点桁数12\",\"itemId\":962060,\"itemName\":\"数字666\",\"itemType\":\"number\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"12\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\"},{\"itemDisplayName\":\"チェックボックス888\",\"itemId\":962057,\"itemName\":\"チェックボックス666\",\"itemType\":\"checkbox\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\"},{\"itemDisplayName\":\"チェックボックス\",\"itemId\":962521,\"itemName\":\"チェックボックス\",\"itemType\":\"checkbox\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\"}],\"readonly\":\"0\",\"sectionPrivateGroups\":\"\",\"sectionPrivateEditGroups\":\"\",\"check\":\"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\",\"masterTypeId\":124043,\"masterChainFlg\":\"0\"}",
        'defaultData': '7794626',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 10,
        'width': 2,
        'height': 3,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-12-19 16:55:38',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492520,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'ファイル',
        'itemDisplayName': 'ファイル',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'file',
        'option': '{}',
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': 'ファイル',
        'sectionSort': 2.0,
        'positionX': 0,
        'positionY': 0,
        'width': 0,
        'height': 0,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:09',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492524,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'FXH_履歴情報',
        'itemDisplayName': 'FXH_履歴情報',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'appurtenancesInfo',
        'option':
            '{"check": "", "readonly": "0", "appurtenancesInformationTypeId": 13844, "appurtenancesInformationDisplayItemList": [{"itemId": 1, "itemName": "ユーザー名", "subItemId": 0, "subItemName": ""}, {"itemId": 2, "itemName": "新規登録時間", "subItemId": 0, "subItemName": ""}, {"itemId": 4, "itemName": "更新時間", "subItemId": 0, "subItemName": ""}, {"itemId": 3, "itemName": "ロケーション", "subItemId": 0, "subItemName": ""}]}',
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': 'FXH_履歴情報',
        'sectionSort': 3.0,
        'positionX': 0,
        'positionY': 0,
        'width': 0,
        'height': 0,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:15:26',
        'modifiedById': '1044758',
        'modifiedDate': '2025-03-03 15:53:42',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111855,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      }
    ],
    'assetMobileSettingForMobile': {
      'arInfo': {
        'category5ItemId': null,
        'category5ItemName': null,
        'category5ItemType': null,
        'category5ItemOption': null,
        'category5SubItemId': null,
        'category5SubItemName': null,
        'category5SubItemType': null,
        'category5SubItemOption': null,
        'category4ItemId': 12493082,
        'category4ItemName': 'FXH_Master_All',
        'category4ItemType': 'master',
        'category4ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"masterTypeId\": 124043, \"masterChainFlg\": \"0\", \"masterDisplayItems\": [{\"itemId\": 962060, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"12\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\", \"itemName\": \"数字666\", \"itemType\": \"number\"}, {\"itemId\": 962057, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス666\", \"itemType\": \"checkbox\"}, {\"itemId\": 962521, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス\", \"itemType\": \"checkbox\"}], \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'category4SubItemId': 962060,
        'category4SubItemName': '数字666',
        'category4SubItemType': 'number',
        'category4SubItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"12\", \"numberCommaDecimalPoint\": \"3\"}",
        'category3ItemId': 12493081,
        'category3ItemName': '計算',
        'category3ItemType': 'calculate',
        'category3ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`数字`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'category3SubItemId': null,
        'category3SubItemName': null,
        'category3SubItemType': null,
        'category3SubItemOption': null,
        'category2ItemId': 12493080,
        'category2ItemName': '通貨',
        'category2ItemType': 'currency',
        'category2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"currencyType\": \"JPY(￥)\", \"currencyDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'category2SubItemId': null,
        'category2SubItemName': null,
        'category2SubItemType': null,
        'category2SubItemOption': null,
        'category1ItemId': 12493079,
        'category1ItemName': '数字',
        'category1ItemType': 'number',
        'category1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"numberCommaDecimalPoint\": \"3\", \"sectionPrivateEditGroups\": \"\"}",
        'category1SubItemId': null,
        'category1SubItemName': null,
        'category1SubItemType': null,
        'category1SubItemOption': null,
        'arLevel3BgColor': null,
        'arLevel1ItemId': 3,
        'arLevel2ItemId': null,
        'arLevel3ItemId': null,
        'arLevel1ItemName': 'assetName',
        'arLevel2ItemName': null,
        'arLevel3ItemName': null,
        'arLevel1ItemType': 'input',
        'arLevel2ItemType': null,
        'arLevel3ItemType': null,
        'arLevel1ItemOption':
            '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'arLevel2ItemOption': null,
        'arLevel3ItemOption': null,
        'arLevel1SubItemId': null,
        'arLevel2SubItemId': null,
        'arLevel3SubItemId': null
      },
      'assetInfo': {
        'assetLevel1ItemId': 12493079,
        'assetLevel2ItemId': 12493080,
        'assetLevel3ItemId': 12493081,
        'assetLevel4ItemId': 12493082,
        'assetLevel5ItemId': 12493082,
        'assetLevel1SubItemId': null,
        'assetLevel2SubItemId': null,
        'assetLevel3SubItemId': null,
        'assetLevel4SubItemId': 962060,
        'assetLevel5SubItemId': 962057,
        'assetLevel1ItemType': 'number',
        'assetLevel2ItemType': 'currency',
        'assetLevel3ItemType': 'calculate',
        'assetLevel4ItemType': 'master',
        'assetLevel5ItemType': 'master',
        'assetLevel1ItemName': '数字',
        'assetLevel2ItemName': '通貨',
        'assetLevel3ItemName': '計算',
        'assetLevel4ItemName': 'FXH_Master_All',
        'assetLevel5ItemName': 'FXH_Master_All',
        'assetLevel1ItemDisplayName': null,
        'assetLevel2ItemDisplayName': null,
        'assetLevel3ItemDisplayName': null,
        'assetLevel4ItemDisplayName': null,
        'assetLevel5ItemDisplayName': null,
        'assetLevel1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"numberCommaDecimalPoint\": \"3\", \"sectionPrivateEditGroups\": \"\"}",
        'assetLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"currencyType\": \"JPY(￥)\", \"currencyDecimalPoint\": \"12\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'assetLevel3ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`数字`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel4ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"masterTypeId\": 124043, \"masterChainFlg\": \"0\", \"masterDisplayItems\": [{\"itemId\": 962060, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"12\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\", \"itemName\": \"数字666\", \"itemType\": \"number\"}, {\"itemId\": 962057, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス666\", \"itemType\": \"checkbox\"}, {\"itemId\": 962521, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス\", \"itemType\": \"checkbox\"}], \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'assetLevel5ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"masterTypeId\": 124043, \"masterChainFlg\": \"0\", \"masterDisplayItems\": [{\"itemId\": 962060, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"12\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\", \"itemName\": \"数字666\", \"itemType\": \"number\"}, {\"itemId\": 962057, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス666\", \"itemType\": \"checkbox\"}, {\"itemId\": 962521, \"option\": \"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\", \"itemName\": \"チェックボックス\", \"itemType\": \"checkbox\"}], \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}"
      }
    }
  };

  static Map<String, Object> layoutSettingResults = {
    'code': 0,
    'msg': '成功',
    'layoutSettingList': [
      {
        'itemId': 962054,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': '1行テキスト666',
        'itemDisplayName': '1行テキスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 0,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962055,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': '複数行テキスト666',
        'itemDisplayName': '複数行テキスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'textarea',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"10000\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 1,
        'width': 4,
        'height': 3,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962056,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': 'リスト666',
        'itemDisplayName': 'リスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'list',
        'option':
            "{\"data\": [\"ZA\", \"AZ\"], \"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 4,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962057,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': 'チェックボックス666',
        'itemDisplayName': 'チェックボックス888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'checkbox',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"checkboxOptions\": [], \"checkboxMultiFlg\": \"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 5,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962058,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': 'オプションボタン666',
        'itemDisplayName': 'オプションボタン888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'radio',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"radioOptions\": [\"555\", \"666\"]}",
        'defaultData': '555',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 6,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962059,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': '日付/時間666',
        'itemDisplayName': '日付/時間888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 7,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962060,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': '数字666',
        'itemDisplayName': '数字_小数点桁数12',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'number',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"12\", \"numberCommaDecimalPoint\": \"3\"}",
        'defaultData': '1234.112233',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 8,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 962061,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': 4,
        'typeId': 124043,
        'itemName': 'メールアドレス666',
        'itemDisplayName': 'メールアドレス888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'email',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 9,
        'width': 4,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:01:21',
        'modifiedById': '1044758',
        'modifiedDate': '2024-12-19 16:54:30',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': null,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      }
    ],
    'masterDeletedItemNames': []
  };

  static Map<String, Object> layoutSettingResultsIsNUll = {
    'code': 0,
    'msg': '成功',
    'layoutSettingList': [],
    'masterDeletedItemNames': []
  };

  static Map<String, Object> searchResults = {
    'code': 0,
    'msg': '成功',
    'sumAssetCount': 20,
    'searchAssetCount': 20,
    'data': [
      {
        'assetId': 1622791192,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"RFID": "", "数字": "-10000.00001", "計算": -9999.00001, "通貨": "1234.008900", "location": "東京", "talkList": "", "assetName": "FXH247", "passedTime": 171, "createdDate": "2024/07/19 10:38", "updatedDate": "2025/01/06 16:48", "identityCode": "w52228", "ファイル": [{"url": "9900004/2024/11/28/2024112811555739132066a12e243488ba9a50409c794805b.png", "size": 3126933, "fileName": "Screenshot_20241121-172417.png", "directory": "", "uploadDate": "2024/11/28 10:56", "uploadUserName": "李 暁琳"}], "資産種類": "FXH_個体管理", "FXH_Master_All": {"display": {"962060": "1231.4561237"}, "masterId": 7794626}, "日付/時間666": "2024/11/28 10:55", "1行テキスト666": "oo", "ナンバープレート": ""}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2025-01-06 16:48:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': 'w52228',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791191,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH246", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052227", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052227',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791190,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH245", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052226", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052226',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791189,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH244", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052225", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052225',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791188,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH243", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052224", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052224',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791187,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH242", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052223", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052223',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791186,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH241", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052222", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052222',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791185,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH240", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052221", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052221',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791184,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH239", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052220", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052220',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791183,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH238", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052219", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052219',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791182,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH237", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052218", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052218',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791181,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH236", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052217", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052217',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791180,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH235", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052216", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052216',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791179,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH234", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052215", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052215',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791178,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH233", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052214", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052214',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791177,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH232", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052213", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052213',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791176,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH231", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052212", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052212',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791175,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH230", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052211", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052211',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791174,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH229", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052210", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052210',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
      {
        'assetId': 1622791173,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"計算": "", "location": "", "assetName": "FXH228", "createdDate": "2024/07/19 10:38", "updatedDate": "2024/07/19 10:38", "identityCode": "99000000052209", "資産種類": "FXH_個体管理"}',
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2024-07-19 10:38:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': '99000000052209',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
    ],
    'moreThenLimit': true
  };

  static Map<String, Object> searchOneResult = {
    'code': 0,
    'msg': '成功',
    'sumAssetCount': 1,
    'searchAssetCount': 1,
    'data': [
      {
        'assetId': 1622791192,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText': null,
        'assetTextObj': null,
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2025-01-06 16:48:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': 'w52228',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null,
        'mobielDisplayList': null
      },
    ],
    'moreThenLimit': true
  };

  static Map<String, Object> assetMobileResponse2 = {
    'code': 0,
    'msg': '成功',
    'assetItemList': [
      {
        'itemId': 3,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'assetName',
        'itemDisplayName': '資産名',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option': '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '1',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 2,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'identityCode',
        'itemDisplayName': '識別コード',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            '{"readonly": "1", "automatic": true, "maxlength": "30", "ruleUseFlg": "0", "resetTiming": "", "sectionPrivateGroups": "", "autoNumberingRuleList": [], "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 5,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'createdDate',
        'itemDisplayName': '登録日',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option': '{"dateType": "date", "readonly": "0", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': 'today',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 1,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 7,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'passedTime',
        'itemDisplayName': '経過日数',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'number',
        'option':
            '{"readonly": "0", "numberDecimalPoint": "0", "sectionPrivateGroups": "", "numberCommaDecimalPoint": "0", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 2,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 1,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'location',
        'itemDisplayName': '場所',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'list',
        'option':
            "{\"data\": [\"東京\", \"火星\", \"地球\", \"長城\", \"御屋\", \"レストラン\", \"マーケット\", \"喫茶店\", \"地下鉄\"], \"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 3,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-10-24 14:50:34',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492648,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '1行テキスト666',
        'itemDisplayName': '1行テキスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 4,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492649,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'ラベル',
        'itemDisplayName': 'ラベル888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'label',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"color\": \"#000000\", \"fontSize\": \"14px\", \"readonly\": \"0\", \"fontStyle\": \"bodyStyle\", \"fontWeight\": \"normal\", \"lineHeight\": \"150%\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': 'VVV',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 5,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492650,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '複数行テキスト666',
        'itemDisplayName': '複数行テキスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'textarea',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"10000\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 6,
        'width': 2,
        'height': 3,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492651,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'リスト666',
        'itemDisplayName': 'リスト888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'list',
        'option':
            "{\"data\": [\"1\", \"a\"], \"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 9,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492652,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'チェックボックス666',
        'itemDisplayName': 'チェックボックス888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'checkbox',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"checkboxOptions\": [], \"checkboxMultiFlg\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 10,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492653,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'オプションボタン666',
        'itemDisplayName': 'オプションボタン888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'radio',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"radioOptions\": [\"OO\", \"QQ\"], \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': 'OO',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 11,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492654,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '日付/時間666',
        'itemDisplayName': '日付/時間888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 12,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492655,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '数字666',
        'itemDisplayName': '数字888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'number',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"percentage\": \"0\", \"numberDecimalPoint\": \"8\", \"sectionPrivateGroups\": \"\", \"numberCommaDecimalPoint\": \"0\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '12354.1234',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 13,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492656,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '通貨666',
        'itemDisplayName': '通貨888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'currency',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"currencyType\": \"JPY(￥)\", \"currencyDecimalPoint\": \"8\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '1234.1234',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 14,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492657,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'メールアドレス666',
        'itemDisplayName': 'メールアドレス888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'email',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 15,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492658,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '画像666',
        'itemDisplayName': '画像888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'image',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 16,
        'width': 2,
        'height': 2,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492660,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '担当者666',
        'itemDisplayName': '担当者888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'userSelect',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 18,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492661,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '計算666',
        'itemDisplayName': '計算888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'calculate',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+`quantity`\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 19,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492663,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'サブフォーム666',
        'itemDisplayName': 'サブフォーム888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'subForm',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '0',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 20,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492664,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'ハイパーリンク666',
        'itemDisplayName': 'ハイパーリンク888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'hyperlink',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 21,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-16 11:14:54',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492684,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'FXH_Master_All',
        'itemDisplayName': 'FXH_Master_All',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'master',
        'option':
            "{\"masterDisplayItems\":[{\"itemDisplayName\":\"1行テキスト888\",\"itemId\":962054,\"itemName\":\"1行テキスト666\",\"itemType\":\"input\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"maxlength\\\": \\\"255\\\"}\"},{\"itemDisplayName\":\"複数行テキスト888\",\"itemId\":962055,\"itemName\":\"複数行テキスト666\",\"itemType\":\"textarea\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"maxlength\\\": \\\"10000\\\"}\"},{\"itemDisplayName\":\"リスト888\",\"itemId\":962056,\"itemName\":\"リスト666\",\"itemType\":\"list\",\"option\":\"{\\\"data\\\": [\\\"ZA\\\", \\\"AZ\\\"], \\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\"}\"},{\"itemDisplayName\":\"チェックボックス888\",\"itemId\":962057,\"itemName\":\"チェックボックス666\",\"itemType\":\"checkbox\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"checkboxOptions\\\": [], \\\"checkboxMultiFlg\\\": \\\"0\\\"}\"},{\"itemDisplayName\":\"オプションボタン888\",\"itemId\":962058,\"itemName\":\"オプションボタン666\",\"itemType\":\"radio\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\", \\\"radioOptions\\\": [\\\"555\\\", \\\"666\\\"]}\"},{\"itemDisplayName\":\"日付/時間888\",\"itemId\":962059,\"itemName\":\"日付/時間666\",\"itemType\":\"date\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"dateType\\\": \\\"date\\\", \\\"readonly\\\": \\\"0\\\"}\"},{\"itemDisplayName\":\"数字_小数点桁数12\",\"itemId\":962060,\"itemName\":\"数字666\",\"itemType\":\"number\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"0\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\"},{\"itemDisplayName\":\"メールアドレス888\",\"itemId\":962061,\"itemName\":\"メールアドレス666\",\"itemType\":\"email\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"readonly\\\": \\\"0\\\"}\"}],\"readonly\":\"0\",\"sectionPrivateGroups\":\"\",\"sectionPrivateEditGroups\":\"\",\"check\":\"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\",\"masterTypeId\":124043,\"masterChainFlg\":\"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 22,
        'width': 2,
        'height': 7,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-19 15:46:11',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492685,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '履歴情報合計666',
        'itemDisplayName': '履歴情報合計888',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'appurInfoSummary',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 29,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-19 15:46:11',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492688,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'FXH_Master0225',
        'itemDisplayName': 'FXH_Master0288',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'master',
        'option':
            "{\"masterDisplayItems\":[{\"itemDisplayName\":\"数字888\",\"itemId\":962099,\"itemName\":\"数字666\",\"itemType\":\"number\",\"option\":\"{\\\"check\\\": \\\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証コード\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:''\\\\n      }\\\\n    };\\\\n  };\\\", \\\"unitFlg\\\": \\\"0\\\", \\\"readonly\\\": \\\"0\\\", \\\"percentage\\\": \\\"0\\\", \\\"numberDecimalPoint\\\": \\\"11\\\", \\\"numberCommaDecimalPoint\\\": \\\"3\\\"}\"}],\"readonly\":\"0\",\"sectionPrivateGroups\":\"\",\"sectionPrivateEditGroups\":\"\",\"check\":\"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\",\"masterTypeId\":124045,\"masterChainFlg\":\"0\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 30,
        'width': 2,
        'height': 2,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-08-20 17:59:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493078,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '日付/時間',
        'itemDisplayName': '日付/時間',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'date',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"dateTime\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 32,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044755',
        'createdDate': '2024-12-19 13:12:16',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493085,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '計算通货结果',
        'itemDisplayName': '計算通货结果',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'calculate',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"calculate\": \"`数字666`+`passedTime`\", \"calculateType\": \"currency\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": 0, \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": 3}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 35,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044755',
        'createdDate': '2024-12-20 11:56:47',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493090,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '計算经过日数+1',
        'itemDisplayName': '計算经过日数+1',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'calculate',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": 0, \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 39,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044755',
        'createdDate': '2024-12-20 13:09:06',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12493093,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': '1行テキスト',
        'itemDisplayName': '1行テキスト',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 41,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044755',
        'createdDate': '2024-12-20 13:30:06',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492522,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'ファイル',
        'itemDisplayName': 'ファイル',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'file',
        'option': '{}',
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': 'ファイル',
        'sectionSort': 2.0,
        'positionX': 0,
        'positionY': 0,
        'width': 0,
        'height': 0,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:55',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 12492523,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'FXH_履歴情報',
        'itemDisplayName': 'FXH_履歴情報',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'appurtenancesInfo',
        'option':
            '{"check": "", "readonly": "0", "appurtenancesInformationTypeId": 13844, "appurtenancesInformationDisplayItemList": [{"itemId": 1, "itemName": "ユーザー名", "subItemId": 0, "subItemName": ""}, {"itemId": 2, "itemName": "新規登録時間", "subItemId": 0, "subItemName": ""}, {"itemId": 4, "itemName": "更新時間", "subItemId": 0, "subItemName": ""}, {"itemId": 3, "itemName": "ロケーション", "subItemId": 0, "subItemName": ""}]}',
        'defaultData': '',
        'inputFlg': '',
        'uneditableFlg': null,
        'mobileFlg': '1',
        'sectionName': 'FXH_履歴情報',
        'sectionSort': 3.0,
        'positionX': 0,
        'positionY': 0,
        'width': 0,
        'height': 0,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:55',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
    ],
    'assetMobileSettingForMobile': {
      'arInfo': {
        'category5ItemId': null,
        'category5ItemName': null,
        'category5ItemType': null,
        'category5ItemOption': null,
        'category5SubItemId': null,
        'category5SubItemName': null,
        'category5SubItemType': null,
        'category5SubItemOption': null,
        'category4ItemId': null,
        'category4ItemName': null,
        'category4ItemType': null,
        'category4ItemOption': null,
        'category4SubItemId': null,
        'category4SubItemName': null,
        'category4SubItemType': null,
        'category4SubItemOption': null,
        'category3ItemId': null,
        'category3ItemName': null,
        'category3ItemType': null,
        'category3ItemOption': null,
        'category3SubItemId': null,
        'category3SubItemName': null,
        'category3SubItemType': null,
        'category3SubItemOption': null,
        'category2ItemId': 12492654,
        'category2ItemName': '日付/時間666',
        'category2ItemType': 'date',
        'category2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'category2SubItemId': null,
        'category2SubItemName': null,
        'category2SubItemType': null,
        'category2SubItemOption': null,
        'category1ItemId': 12492685,
        'category1ItemName': '履歴情報合計666',
        'category1ItemType': 'appurInfoSummary',
        'category1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'category1SubItemId': null,
        'category1SubItemName': null,
        'category1SubItemType': null,
        'category1SubItemOption': null,
        'arLevel3BgColor': null,
        'arLevel1ItemId': 12492685,
        'arLevel2ItemId': 12492654,
        'arLevel3ItemId': null,
        'arLevel1ItemName': '履歴情報合計666',
        'arLevel2ItemName': '日付/時間666',
        'arLevel3ItemName': null,
        'arLevel1ItemType': 'appurInfoSummary',
        'arLevel2ItemType': 'date',
        'arLevel3ItemType': null,
        'arLevel1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'arLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'arLevel3ItemOption': null,
        'arLevel1SubItemId': null,
        'arLevel2SubItemId': null,
        'arLevel3SubItemId': null
      },
      'assetInfo': {
        'assetLevel1ItemId': 3,
        'assetLevel2ItemId': 12492661,
        'assetLevel3ItemId': 12493090,
        'assetLevel4ItemId': 12492685,
        'assetLevel5ItemId': 12493078,
        'assetLevel1SubItemId': null,
        'assetLevel2SubItemId': null,
        'assetLevel3SubItemId': null,
        'assetLevel4SubItemId': null,
        'assetLevel5SubItemId': null,
        'assetLevel1ItemType': 'input',
        'assetLevel2ItemType': 'calculate',
        'assetLevel3ItemType': 'calculate',
        'assetLevel4ItemType': 'appurInfoSummary',
        'assetLevel5ItemType': 'date',
        'assetLevel1ItemName': 'assetName',
        'assetLevel2ItemName': '計算666',
        'assetLevel3ItemName': '計算经过日数+1',
        'assetLevel4ItemName': '履歴情報合計666',
        'assetLevel5ItemName': '日付/時間',
        'assetLevel1ItemDisplayName': null,
        'assetLevel2ItemDisplayName': null,
        'assetLevel3ItemDisplayName': null,
        'assetLevel4ItemDisplayName': null,
        'assetLevel5ItemDisplayName': null,
        'assetLevel1ItemOption':
            '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'assetLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+`quantity`\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel3ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": 0, \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel4ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'assetLevel5ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"dateTime\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}"
      }
    }
  };

  static Map<String, Object> assetMobileResponse3 = {
    'code': 0,
    'msg': '成功',
    'assetItemList': [
      {
        'itemId': 3,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'assetName',
        'itemDisplayName': '資産名',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option': '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '1',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 2,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'identityCode',
        'itemDisplayName': '識別コード',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            '{"readonly": "1", "automatic": true, "maxlength": "30", "ruleUseFlg": "0", "resetTiming": "", "sectionPrivateGroups": "", "autoNumberingRuleList": [], "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
    ],
    'assetMobileSettingForMobile': {
      'arInfo': {
        'category5ItemId': null,
        'category5ItemName': null,
        'category5ItemType': null,
        'category5ItemOption': null,
        'category5SubItemId': null,
        'category5SubItemName': null,
        'category5SubItemType': null,
        'category5SubItemOption': null,
        'category4ItemId': null,
        'category4ItemName': null,
        'category4ItemType': null,
        'category4ItemOption': null,
        'category4SubItemId': null,
        'category4SubItemName': null,
        'category4SubItemType': null,
        'category4SubItemOption': null,
        'category3ItemId': null,
        'category3ItemName': null,
        'category3ItemType': null,
        'category3ItemOption': null,
        'category3SubItemId': null,
        'category3SubItemName': null,
        'category3SubItemType': null,
        'category3SubItemOption': null,
        'category2ItemId': 12492654,
        'category2ItemName': '日付/時間666',
        'category2ItemType': 'date',
        'category2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'category2SubItemId': null,
        'category2SubItemName': null,
        'category2SubItemType': null,
        'category2SubItemOption': null,
        'category1ItemId': 12492685,
        'category1ItemName': '履歴情報合計666',
        'category1ItemType': 'appurInfoSummary',
        'category1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'category1SubItemId': null,
        'category1SubItemName': null,
        'category1SubItemType': null,
        'category1SubItemOption': null,
        'arLevel3BgColor': null,
        'arLevel1ItemId': 12492685,
        'arLevel2ItemId': 12492654,
        'arLevel3ItemId': null,
        'arLevel1ItemName': '履歴情報合計666',
        'arLevel2ItemName': '日付/時間666',
        'arLevel3ItemName': null,
        'arLevel1ItemType': 'appurInfoSummary',
        'arLevel2ItemType': 'date',
        'arLevel3ItemType': null,
        'arLevel1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'arLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'arLevel3ItemOption': null,
        'arLevel1SubItemId': null,
        'arLevel2SubItemId': null,
        'arLevel3SubItemId': null
      },
      'assetInfo': {
        'assetLevel1ItemId': 0,
        'assetLevel2ItemId': 12492661,
        'assetLevel3ItemId': 12493090,
        'assetLevel4ItemId': 12492685,
        'assetLevel5ItemId': 12493078,
        'assetLevel1SubItemId': null,
        'assetLevel2SubItemId': null,
        'assetLevel3SubItemId': null,
        'assetLevel4SubItemId': null,
        'assetLevel5SubItemId': null,
        'assetLevel1ItemType': 'input',
        'assetLevel2ItemType': 'calculate',
        'assetLevel3ItemType': 'calculate',
        'assetLevel4ItemType': 'appurInfoSummary',
        'assetLevel5ItemType': 'date',
        'assetLevel1ItemName': 'assetName',
        'assetLevel2ItemName': '計算666',
        'assetLevel3ItemName': '計算经过日数+1',
        'assetLevel4ItemName': '履歴情報合計666',
        'assetLevel5ItemName': '日付/時間',
        'assetLevel1ItemDisplayName': null,
        'assetLevel2ItemDisplayName': null,
        'assetLevel3ItemDisplayName': null,
        'assetLevel4ItemDisplayName': null,
        'assetLevel5ItemDisplayName': null,
        'assetLevel1ItemOption':
            '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'assetLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+`quantity`\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel3ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": 0, \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel4ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'assetLevel5ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"dateTime\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}"
      }
    }
  };

  static Map<String, Object> assetMobileResponse4 = {
    'code': 0,
    'msg': '成功',
    'assetItemList': [
      {
        'itemId': 3,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'assetName',
        'itemDisplayName': '資産名',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option': '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '1',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 0,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': null,
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
      {
        'itemId': 2,
        'subItemId': null,
        'tenantId': '9900004',
        'classification': null,
        'typeId': null,
        'itemName': 'identityCode',
        'itemDisplayName': '識別コード',
        'subItemName': null,
        'subItemDisplayName': null,
        'displayItemName': null,
        'itemType': 'input',
        'option':
            '{"readonly": "1", "automatic": true, "maxlength": "30", "ruleUseFlg": "0", "resetTiming": "", "sectionPrivateGroups": "", "autoNumberingRuleList": [], "sectionPrivateEditGroups": ""}',
        'defaultData': '',
        'inputFlg': '0',
        'uneditableFlg': '1',
        'mobileFlg': '1',
        'sectionName': '基本情報',
        'sectionSort': 1.0,
        'positionX': 2,
        'positionY': 0,
        'width': 2,
        'height': 1,
        'sysSetFlg': '1',
        'createdById': '1044758',
        'createdDate': '2024-07-12 18:14:17',
        'modifiedById': '1044787',
        'modifiedDate': '2025-01-07 15:15:48',
        'DBType': null,
        'replaceName': null,
        'columnFlg': null,
        'logContext': null,
        'subItemType': null,
        'assetTypeId': 111111856,
        'itemIds': null,
        'sectionPrivateGroupsPermissionCheckLog': null,
        'sectionPrivateEditGroupsPermissionCheckLog': null,
        'sectionPrivateGroupsPermissionCheckId': null,
        'sectionPrivateEditGroupsPermissionCheckId': null,
        'sectionPrivateGroupsName': null,
        'sectionPrivateEditGroupsName': null,
        'appurtenancesInformationTypeNames': null,
        'masterTypeNames': null
      },
    ],
    'assetMobileSettingForMobile': {'arInfo': null, 'assetInfo': null}
  };

  static Map<String, Object> assetMobileResponse5 = {
    'code': 0,
    'msg': '成功',
    'assetItemList': [],
    'assetMobileSettingForMobile': {
      'arInfo': {
        'category5ItemId': null,
        'category5ItemName': null,
        'category5ItemType': null,
        'category5ItemOption': null,
        'category5SubItemId': null,
        'category5SubItemName': null,
        'category5SubItemType': null,
        'category5SubItemOption': null,
        'category4ItemId': null,
        'category4ItemName': null,
        'category4ItemType': null,
        'category4ItemOption': null,
        'category4SubItemId': null,
        'category4SubItemName': null,
        'category4SubItemType': null,
        'category4SubItemOption': null,
        'category3ItemId': null,
        'category3ItemName': null,
        'category3ItemType': null,
        'category3ItemOption': null,
        'category3SubItemId': null,
        'category3SubItemName': null,
        'category3SubItemType': null,
        'category3SubItemOption': null,
        'category2ItemId': 12492654,
        'category2ItemName': '日付/時間666',
        'category2ItemType': 'date',
        'category2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'category2SubItemId': null,
        'category2SubItemName': null,
        'category2SubItemType': null,
        'category2SubItemOption': null,
        'category1ItemId': 12492685,
        'category1ItemName': '履歴情報合計666',
        'category1ItemType': 'appurInfoSummary',
        'category1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'category1SubItemId': null,
        'category1SubItemName': null,
        'category1SubItemType': null,
        'category1SubItemOption': null,
        'arLevel3BgColor': null,
        'arLevel1ItemId': 12492685,
        'arLevel2ItemId': 12492654,
        'arLevel3ItemId': null,
        'arLevel1ItemName': '履歴情報合計666',
        'arLevel2ItemName': '日付/時間666',
        'arLevel3ItemName': null,
        'arLevel1ItemType': 'appurInfoSummary',
        'arLevel2ItemType': 'date',
        'arLevel3ItemType': null,
        'arLevel1ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'arLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"date\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}",
        'arLevel3ItemOption': null,
        'arLevel1SubItemId': null,
        'arLevel2SubItemId': null,
        'arLevel3SubItemId': null
      },
      'assetInfo': {
        'assetLevel1ItemId': 0,
        'assetLevel2ItemId': 12492661,
        'assetLevel3ItemId': 12493090,
        'assetLevel4ItemId': 12492685,
        'assetLevel5ItemId': 12493078,
        'assetLevel1SubItemId': null,
        'assetLevel2SubItemId': null,
        'assetLevel3SubItemId': null,
        'assetLevel4SubItemId': null,
        'assetLevel5SubItemId': null,
        'assetLevel1ItemType': 'input',
        'assetLevel2ItemType': 'calculate',
        'assetLevel3ItemType': 'calculate',
        'assetLevel4ItemType': 'appurInfoSummary',
        'assetLevel5ItemType': 'date',
        'assetLevel1ItemName': 'assetName',
        'assetLevel2ItemName': '計算666',
        'assetLevel3ItemName': '計算经过日数+1',
        'assetLevel4ItemName': '履歴情報合計666',
        'assetLevel5ItemName': '日付/時間',
        'assetLevel1ItemDisplayName': null,
        'assetLevel2ItemDisplayName': null,
        'assetLevel3ItemDisplayName': null,
        'assetLevel4ItemDisplayName': null,
        'assetLevel5ItemDisplayName': null,
        'assetLevel1ItemOption':
            '{"readonly": "0", "maxlength": "255", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
        'assetLevel2ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+`quantity`\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": \"12\", \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel3ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"unitFlg\": \"0\", \"readonly\": \"0\", \"calculate\": \"`passedTime`+1\", \"percentage\": \"0\", \"calculateType\": \"digital\", \"sectionPrivateGroups\": \"\", \"calculateDecimalPoint\": 0, \"sectionPrivateEditGroups\": \"\", \"calculateCommaDecimalPoint\": \"3\"}",
        'assetLevel4ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"appurInfoSummaryItemId\": 962357, \"sectionPrivateEditGroups\": \"\", \"appurtenancesInformationTypeId\": 13844, \"appurInfoSummaryConditionContent\": []}",
        'assetLevel5ItemOption':
            "{\"check\": \"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証コード\\n    return {\\n      valid: true,\\n      data: {\\n        message:''\\n      }\\n    };\\n  };\", \"dateType\": \"dateTime\", \"readonly\": \"0\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}"
      }
    }
  };

  /// asset_list_controller_test.dart
  static Map<String, Object> loadDataResult = {
    'isMoreThenLimit': false,
    'searchAssetCount': 22,
    'sumAssetCount': 22,
    'isLoading': false,
    'assetUIModelList': [
      {
        'assetDisplayList': [
          {'title': '資産種類', 'content': 'HJL_資産種類_個数(項目名と表示名)'},
          {'title': '資産名', 'content': "022^\$*.[]{}()?-\"!@#%&/\\,><':|_~`"},
          {'title': '識別コード', 'content': '99000000075624'},
          {'title': 'HJL_マスター-(チェックボックス_表示名)', 'content': 'チェックボックス１'},
          {'title': 'HJL_マスター-(メールアドレス_表示名)', 'content': '<EMAIL>'}
        ],
        'imageUrl': null,
        'assetId': 1622814774
      },
      {
        'assetDisplayList': [
          {'title': '資産種類', 'content': 'HJL_資産種類_個数(項目名と表示名)'},
          {'title': '資産名', 'content': '021@#\$%^&'},
          {'title': '識別コード', 'content': '990000000756239900000007562399'},
          {'title': 'HJL_マスター-(チェックボックス_表示名)', 'content': 'チェックボックス１'},
          {'title': 'HJL_マスター-(メールアドレス_表示名)', 'content': '<EMAIL>'}
        ],
        'imageUrl': null,
        'assetId': 1622814771
      },
      {
        'assetDisplayList': [
          {'title': '資産種類', 'content': 'HJL_資産種類_個数(項目名と表示名)'},
          {'title': '資産名', 'content': '017_AIOCR'},
          {'title': '識別コード', 'content': 'w9962520'},
          {'title': 'HJL_マスター-(チェックボックス_表示名)', 'content': 'チェックボックス１'},
          {'title': 'HJL_マスター-(メールアドレス_表示名)', 'content': '<EMAIL>'}
        ],
        'imageUrl': null,
        'assetId': 1622801586
      },
      {
        'assetDisplayList': [
          {'title': '資産種類', 'content': 'HJL_資産種類_個数(項目名と表示名)'},
          {'title': '資産名', 'content': '016_AIOCR'},
          {'title': '識別コード', 'content': 'w9962519'},
          {'title': 'HJL_マスター-(チェックボックス_表示名)', 'content': 'チェックボックス１'},
          {'title': 'HJL_マスター-(メールアドレス_表示名)', 'content': '<EMAIL>'}
        ],
        'imageUrl': null,
        'assetId': 1622801585
      },
      {
        'assetDisplayList': [
          {'title': '資産種類', 'content': 'HJL_資産種類_個数(項目名と表示名)'},
          {'title': '資産名', 'content': '015'},
          {'title': '識別コード', 'content': '99000000062518'},
          {'title': 'HJL_マスター-(チェックボックス_表示名)', 'content': 'チェックボックス１'},
          {'title': 'HJL_マスター-(メールアドレス_表示名)', 'content': '<EMAIL>'}
        ],
        'imageUrl': null,
        'assetId': 1622801584
      },
    ],
    'assetCategoryList': [
      {
        'itemId': 3,
        'itemDisplayName': '資産名',
        'itemName': 'assetName',
        'itemType': 'input',
        'optionObj': null,
        'itemSubName': null,
        'itemSubId': null,
        'itemSubType': null,
        'subOptionObj': null,
        'value': null,
        'selectedCategoryValue': null,
        'subOption': null,
        'itemSubDisplayName': null
      },
      {
        'itemId': 2,
        'itemDisplayName': '識別コード',
        'itemName': 'identityCode',
        'itemType': 'input',
        'optionObj': null,
        'itemSubName': null,
        'itemSubId': null,
        'itemSubType': null,
        'subOptionObj': null,
        'value': null,
        'selectedCategoryValue': null,
        'subOption': null,
        'itemSubDisplayName': null
      },
      {
        'itemId': 12492636,
        'itemDisplayName': 'リスト_表示名',
        'itemName': 'リスト_項目名',
        'itemType': 'list',
        'optionObj': null,
        'itemSubName': null,
        'itemSubId': null,
        'itemSubType': null,
        'subOptionObj': null,
        'value': null,
        'selectedCategoryValue': null,
        'subOption': null,
        'itemSubDisplayName': null
      },
      {
        'itemId': 12492638,
        'itemDisplayName': 'オプションボタン_表示名',
        'itemName': 'オプションボタン_項目名',
        'itemType': 'radio',
        'optionObj': null,
        'itemSubName': null,
        'itemSubId': null,
        'itemSubType': null,
        'subOptionObj': null,
        'value': null,
        'selectedCategoryValue': null,
        'subOption': null,
        'itemSubDisplayName': null
      },
      {
        'itemId': 12492633,
        'itemDisplayName': '1行テキスト_表示名',
        'itemName': '1行テキスト_項目名',
        'itemType': 'input',
        'optionObj': null,
        'itemSubName': null,
        'itemSubId': null,
        'itemSubType': null,
        'subOptionObj': null,
        'value': null,
        'selectedCategoryValue': null,
        'subOption': null,
        'itemSubDisplayName': null
      }
    ]
  };

  static List assetList = [
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': '-10,000.00001'},
        {'title': '通貨_小数点桁数12', 'content': '1,234.0089'},
        {'title': '計算_小数点桁数12', 'content': '-9,999.00001'},
        {'title': 'FXH_Master_All-(数字_小数点桁数12)', 'content': '1,231.4561237'},
        {'title': '資産名', 'content': 'FXH247'}
      ],
      'imageUrl': null,
      'assetId': 1622791192
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH246'}
      ],
      'imageUrl': null,
      'assetId': 1622791191
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH245'}
      ],
      'imageUrl': null,
      'assetId': 1622791190
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH244'}
      ],
      'imageUrl': null,
      'assetId': 1622791189
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH243'}
      ],
      'imageUrl': null,
      'assetId': 1622791188
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH242'}
      ],
      'imageUrl': null,
      'assetId': 1622791187
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH241'}
      ],
      'imageUrl': null,
      'assetId': 1622791186
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH240'}
      ],
      'imageUrl': null,
      'assetId': 1622791185
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH239'}
      ],
      'imageUrl': null,
      'assetId': 1622791184
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH238'}
      ],
      'imageUrl': null,
      'assetId': 1622791183
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH237'}
      ],
      'imageUrl': null,
      'assetId': 1622791182
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH236'}
      ],
      'imageUrl': null,
      'assetId': 1622791181
    },
    {
      'assetDisplayList': [
        {'title': '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12', 'content': ''},
        {'title': '通貨_小数点桁数12', 'content': ''},
        {'title': '計算_小数点桁数12', 'content': ''},
        {'title': '資産名', 'content': 'FXH235'}
      ],
      'imageUrl': null,
      'assetId': 1622791180
    },
  ];

  /// asset_repository_impl_test.dart
  static Map<String, Object> customizeSearchForMobileResponse = {
    'msg': '成功',
    'moreThenLimit': true,
    'code': 0,
    'data': [
      {
        'assetId': 1622791192,
        'externalCode': null,
        'tenantId': '9900004',
        'assetTypeId': 111111855,
        'assetTypeName': null,
        'groupIds': null,
        'assetText':
            '{"RFID": "", "数字": "-10000.00001", "計算": -9999.00001, "通貨": "1234.008900", "location": "東京", "talkList": "", "assetName": "FXH247", "passedTime": 171, "createdDate": "2024/07/19 10:38", "updatedDate": "2025/01/06 16:48", "identityCode": "w52228", "ファイル": [{"url": "9900004/2024/11/28/2024112811555739132066a12e243488ba9a50409c794805b.png", "size": 3126933, "fileName": "Screenshot_20241121-172417.png", "directory": "", "uploadDate": "2024/11/28 10:56", "uploadUserName": "李 暁琳"}], "資産種類": "FXH_個体管理", "FXH_Master_All": {"display": {"962060": "1231.4561237"}, "masterId": 7794626}, "日付/時間666": "2024/11/28 10:55", "1行テキスト666": "oo", "ナンバープレート": ""}',
        'layoutNo': null,
        'createdById': '1044758',
        'createdDate': '2024-07-19 10:38:07',
        'modifiedById': '1044758',
        'modifiedDate': '2025-01-06 16:48:07',
        'state': null,
        'assetName': null,
        'workflowId': 0,
        'barcode': 'w52228',
        'assetLocation': null,
        'relationFlg': false,
        'relationAssetDataList': [],
        'relationAssetIdList': null,
        'dependentRelationAssetIdList': null,
        'message': null,
        'jurisdiction': null,
        'insertAssetRelationColumnData': null,
        'relationNotUpdateFlag': false,
        'rfid': null,
        'copyAppurtenancesInformationTypeIds': null,
        'assetReservationStatusList': null,
        'assetItemList': null,
        'willChangedAmount': null,
        'interactionOperation': null
      },
    ]
  };

  static Map<String, Object> customizeSearchCountForMobileResponse = {
    'msg': '成功',
    'code': 0,
    'sumAssetCount': 1,
    'searchAssetCount': 1
  };
}
