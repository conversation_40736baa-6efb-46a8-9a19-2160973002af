// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_category/presentation/pages/category_search_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i12;
import 'dart:ui' as _i15;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i13;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_count_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/enums/navigation_action_enum.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/mappers/category_mapper.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/mappers/category_parameter_initializer.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/states/asset_category_ui_state.dart'
    as _i6;
import 'package:get/get.dart' as _i9;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i14;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetCategoryCountUseCase_0 extends _i1.SmartFake
    implements _i2.GetCategoryCountUseCase {
  _FakeGetCategoryCountUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCategoryAssembleListUseCase_1 extends _i1.SmartFake
    implements _i3.GetCategoryAssembleListUseCase {
  _FakeGetCategoryAssembleListUseCase_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeGetCategoryAllListUseCase_2 extends _i1.SmartFake
    implements _i4.GetCategoryAllListUseCase {
  _FakeGetCategoryAllListUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetCategoryUIState_4 extends _i1.SmartFake
    implements _i6.AssetCategoryUIState {
  _FakeAssetCategoryUIState_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCategoryParameterInitializer_5 extends _i1.SmartFake
    implements _i7.CategoryParameterInitializer {
  _FakeCategoryParameterInitializer_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeCategoryMapper_6 extends _i1.SmartFake
    implements _i8.CategoryMapper {
  _FakeCategoryMapper_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i9.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CategoryController].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryController extends _i1.Mock
    implements _i10.CategoryController {
  @override
  _i2.GetCategoryCountUseCase get getCategoryCountUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getCategoryCountUseCase),
            returnValue: _FakeGetCategoryCountUseCase_0(
              this,
              Invocation.getter(#getCategoryCountUseCase),
            ),
            returnValueForMissingStub: _FakeGetCategoryCountUseCase_0(
              this,
              Invocation.getter(#getCategoryCountUseCase),
            ),
          )
          as _i2.GetCategoryCountUseCase);

  @override
  _i3.GetCategoryAssembleListUseCase get getCategoryAssembleListUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getCategoryAssembleListUseCase),
            returnValue: _FakeGetCategoryAssembleListUseCase_1(
              this,
              Invocation.getter(#getCategoryAssembleListUseCase),
            ),
            returnValueForMissingStub: _FakeGetCategoryAssembleListUseCase_1(
              this,
              Invocation.getter(#getCategoryAssembleListUseCase),
            ),
          )
          as _i3.GetCategoryAssembleListUseCase);

  @override
  _i4.GetCategoryAllListUseCase get getCategoryAllListUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getCategoryAllListUseCase),
            returnValue: _FakeGetCategoryAllListUseCase_2(
              this,
              Invocation.getter(#getCategoryAllListUseCase),
            ),
            returnValueForMissingStub: _FakeGetCategoryAllListUseCase_2(
              this,
              Invocation.getter(#getCategoryAllListUseCase),
            ),
          )
          as _i4.GetCategoryAllListUseCase);

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i6.AssetCategoryUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeAssetCategoryUIState_4(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeAssetCategoryUIState_4(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i6.AssetCategoryUIState);

  @override
  _i7.CategoryParameterInitializer get parameterInitializer =>
      (super.noSuchMethod(
            Invocation.getter(#parameterInitializer),
            returnValue: _FakeCategoryParameterInitializer_5(
              this,
              Invocation.getter(#parameterInitializer),
            ),
            returnValueForMissingStub: _FakeCategoryParameterInitializer_5(
              this,
              Invocation.getter(#parameterInitializer),
            ),
          )
          as _i7.CategoryParameterInitializer);

  @override
  set parameterInitializer(
    _i7.CategoryParameterInitializer? _parameterInitializer,
  ) => super.noSuchMethod(
    Invocation.setter(#parameterInitializer, _parameterInitializer),
    returnValueForMissingStub: null,
  );

  @override
  _i8.CategoryMapper get categoryMapper =>
      (super.noSuchMethod(
            Invocation.getter(#categoryMapper),
            returnValue: _FakeCategoryMapper_6(
              this,
              Invocation.getter(#categoryMapper),
            ),
            returnValueForMissingStub: _FakeCategoryMapper_6(
              this,
              Invocation.getter(#categoryMapper),
            ),
          )
          as _i8.CategoryMapper);

  @override
  set categoryMapper(_i8.CategoryMapper? _categoryMapper) => super.noSuchMethod(
    Invocation.setter(#categoryMapper, _categoryMapper),
    returnValueForMissingStub: null,
  );

  @override
  _i9.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  _i9.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void backBtnClick() => super.noSuchMethod(
    Invocation.method(#backBtnClick, []),
    returnValueForMissingStub: null,
  );

  @override
  void handleNavigation(_i11.NavigationActionEnum? action, {dynamic payload}) =>
      super.noSuchMethod(
        Invocation.method(#handleNavigation, [action], {#payload: payload}),
        returnValueForMissingStub: null,
      );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i13.ErrorHandlingMode? mode = _i13.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Disposer addListener(_i14.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i14.Disposer);

  @override
  void removeListener(_i15.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i15.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Disposer addListenerId(Object? key, _i14.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i14.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
