// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_type/presentation/pages/asset_type_list_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:ui' as _i11;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_type/domain/usecase/get_asset_type_list_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/controllers/asset_type_controller.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/states/asset_type_list_ui_state.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart'
    as _i8;
import 'package:get/get.dart' as _i5;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetAssetTypeListUseCase_0 extends _i1.SmartFake
    implements _i2.GetAssetTypeListUseCase {
  _FakeGetAssetTypeListUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetTypeListUIState_1 extends _i1.SmartFake
    implements _i3.AssetTypeListUIState {
  _FakeAssetTypeListUIState_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_2 extends _i1.SmartFake
    implements _i4.NavigationService {
  _FakeNavigationService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_3<T> extends _i1.SmartFake
    implements _i5.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AssetTypeController].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetTypeController extends _i1.Mock
    implements _i6.AssetTypeController {
  @override
  _i2.GetAssetTypeListUseCase get getAssetTypeUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getAssetTypeUseCase),
            returnValue: _FakeGetAssetTypeListUseCase_0(
              this,
              Invocation.getter(#getAssetTypeUseCase),
            ),
            returnValueForMissingStub: _FakeGetAssetTypeListUseCase_0(
              this,
              Invocation.getter(#getAssetTypeUseCase),
            ),
          )
          as _i2.GetAssetTypeListUseCase);

  @override
  _i3.AssetTypeListUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeAssetTypeListUIState_1(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeAssetTypeListUIState_1(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i3.AssetTypeListUIState);

  @override
  _i4.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i4.NavigationService);

  @override
  _i5.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_3<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_3<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  _i5.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_3<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_3<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> loadData() =>
      (super.noSuchMethod(
            Invocation.method(#loadData, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> selectAssetType(_i8.AssetTypeListModel? assetType) =>
      (super.noSuchMethod(
            Invocation.method(#selectAssetType, [assetType]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void updateSearch(String? query) => super.noSuchMethod(
    Invocation.method(#updateSearch, [query]),
    returnValueForMissingStub: null,
  );

  @override
  void cancelSelection() => super.noSuchMethod(
    Invocation.method(#cancelSelection, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> qrcodeScan() =>
      (super.noSuchMethod(
            Invocation.method(#qrcodeScan, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> doScanResponseProcess(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
            Invocation.method(#doScanResponseProcess, [data]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i9.ErrorHandlingMode? mode = _i9.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListener(_i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void removeListener(_i11.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i11.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListenerId(Object? key, _i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
