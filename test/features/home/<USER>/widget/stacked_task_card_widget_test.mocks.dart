// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/home/<USER>/widget/stacked_task_card_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i11;
import 'dart:ui' as _i14;

import 'package:asset_force_mobile_v2/core/services/action_scan_service.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_model_and_unread_hint_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_page_list_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/home/<USER>/states/home_ui_state.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart'
    as _i12;
import 'package:flutter/material.dart' as _i16;
import 'package:flutter/services.dart' as _i15;
import 'package:get/get.dart' as _i9;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i13;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetHomeModelAndUnreadHintUseCase_0 extends _i1.SmartFake
    implements _i2.GetHomeModelAndUnreadHintUseCase {
  _FakeGetHomeModelAndUnreadHintUseCase_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeGetHomePageListUseCase_1 extends _i1.SmartFake
    implements _i3.GetHomePageListUseCase {
  _FakeGetHomePageListUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetRoleListUseCase_2 extends _i1.SmartFake
    implements _i4.GetRoleListUseCase {
  _FakeGetRoleListUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTabsController_3 extends _i1.SmartFake
    implements _i5.TabsController {
  _FakeTabsController_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionScanService_4 extends _i1.SmartFake
    implements _i6.ActionScanService {
  _FakeActionScanService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_5 extends _i1.SmartFake
    implements _i7.NavigationService {
  _FakeNavigationService_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHomeUIState_6 extends _i1.SmartFake implements _i8.HomeUIState {
  _FakeHomeUIState_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i9.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MyHomeController].
///
/// See the documentation for Mockito's code generation for more information.
class MockMyHomeController extends _i1.Mock implements _i10.MyHomeController {
  @override
  _i2.GetHomeModelAndUnreadHintUseCase get getHomeModelAndUnreadHintUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getHomeModelAndUnreadHintUseCase),
            returnValue: _FakeGetHomeModelAndUnreadHintUseCase_0(
              this,
              Invocation.getter(#getHomeModelAndUnreadHintUseCase),
            ),
            returnValueForMissingStub: _FakeGetHomeModelAndUnreadHintUseCase_0(
              this,
              Invocation.getter(#getHomeModelAndUnreadHintUseCase),
            ),
          )
          as _i2.GetHomeModelAndUnreadHintUseCase);

  @override
  _i3.GetHomePageListUseCase get getHomePageListUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getHomePageListUseCase),
            returnValue: _FakeGetHomePageListUseCase_1(
              this,
              Invocation.getter(#getHomePageListUseCase),
            ),
            returnValueForMissingStub: _FakeGetHomePageListUseCase_1(
              this,
              Invocation.getter(#getHomePageListUseCase),
            ),
          )
          as _i3.GetHomePageListUseCase);

  @override
  _i4.GetRoleListUseCase get getRoleListUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getRoleListUseCase),
            returnValue: _FakeGetRoleListUseCase_2(
              this,
              Invocation.getter(#getRoleListUseCase),
            ),
            returnValueForMissingStub: _FakeGetRoleListUseCase_2(
              this,
              Invocation.getter(#getRoleListUseCase),
            ),
          )
          as _i4.GetRoleListUseCase);

  @override
  _i5.TabsController get tabsController =>
      (super.noSuchMethod(
            Invocation.getter(#tabsController),
            returnValue: _FakeTabsController_3(
              this,
              Invocation.getter(#tabsController),
            ),
            returnValueForMissingStub: _FakeTabsController_3(
              this,
              Invocation.getter(#tabsController),
            ),
          )
          as _i5.TabsController);

  @override
  _i6.ActionScanService get actionScanService =>
      (super.noSuchMethod(
            Invocation.getter(#actionScanService),
            returnValue: _FakeActionScanService_4(
              this,
              Invocation.getter(#actionScanService),
            ),
            returnValueForMissingStub: _FakeActionScanService_4(
              this,
              Invocation.getter(#actionScanService),
            ),
          )
          as _i6.ActionScanService);

  @override
  _i7.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_5(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_5(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i7.NavigationService);

  @override
  _i8.HomeUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeHomeUIState_6(this, Invocation.getter(#state)),
            returnValueForMissingStub: _FakeHomeUIState_6(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i8.HomeUIState);

  @override
  _i9.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  _i9.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i9.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> loadData() =>
      (super.noSuchMethod(
            Invocation.method(#loadData, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> doRefresh() =>
      (super.noSuchMethod(
            Invocation.method(#doRefresh, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> getHomePageList() =>
      (super.noSuchMethod(
            Invocation.method(#getHomePageList, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> getHomePage() =>
      (super.noSuchMethod(
            Invocation.method(#getHomePage, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void resetHomePageData() => super.noSuchMethod(
    Invocation.method(#resetHomePageData, []),
    returnValueForMissingStub: null,
  );

  @override
  void goMyPage() => super.noSuchMethod(
    Invocation.method(#goMyPage, []),
    returnValueForMissingStub: null,
  );

  @override
  void toMessage() => super.noSuchMethod(
    Invocation.method(#toMessage, []),
    returnValueForMissingStub: null,
  );

  @override
  void onUnApproveProcessClick() => super.noSuchMethod(
    Invocation.method(#onUnApproveProcessClick, []),
    returnValueForMissingStub: null,
  );

  @override
  void onSaveTemporaryProcessClick() => super.noSuchMethod(
    Invocation.method(#onSaveTemporaryProcessClick, []),
    returnValueForMissingStub: null,
  );

  @override
  void onSaveTemporaryActionClick() => super.noSuchMethod(
    Invocation.method(#onSaveTemporaryActionClick, []),
    returnValueForMissingStub: null,
  );

  @override
  void onToNewActionClick(_i12.SharedAssetAction? item) => super.noSuchMethod(
    Invocation.method(#onToNewActionClick, [item]),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListener(_i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i14.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListenerId(Object? key, _i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<bool> didPopRoute() =>
      (super.noSuchMethod(
            Invocation.method(#didPopRoute, []),
            returnValue: _i11.Future<bool>.value(false),
            returnValueForMissingStub: _i11.Future<bool>.value(false),
          )
          as _i11.Future<bool>);

  @override
  bool handleStartBackGesture(_i15.PredictiveBackEvent? backEvent) =>
      (super.noSuchMethod(
            Invocation.method(#handleStartBackGesture, [backEvent]),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void handleUpdateBackGestureProgress(_i15.PredictiveBackEvent? backEvent) =>
      super.noSuchMethod(
        Invocation.method(#handleUpdateBackGestureProgress, [backEvent]),
        returnValueForMissingStub: null,
      );

  @override
  void handleCommitBackGesture() => super.noSuchMethod(
    Invocation.method(#handleCommitBackGesture, []),
    returnValueForMissingStub: null,
  );

  @override
  void handleCancelBackGesture() => super.noSuchMethod(
    Invocation.method(#handleCancelBackGesture, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<bool> didPushRoute(String? route) =>
      (super.noSuchMethod(
            Invocation.method(#didPushRoute, [route]),
            returnValue: _i11.Future<bool>.value(false),
            returnValueForMissingStub: _i11.Future<bool>.value(false),
          )
          as _i11.Future<bool>);

  @override
  _i11.Future<bool> didPushRouteInformation(
    _i16.RouteInformation? routeInformation,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#didPushRouteInformation, [routeInformation]),
            returnValue: _i11.Future<bool>.value(false),
            returnValueForMissingStub: _i11.Future<bool>.value(false),
          )
          as _i11.Future<bool>);

  @override
  void didChangeMetrics() => super.noSuchMethod(
    Invocation.method(#didChangeMetrics, []),
    returnValueForMissingStub: null,
  );

  @override
  void didChangeTextScaleFactor() => super.noSuchMethod(
    Invocation.method(#didChangeTextScaleFactor, []),
    returnValueForMissingStub: null,
  );

  @override
  void didChangePlatformBrightness() => super.noSuchMethod(
    Invocation.method(#didChangePlatformBrightness, []),
    returnValueForMissingStub: null,
  );

  @override
  void didChangeLocales(List<_i14.Locale>? locales) => super.noSuchMethod(
    Invocation.method(#didChangeLocales, [locales]),
    returnValueForMissingStub: null,
  );

  @override
  void didChangeAppLifecycleState(_i14.AppLifecycleState? state) =>
      super.noSuchMethod(
        Invocation.method(#didChangeAppLifecycleState, [state]),
        returnValueForMissingStub: null,
      );

  @override
  void didChangeViewFocus(_i14.ViewFocusEvent? event) => super.noSuchMethod(
    Invocation.method(#didChangeViewFocus, [event]),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<_i14.AppExitResponse> didRequestAppExit() =>
      (super.noSuchMethod(
            Invocation.method(#didRequestAppExit, []),
            returnValue: _i11.Future<_i14.AppExitResponse>.value(
              _i14.AppExitResponse.exit,
            ),
            returnValueForMissingStub: _i11.Future<_i14.AppExitResponse>.value(
              _i14.AppExitResponse.exit,
            ),
          )
          as _i11.Future<_i14.AppExitResponse>);

  @override
  void didHaveMemoryPressure() => super.noSuchMethod(
    Invocation.method(#didHaveMemoryPressure, []),
    returnValueForMissingStub: null,
  );

  @override
  void didChangeAccessibilityFeatures() => super.noSuchMethod(
    Invocation.method(#didChangeAccessibilityFeatures, []),
    returnValueForMissingStub: null,
  );
}
