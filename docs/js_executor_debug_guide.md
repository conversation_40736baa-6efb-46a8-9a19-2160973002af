# JavaScript 执行器调试指南

## 概述

JavaScript 执行器现在支持强大的调试功能，可以帮助开发者更容易地调试和监控 JavaScript 代码的执行。

## 主要功能

### 1. 自动环境配置

调试配置会根据当前环境自动设置：

- **开发环境 (dev/qa)**: 启用所有调试功能
- **预发布环境 (stg)**: 启用基础调试功能
- **生产环境 (prod)**: 禁用调试功能

### 2. WebView 调试检查器

在开发和测试环境下，WebView 的 `isInspectable` 属性会自动设置为 `true`，允许使用浏览器开发者工具进行调试。

### 3. 详细日志记录

- JavaScript 代码转换过程的详细日志
- 控制台消息的分级处理
- 执行过程的详细跟踪

### 4. 性能监控

- 自动记录 JavaScript 执行时间
- 统计不同代码类型的性能指标
- 提供详细的性能报告

### 5. 增强错误报告

- 详细的错误上下文信息
- JavaScript 代码片段记录
- 执行状态快照

## 使用方法

### 基本使用

```dart
// 创建 JavaScript 执行器
final jsExecutor = JsExecutor(bridgeService);

// 执行 JavaScript 代码
final result = await jsExecutor.eval(
  'console.log("Hello World");',
  JsCodeLocal.customizedLogicJavascript,
);
```

### 手动控制调试功能

```dart
// 启用详细日志
jsExecutor.enableVerboseLogging(true);

// 启用性能监控
jsExecutor.enablePerformanceMonitoring(true);

// 启用详细错误报告
jsExecutor.enableDetailedErrorReporting(true);

// 启用 WebView 调试检查器（需要重新创建 WebView）
jsExecutor.enableWebViewDebug(true);
```

### 查看调试信息

```dart
// 获取当前调试配置状态
final debugConfig = jsExecutor.getDebugConfigStatus();
print('调试配置: $debugConfig');

// 打印性能统计报告
jsExecutor.printPerformanceReport();

// 获取详细的性能数据
final performanceReport = jsExecutor.getPerformanceReport();
```

### 重置和管理

```dart
// 重置性能统计
jsExecutor.resetPerformanceStats();

// 重新初始化调试配置
jsExecutor.reinitializeDebugConfig();
```

## 调试配置类 (JsDebugConfig)

### 静态方法

- `initialize()`: 根据环境初始化调试配置
- `enableWebViewInspector(bool enabled)`: 控制 WebView 调试检查器
- `enableVerboseLogging(bool enabled)`: 控制详细日志
- `enablePerformanceMonitoring(bool enabled)`: 控制性能监控
- `enableDetailedErrorReporting(bool enabled)`: 控制详细错误报告

### 获取状态

- `isWebViewInspectable`: WebView 调试检查器状态
- `isVerboseLoggingEnabled`: 详细日志状态
- `isPerformanceMonitoringEnabled`: 性能监控状态
- `isDetailedErrorReportingEnabled`: 详细错误报告状态
- `getDebugConfigSummary()`: 获取所有配置的摘要

## 性能监控

### 自动监控

性能监控会自动记录每次 JavaScript 执行的时间，并按代码类型分类统计：

- 执行次数
- 总执行时间
- 平均执行时间
- 最短/最长执行时间
- 最后一次执行时间

### 性能报告示例

```
JsExecutor: ========== 性能统计报告 ==========
JsExecutor: [customizedLogicJavascript] 执行次数: 15, 平均耗时: 23.45ms, 最短: 12ms, 最长: 45ms
JsExecutor: [calculateJavascript] 执行次数: 8, 平均耗时: 15.67ms, 最短: 8ms, 最长: 28ms
JsExecutor: =====================================
```

## 错误处理增强

### 基础错误信息

在所有环境下都会记录基本的错误信息。

### 详细错误信息

在启用详细错误报告时，会记录：

- JavaScript 代码类型
- 代码片段（前200字符）
- 执行参数
- 错误类型
- 执行状态快照

## 控制台消息处理

### 分级处理

- **ERROR**: 始终记录为错误日志
- **WARNING**: 始终记录为警告日志
- **其他级别**: 仅在详细日志模式下记录

### 详细信息

在详细错误报告模式下，ERROR 和 WARNING 级别的消息会记录额外的上下文信息。

## 最佳实践

1. **开发阶段**: 启用所有调试功能，便于问题定位
2. **测试阶段**: 启用性能监控，关注性能指标
3. **生产环境**: 依赖自动配置，避免手动启用调试功能
4. **性能优化**: 定期查看性能报告，识别性能瓶颈
5. **错误排查**: 启用详细错误报告，获取完整的错误上下文

## 注意事项

1. WebView 调试检查器的设置需要重新创建 WebView 才能生效
2. 详细日志会增加日志输出量，可能影响性能
3. 性能监控数据会累积，建议定期重置
4. 在生产环境中避免启用详细调试功能，以免影响性能和安全性
